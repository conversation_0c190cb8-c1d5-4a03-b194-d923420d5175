/**
 * Session Management Service using Cloudflare KV
 * Handles user sessions, cart persistence, authentication state, and user preferences
 */

export interface SessionData {
  id: string;
  user_id?: string;
  customer_id?: string;
  cart_id?: string;
  email?: string;
  is_authenticated: boolean;
  preferences: {
    language: string;
    currency: string;
    region: string;
    theme?: string;
  };
  activity: {
    last_seen: string;
    ip_address?: string;
    user_agent?: string;
    pages_visited: number;
    time_spent: number; // in seconds
  };
  // PERFORMANCE: Removed cart_data to reduce session size and KV operations
  // Cart data is now retrieved directly from database when needed
  authentication?: {
    login_time: string;
    login_method: 'email' | 'guest';
    role: 'customer' | 'admin' | 'guest';
  };
  tracking: {
    utm_source?: string;
    utm_medium?: string;
    utm_campaign?: string;
    referrer?: string;
    landing_page?: string;
  };
  flags: {
    newsletter_popup_shown: boolean;
    first_visit: boolean;
    returning_customer: boolean;
    high_value_customer: boolean;
  };
  created_at: string;
  updated_at: string;
  expires_at: string;
}

export class SessionService {
  private env: any;
  private defaultTTL: number;

  constructor(env: any) {
    this.env = env;
    this.defaultTTL = 30 * 24 * 60 * 60; // 30 days default
  }

  /**
   * Generate a new session ID
   */
  private generateSessionId(): string {
    return `sess_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
  }

  /**
   * Create a new session
   */
  async createSession(data: Partial<SessionData> = {}): Promise<SessionData> {
    const sessionId = this.generateSessionId();
    const now = new Date().toISOString();
    const expiresAt = new Date(Date.now() + this.defaultTTL * 1000).toISOString();

    const session: SessionData = {
      id: sessionId,
      is_authenticated: false,
      preferences: {
        language: data.preferences?.language || 'ro',
        currency: data.preferences?.currency || 'RON',
        region: data.preferences?.region || 'RO',
        theme: data.preferences?.theme || 'light'
      },
      activity: {
        last_seen: now,
        ip_address: data.activity?.ip_address,
        user_agent: data.activity?.user_agent,
        pages_visited: 1,
        time_spent: 0
      },
      tracking: {
        utm_source: data.tracking?.utm_source,
        utm_medium: data.tracking?.utm_medium,
        utm_campaign: data.tracking?.utm_campaign,
        referrer: data.tracking?.referrer,
        landing_page: data.tracking?.landing_page
      },
      flags: {
        newsletter_popup_shown: false,
        first_visit: true,
        returning_customer: false,
        high_value_customer: false
      },
      created_at: now,
      updated_at: now,
      expires_at: expiresAt,
      ...data
    };

    await this.env.SESSIONS.put(sessionId, JSON.stringify(session), {
      expirationTtl: this.defaultTTL
    });

    console.log(`Created new session: ${sessionId}`);
    return session;
  }

  /**
   * Get session by ID
   */
  async getSession(sessionId: string): Promise<SessionData | null> {
    try {
      const sessionData = await this.env.SESSIONS.get(sessionId, { type: 'json' });
      if (!sessionData) return null;

      const session = sessionData as SessionData;
      
      // Check if session has expired
      if (new Date(session.expires_at) < new Date()) {
        await this.deleteSession(sessionId);
        return null;
      }

      return session;
    } catch (error) {
      console.error('Error getting session:', error);
      return null;
    }
  }

  /**
   * Update session data
   */
  async updateSession(sessionId: string, updates: Partial<SessionData>): Promise<SessionData | null> {
    const existingSession = await this.getSession(sessionId);
    if (!existingSession) return null;

    const updatedSession: SessionData = {
      ...existingSession,
      ...updates,
      updated_at: new Date().toISOString()
    };

    // Deep merge nested objects
    if (updates.preferences) {
      updatedSession.preferences = { ...existingSession.preferences, ...updates.preferences };
    }
    if (updates.activity) {
      updatedSession.activity = { ...existingSession.activity, ...updates.activity };
    }
    // PERFORMANCE: Removed cart_data handling to reduce session complexity
    if (updates.authentication) {
      updatedSession.authentication = { ...existingSession.authentication, ...updates.authentication };
    }
    if (updates.tracking) {
      updatedSession.tracking = { ...existingSession.tracking, ...updates.tracking };
    }
    if (updates.flags) {
      updatedSession.flags = { ...existingSession.flags, ...updates.flags };
    }

    await this.env.SESSIONS.put(sessionId, JSON.stringify(updatedSession), {
      expirationTtl: this.defaultTTL
    });

    return updatedSession;
  }

  /**
   * Touch session (update last seen and extend TTL)
   */
  async touchSession(sessionId: string, additionalData: Partial<SessionData> = {}): Promise<SessionData | null> {
    const session = await this.getSession(sessionId);
    if (!session) return null;

    const updates: Partial<SessionData> = {
      activity: {
        ...session.activity,
        last_seen: new Date().toISOString(),
        pages_visited: session.activity.pages_visited + 1,
        ...additionalData.activity
      },
      ...additionalData
    };

    return this.updateSession(sessionId, updates);
  }

  /**
   * Associate session with user authentication
   */
  async authenticateSession(sessionId: string, userData: {
    user_id?: string;
    customer_id?: string;
    email?: string;
    role?: 'customer' | 'admin';
    login_method?: 'email' | 'guest';
  }): Promise<SessionData | null> {
    const session = await this.getSession(sessionId);
    if (!session) return null;

    const updates: Partial<SessionData> = {
      user_id: userData.user_id,
      customer_id: userData.customer_id,
      email: userData.email,
      is_authenticated: true,
      authentication: {
        login_time: new Date().toISOString(),
        login_method: userData.login_method || 'email',
        role: userData.role || 'customer'
      },
      flags: {
        ...session.flags,
        returning_customer: true
      }
    };

    return this.updateSession(sessionId, updates);
  }

  /**
   * PERFORMANCE: Simplified cart association - only store cart ID
   * Cart data is retrieved from database when needed to reduce session size
   */
  async associateCart(sessionId: string, cartId: string): Promise<SessionData | null> {
    const updates: Partial<SessionData> = {
      cart_id: cartId
    };

    return this.updateSession(sessionId, updates);
  }
  /**
   * Update user preferences
   */
  async updatePreferences(sessionId: string, preferences: Partial<SessionData['preferences']>): Promise<SessionData | null> {
    const session = await this.getSession(sessionId);
    if (!session) return null;

    return this.updateSession(sessionId, { 
      preferences: { ...session.preferences, ...preferences }
    });
  }

  /**
   * Mark user as high value customer
   */
  async markHighValueCustomer(sessionId: string): Promise<SessionData | null> {
    const session = await this.getSession(sessionId);
    if (!session) return null;

    return this.updateSession(sessionId, {
      flags: { ...session.flags, high_value_customer: true }
    });
  }

  /**
   * Update session flags
   */
  async updateFlags(sessionId: string, flags: Partial<SessionData['flags']>): Promise<SessionData | null> {
    const session = await this.getSession(sessionId);
    if (!session) return null;

    return this.updateSession(sessionId, { 
      flags: { ...session.flags, ...flags }
    });
  }

  /**
   * Delete session
   */
  async deleteSession(sessionId: string): Promise<boolean> {
    try {
      await this.env.SESSIONS.delete(sessionId);
      console.log(`Deleted session: ${sessionId}`);
      return true;
    } catch (error) {
      console.error('Error deleting session:', error);
      return false;
    }
  }

  /**
   * Get sessions by user ID
   */
  async getSessionsByUserId(userId: string): Promise<SessionData[]> {
    try {
      const list = await this.env.SESSIONS.list();
      const sessions: SessionData[] = [];

      for (const key of list.keys) {
        const sessionData = await this.env.SESSIONS.get(key.name, { type: 'json' });
        if (sessionData && (sessionData as SessionData).user_id === userId) {
          sessions.push(sessionData as SessionData);
        }
      }

      return sessions;
    } catch (error) {
      console.error('Error getting sessions by user ID:', error);
      return [];
    }
  }

  /**
   * Clean up expired sessions
   */
  async cleanupExpiredSessions(): Promise<number> {
    try {
      const list = await this.env.SESSIONS.list();
      let deletedCount = 0;
      const now = new Date();

      for (const key of list.keys) {
        const sessionData = await this.env.SESSIONS.get(key.name, { type: 'json' });
        if (sessionData) {
          const session = sessionData as SessionData;
          if (new Date(session.expires_at) < now) {
            await this.env.SESSIONS.delete(key.name);
            deletedCount++;
          }
        }
      }

      console.log(`Cleaned up ${deletedCount} expired sessions`);
      return deletedCount;
    } catch (error) {
      console.error('Error cleaning up expired sessions:', error);
      return 0;
    }
  }

  /**
   * Get session analytics
   */
  async getSessionAnalytics(): Promise<{
    total_sessions: number;
    authenticated_sessions: number;
    guest_sessions: number;
    sessions_with_carts: number;
    average_pages_visited: number;
    top_languages: Record<string, number>;
    top_referrers: Record<string, number>;
  }> {
    try {
      const list = await this.env.SESSIONS.list();
      let totalSessions = 0;
      let authenticatedSessions = 0;
      let guestSessions = 0;
      let sessionsWithCarts = 0;
      let totalPagesVisited = 0;
      const languages: Record<string, number> = {};
      const referrers: Record<string, number> = {};

      for (const key of list.keys) {
        const sessionData = await this.env.SESSIONS.get(key.name, { type: 'json' });
        if (sessionData) {
          const session = sessionData as SessionData;
          
          // Skip expired sessions
          if (new Date(session.expires_at) < new Date()) continue;
          
          totalSessions++;
          totalPagesVisited += session.activity.pages_visited;
          
          if (session.is_authenticated) {
            authenticatedSessions++;
          } else {
            guestSessions++;
          }
          
          if (session.cart_id) {
            sessionsWithCarts++;
          }
          
          // Count languages
          const lang = session.preferences.language;
          languages[lang] = (languages[lang] || 0) + 1;
          
          // Count referrers
          if (session.tracking.referrer) {
            const ref = session.tracking.referrer;
            referrers[ref] = (referrers[ref] || 0) + 1;
          }
        }
      }

      return {
        total_sessions: totalSessions,
        authenticated_sessions: authenticatedSessions,
        guest_sessions: guestSessions,
        sessions_with_carts: sessionsWithCarts,
        average_pages_visited: totalSessions > 0 ? totalPagesVisited / totalSessions : 0,
        top_languages: languages,
        top_referrers: referrers
      };
    } catch (error) {
      console.error('Error getting session analytics:', error);
      return {
        total_sessions: 0,
        authenticated_sessions: 0,
        guest_sessions: 0,
        sessions_with_carts: 0,
        average_pages_visited: 0,
        top_languages: {},
        top_referrers: {}
      };
    }
  }

  /**
   * Extend session TTL
   */
  async extendSession(sessionId: string, additionalSeconds: number = this.defaultTTL): Promise<boolean> {
    const session = await this.getSession(sessionId);
    if (!session) return false;

    const newExpiresAt = new Date(Date.now() + additionalSeconds * 1000).toISOString();
    const updatedSession = {
      ...session,
      expires_at: newExpiresAt,
      updated_at: new Date().toISOString()
    };

    await this.env.SESSIONS.put(sessionId, JSON.stringify(updatedSession), {
      expirationTtl: additionalSeconds
    });

    return true;
  }
}

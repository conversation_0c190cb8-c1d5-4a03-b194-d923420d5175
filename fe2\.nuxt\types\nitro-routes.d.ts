// Generated by nitro
import type { Serialize, Simplify } from "nitropack/types";
declare module "nitropack/types" {
  type Awaited<T> = T extends PromiseLike<infer U> ? Awaited<U> : T
  interface InternalApi {
    '/api/auth/**': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/auth/[...]').default>>>>
    }
    '/api/auth/cookie-fix': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/auth/cookie-fix').default>>>>
    }
    '/api/auth/current-user': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/auth/current-user').default>>>>
    }
    '/api/auth/debug': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/auth/debug').default>>>>
    }
    '/api/auth/login': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/auth/login').default>>>>
    }
    '/api/auth/signin': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/auth/signin').default>>>>
    }
    '/api/auth/signout': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/auth/signout').default>>>>
    }
    '/api/auth/sync-medusa': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/auth/sync-medusa').default>>>>
    }
    '/api/contact': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/contact').default>>>>
    }
    '/api/customers/addresses': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/customers/addresses').default>>>>
    }
    '/api/customers/addresses/:id': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/customers/addresses/[id]').default>>>>
    }
    '/api/customers/me': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/customers/me').default>>>>
    }
    '/api/customers/password': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/customers/password').default>>>>
    }
    '/api/newsletter/subscribe': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/newsletter/subscribe.post').default>>>>
    }
    '/api/newsletter/unsubscribe': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/newsletter/unsubscribe.post').default>>>>
    }
    '/api/orders/:id': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/orders/[id]').default>>>>
    }
    '/api/orders/:id/update-customer': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/orders/[id]/update-customer').default>>>>
    }
    '/api/orders': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/orders/index').default>>>>
    }
    '/api/products/:id': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/products/[id]').default>>>>
    }
    '/api/products/prices': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/products/prices').default>>>>
    }
    '/api/reviews/:productId': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/reviews/[productId].get').default>>>>
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/reviews/[productId].post').default>>>>
    }
    '/api/reviews/ratings': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/reviews/ratings.get').default>>>>
    }
    '/__nuxt_error': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../node_modules/nuxt/dist/core/runtime/nitro/handlers/renderer').default>>>>
    }
    '/api/_nuxt_icon/:collection': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../node_modules/@nuxt/icon/dist/runtime/server/api').default>>>>
    }
    '/__nuxt_island/**': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/#internal/nuxt/island-renderer').default>>>>
    }
    '/_ipx/**': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../node_modules/@nuxt/image/dist/runtime/ipx').default>>>>
    }
  }
}
export {}
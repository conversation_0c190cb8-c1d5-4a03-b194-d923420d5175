// Local type definition to avoid dependency issues
interface WorkerEnv {
  DB: any;
  CACHE: any;
  SESSIONS: any;
  ASSETS: any;
  UPLOADS: any;
  AI: any;
  JWT_SECRET: string;
  STRIPE_SECRET_KEY: string;
  STRIPE_WEBHOOK_SECRET: string;
  RESEND_API_KEY: string;
  TYPESENSE_API_KEY: string;
  TRUSTED_SHOP_API_KEY: string;
  ENVIRONMENT: "development" | "staging" | "production";
  FRONTEND_URL: string;
  ADMIN_URL: string;
}

export interface CacheOptions {
  ttl?: number; // Time to live in seconds
  metadata?: Record<string, any>;
}

export class CacheService {
  private env: WorkerEnv;
  private defaultTTL: number;

  constructor(env: WorkerEnv) {
    this.env = env;
    this.defaultTTL = 3600; // 1 hour default
  }

  async get<T>(key: string): Promise<T | null> {
    try {
      const value = await this.env.CACHE.get(key, { type: 'json' });
      return value as T;
    } catch (error) {
      console.error('Cache get error:', error);
      return null;
    }
  }

  async set<T>(key: string, value: T, options: CacheOptions = {}): Promise<boolean> {
    try {
      const ttl = options.ttl || this.defaultTTL;
      
      await this.env.CACHE.put(key, JSON.stringify(value), {
        expirationTtl: ttl,
        metadata: options.metadata,
      });
      
      return true;
    } catch (error) {
      console.error('Cache set error:', error);
      return false;
    }
  }

  async delete(key: string): Promise<boolean> {
    try {
      await this.env.CACHE.delete(key);
      return true;
    } catch (error) {
      console.error('Cache delete error:', error);
      return false;
    }
  }

  async exists(key: string): Promise<boolean> {
    try {
      const value = await this.env.CACHE.get(key);
      return value !== null;
    } catch (error) {
      console.error('Cache exists error:', error);
      return false;
    }
  }

  async invalidatePattern(pattern: string): Promise<number> {
    try {
      const list = await this.env.CACHE.list({ prefix: pattern });
      let deleted = 0;
      
      for (const key of list.keys) {
        await this.env.CACHE.delete(key.name);
        deleted++;
      }
      
      return deleted;
    } catch (error) {
      console.error('Cache invalidate pattern error:', error);
      return 0;
    }
  }

  // Product-specific cache methods
  async getProduct(productId: string): Promise<any | null> {
    return this.get(`product:${productId}`);
  }

  async setProduct(productId: string, product: any, ttl: number = 1800): Promise<boolean> {
    return this.set(`product:${productId}`, product, { ttl });
  }

  async invalidateProduct(productId: string): Promise<boolean> {
    const deleted = await this.invalidatePattern(`product:${productId}`);
    // Also invalidate related caches
    await this.invalidatePattern('products:list');
    await this.invalidatePattern('search:');
    return deleted > 0;
  }

  // Collection-specific cache methods
  async getCollection(collectionId: string): Promise<any | null> {
    return this.get(`collection:${collectionId}`);
  }

  async setCollection(collectionId: string, collection: any, ttl: number = 3600): Promise<boolean> {
    return this.set(`collection:${collectionId}`, collection, { ttl });
  }

  async invalidateCollection(collectionId: string): Promise<boolean> {
    const deleted = await this.invalidatePattern(`collection:${collectionId}`);
    await this.invalidatePattern('collections:list');
    return deleted > 0;
  }

  // Search cache methods
  async getSearchResults(query: string, filters: Record<string, any> = {}): Promise<any | null> {
    const cacheKey = this.generateSearchKey(query, filters);
    return this.get(cacheKey);
  }

  async setSearchResults(
    query: string, 
    filters: Record<string, any> = {}, 
    results: any, 
    ttl: number = 900
  ): Promise<boolean> {
    const cacheKey = this.generateSearchKey(query, filters);
    return this.set(cacheKey, results, { ttl });
  }

  private generateSearchKey(query: string, filters: Record<string, any>): string {
    const filterString = Object.keys(filters)
      .sort()
      .map(key => `${key}:${filters[key]}`)
      .join('|');
    
    return `search:${encodeURIComponent(query)}:${encodeURIComponent(filterString)}`;
  }

  // Cart cache methods
  async getCart(cartId: string): Promise<any | null> {
    return this.get(`cart:${cartId}`);
  }

  async setCart(cartId: string, cart: any, ttl: number = 1800): Promise<boolean> {
    return this.set(`cart:${cartId}`, cart, { ttl });
  }

  async invalidateCart(cartId: string): Promise<boolean> {
    return this.delete(`cart:${cartId}`);
  }

  // Customer cache methods
  async getCustomer(customerId: string): Promise<any | null> {
    return this.get(`customer:${customerId}`);
  }

  async setCustomer(customerId: string, customer: any, ttl: number = 3600): Promise<boolean> {
    return this.set(`customer:${customerId}`, customer, { ttl });
  }

  async invalidateCustomer(customerId: string): Promise<boolean> {
    return this.delete(`customer:${customerId}`);
  }

  // Order cache methods
  async getOrder(orderId: string): Promise<any | null> {
    return this.get(`order:${orderId}`);
  }

  async setOrder(orderId: string, order: any, ttl: number = 7200): Promise<boolean> {
    return this.set(`order:${orderId}`, order, { ttl });
  }

  async invalidateOrder(orderId: string): Promise<boolean> {
    return this.delete(`order:${orderId}`);
  }

  // Journal cache methods
  async getJournalEntry(entryId: string): Promise<any | null> {
    return this.get(`journal:${entryId}`);
  }

  async setJournalEntry(entryId: string, entry: any, ttl: number = 7200): Promise<boolean> {
    return this.set(`journal:${entryId}`, entry, { ttl });
  }

  async getJournalBySlug(slug: string): Promise<any | null> {
    return this.get(`journal:slug:${slug}`);
  }

  async setJournalBySlug(slug: string, entry: any, ttl: number = 7200): Promise<boolean> {
    return this.set(`journal:slug:${slug}`, entry, { ttl });
  }

  async invalidateJournal(entryId: string, slug?: string): Promise<boolean> {
    let deleted = await this.delete(`journal:${entryId}`);
    if (slug) {
      deleted = (await this.delete(`journal:slug:${slug}`)) || deleted;
    }
    await this.invalidatePattern('journal:list');
    return deleted;
  }

  // List cache methods
  async getProductsList(page: number, limit: number, filters: Record<string, any> = {}): Promise<any | null> {
    const filterString = Object.keys(filters)
      .sort()
      .map(key => `${key}:${filters[key]}`)
      .join('|');
    
    return this.get(`products:list:${page}:${limit}:${encodeURIComponent(filterString)}`);
  }

  async setProductsList(
    page: number, 
    limit: number, 
    filters: Record<string, any> = {}, 
    products: any, 
    ttl: number = 1800
  ): Promise<boolean> {
    const filterString = Object.keys(filters)
      .sort()
      .map(key => `${key}:${filters[key]}`)
      .join('|');
    
    return this.set(`products:list:${page}:${limit}:${encodeURIComponent(filterString)}`, products, { ttl });
  }

  // Dashboard stats cache
  async getDashboardStats(): Promise<any | null> {
    return this.get('dashboard:stats');
  }

  async setDashboardStats(stats: any, ttl: number = 300): Promise<boolean> {
    return this.set('dashboard:stats', stats, { ttl });
  }

  async invalidateDashboardStats(): Promise<boolean> {
    return this.delete('dashboard:stats');
  }
  // Payment methods cache
  async getPaymentMethods(): Promise<any[] | null> {
    return this.get('payment_methods:all');
  }

  async setPaymentMethods(methods: any[], ttl: number = 1800): Promise<boolean> {
    return this.set('payment_methods:all', methods, { ttl });
  }

  async invalidatePaymentMethods(): Promise<boolean> {
    return this.delete('payment_methods:all');
  }

  // Shipping methods cache
  async getShippingMethods(region?: string): Promise<any[] | null> {
    const key = region ? `shipping_methods:${region}` : 'shipping_methods:all';
    return this.get(key);
  }

  async setShippingMethods(methods: any[], region?: string, ttl: number = 3600): Promise<boolean> {
    const key = region ? `shipping_methods:${region}` : 'shipping_methods:all';
    return this.set(key, methods, { ttl });
  }

  async invalidateShippingMethods(region?: string): Promise<boolean> {
    if (region) {
      return this.delete(`shipping_methods:${region}`);
    } else {
      return await this.invalidatePattern('shipping_methods:') > 0;
    }
  }

  // Product variants cache
  async getProductVariants(productId: string): Promise<any[] | null> {
    return this.get(`product_variants:${productId}`);
  }

  async setProductVariants(productId: string, variants: any[], ttl: number = 1800): Promise<boolean> {
    return this.set(`product_variants:${productId}`, variants, { ttl });
  }

  async invalidateProductVariants(productId: string): Promise<boolean> {
    return this.delete(`product_variants:${productId}`);
  }

  // Popular products cache
  async getPopularProducts(limit: number = 10): Promise<any[] | null> {
    return this.get(`popular_products:${limit}`);
  }

  async setPopularProducts(products: any[], limit: number = 10, ttl: number = 3600): Promise<boolean> {
    return this.set(`popular_products:${limit}`, products, { ttl });
  }

  async invalidatePopularProducts(): Promise<boolean> {
    return await this.invalidatePattern('popular_products:') > 0;
  }

  // Featured collections cache
  async getFeaturedCollections(): Promise<any[] | null> {
    return this.get('featured_collections');
  }

  async setFeaturedCollections(collections: any[], ttl: number = 3600): Promise<boolean> {
    return this.set('featured_collections', collections, { ttl });
  }

  async invalidateFeaturedCollections(): Promise<boolean> {
    return this.delete('featured_collections');
  }

  // Regional pricing cache
  async getRegionalPricing(productId: string, regionId: string): Promise<any | null> {
    return this.get(`pricing:${productId}:${regionId}`);
  }

  async setRegionalPricing(productId: string, regionId: string, pricing: any, ttl: number = 1800): Promise<boolean> {
    return this.set(`pricing:${productId}:${regionId}`, pricing, { ttl });
  }

  async invalidateRegionalPricing(productId?: string, regionId?: string): Promise<boolean> {
    if (productId && regionId) {
      return this.delete(`pricing:${productId}:${regionId}`);
    } else if (productId) {
      return await this.invalidatePattern(`pricing:${productId}:`) > 0;
    } else {
      return await this.invalidatePattern('pricing:') > 0;
    }
  }

  // Cart totals cache (for complex calculations)
  async getCartTotals(cartId: string): Promise<any | null> {
    return this.get(`cart_totals:${cartId}`);
  }

  async setCartTotals(cartId: string, totals: any, ttl: number = 300): Promise<boolean> {
    return this.set(`cart_totals:${cartId}`, totals, { ttl });
  }

  async invalidateCartTotals(cartId: string): Promise<boolean> {
    return this.delete(`cart_totals:${cartId}`);
  }

  // Homepage data cache
  async getHomepageData(): Promise<any | null> {
    return this.get('homepage_data');
  }

  async setHomepageData(data: any, ttl: number = 1800): Promise<boolean> {
    return this.set('homepage_data', data, { ttl });
  }

  async invalidateHomepageData(): Promise<boolean> {
    return this.delete('homepage_data');
  }

  // Utility methods
  async warmupCache(): Promise<void> {
    // Pre-populate frequently accessed data
    console.log('Starting cache warmup...');
    
    // This would typically fetch and cache:
    // - Popular products
    // - Featured collections
    // - Recent journal entries
    // - Dashboard stats
    // - Payment methods
    // - Shipping methods
    // - Homepage data
    
    console.log('Cache warmup completed');
  }

  async getCacheStats(): Promise<{ keys: number; size: string }> {
    try {
      const list = await this.env.CACHE.list();
      return {
        keys: list.keys.length,
        size: 'Unknown', // KV doesn't provide size info
      };
    } catch (error) {
      console.error('Failed to get cache stats:', error);
      return { keys: 0, size: 'Unknown' };
    }
  }

  async clearAllCache(): Promise<number> {
    try {
      const list = await this.env.CACHE.list();
      let deleted = 0;
      
      for (const key of list.keys) {
        await this.env.CACHE.delete(key.name);
        deleted++;
      }
      
      return deleted;
    } catch (error) {
      console.error('Failed to clear cache:', error);
      return 0;
    }
  }
} 
import { Context } from 'hono';

export class DatabaseSessionService {
  private session: D1DatabaseSession | null = null;
  private bookmark: string | null = null;
  private db: D1Database;
  private context: Context;
  private useSimpleMode: boolean = true; // PERFORMANCE: Enable simple mode by default

  constructor(db: D1Database, context: Context, options: { useSimpleMode?: boolean } = {}) {
    this.db = db;
    this.context = context;
    this.useSimpleMode = options.useSimpleMode ?? true;

    if (!this.useSimpleMode) {
      this.initializeSession();
    }
  }

  /**
   * Initialize session based on request headers and operation type
   */
  private initializeSession(): void {
    // Get bookmark from request headers if available
    const requestBookmark = this.context.req.header('x-d1-bookmark');
    
    // For write operations or when fresh data is required, use primary
    const method = this.context.req.method;
    const isWriteOperation = ['POST', 'PUT', 'DELETE', 'PATCH'].includes(method);
    
    if (isWriteOperation) {
      // Use primary database for write operations
      this.session = this.db.withSession('first-primary');
      console.log('🔄 DB SESSION: Using primary database for write operation');
    } else if (requestBookmark) {
      // Continue from previous session using bookmark
      this.session = this.db.withSession(requestBookmark);
      this.bookmark = requestBookmark;
      console.log('🔄 DB SESSION: Continuing from bookmark:', requestBookmark.substring(0, 20) + '...');
    } else {
      // Use any available replica for read operations
      this.session = this.db.withSession(); // equivalent to 'first-unconstrained'
      console.log('🔄 DB SESSION: Using read replica for read operation');
    }
  }

  /**
   * Get the current session for database operations
   * PERFORMANCE: Simplified to use direct database access in simple mode
   */
  getSession(): D1DatabaseSession {
    if (this.useSimpleMode) {
      // Simple mode: use database directly without session complexity
      return this.db as any; // Cast to session type for compatibility
    }

    if (!this.session) {
      throw new Error('Database session not initialized');
    }
    return this.session;
  }

  /**
   * Get a fresh primary session for operations requiring latest data
   */
  getPrimarySession(): D1DatabaseSession {
    const primarySession = this.db.withSession('first-primary');
    console.log('🔄 DB SESSION: Created fresh primary session');
    return primarySession;
  }

  /**
   * Get current bookmark and optionally set it in response headers
   */
  getCurrentBookmark(): string | null {
    if (this.session && 'getBookmark' in this.session) {
      this.bookmark = (this.session as any).getBookmark() || null;
    }
    return this.bookmark;
  }

  /**
   * Set bookmark in response headers for session continuity
   * PERFORMANCE: Skip bookmark handling in simple mode
   */
  setBookmarkHeader(): void {
    if (this.useSimpleMode) {
      return; // Skip bookmark handling in simple mode for better performance
    }

    const bookmark = this.getCurrentBookmark();
    if (bookmark) {
      // Set bookmark in response headers for client to use in next request
      this.context.header('x-d1-bookmark', bookmark);
      console.log('🔄 DB SESSION: Setting bookmark in response headers');
    }
  }

  /**
   * Log database routing information from result meta
   */
  logDatabaseRouting(result: any, queryName?: string): void {
    if (result && typeof result === 'object' && 'meta' in result) {
      const meta = result.meta;
      if (meta) {
        console.log('🌐 DB ROUTING:', {
          servedByRegion: meta.served_by_region || 'unknown',
          servedByPrimary: meta.served_by_primary || false,
          queryName: queryName || 'unnamed'
        });
      }
    }
  }

  /**
   * Prepare a statement using the current session
   */
  prepare(query: string): D1PreparedStatement {
    return this.getSession().prepare(query);
  }

  /**
   * Prepare a statement using primary session (for fresh data)
   */
  preparePrimary(query: string): D1PreparedStatement {
    return this.getPrimarySession().prepare(query);
  }

  /**
   * Execute multiple operations in the same session
   */
  async executeInSession<T>(
    operations: (session: D1DatabaseSession) => Promise<T>,
    usePrimary: boolean = false
  ): Promise<T> {
    try {
      const session = usePrimary ? this.getPrimarySession() : this.getSession();
      const result = await operations(session);
      
      // Update bookmark after successful operations
      this.setBookmarkHeader();
      
      return result;
    } catch (error) {
      console.error('❌ DB SESSION ERROR:', error);
      throw error;
    }
  }

  /**
   * Execute a single query and automatically handle bookmarks
   */
  async query<T = any>(
    queryFn: (session: D1DatabaseSession) => Promise<T>,
    options: {
      usePrimary?: boolean;
      queryName?: string;
    } = {}
  ): Promise<T> {
    const session = options.usePrimary ? this.getPrimarySession() : this.getSession();
    
    if (options.queryName) {
      console.log(`🔍 DB QUERY [${options.queryName}]: Executing via session`);
    }

    const result = await queryFn(session);
    
    // Log routing information
    this.logDatabaseRouting(result, options.queryName);
    
    // Update bookmark
    this.setBookmarkHeader();
    
    return result;
  }

  /**
   * Static factory method to create session service from context
   * PERFORMANCE: Use simple mode by default for better performance
   */
  static create(context: Context, options: { useSimpleMode?: boolean } = {}): DatabaseSessionService {
    if (!context.env?.DB) {
      throw new Error('D1 database binding not found in environment');
    }
    return new DatabaseSessionService(context.env.DB, context, {
      useSimpleMode: options.useSimpleMode ?? true
    });
  }

  /**
   * Create a middleware function for automatic session management
   */
  static middleware() {
    return async (c: Context, next: () => Promise<void>) => {
      // Create session service and attach to context
      const dbSession = DatabaseSessionService.create(c);
      c.set('dbSession', dbSession);
      
      await next();
      
      // Ensure bookmark is set in response headers
      dbSession.setBookmarkHeader();
    };
  }
}

// Helper types for better TypeScript support
export interface SessionQueryOptions {
  usePrimary?: boolean;
  queryName?: string;
}

// Utility function to get session from context
export function getDbSession(c: Context): DatabaseSessionService {
  return DatabaseSessionService.create(c);
}

// Helper function to get session from context if it exists
export function getExistingDbSession(c: Context): DatabaseSessionService | null {
  const existing = c.get('dbSession');
  return existing || null;
} 
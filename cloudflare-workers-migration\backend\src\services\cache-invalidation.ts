import { Context } from 'hono';

/**
 * Smart cache invalidation service
 * Automatically invalidates related cache entries when data changes
 */
export class CacheInvalidationService {
  private env: any;

  constructor(env: any) {
    this.env = env;
  }
  /**
   * Invalidate cache patterns in KV directly
   */
  async invalidatePattern(pattern: string): Promise<number> {
    try {
      const list = await this.env.CACHE.list({ prefix: pattern });
      let deleted = 0;
      
      for (const key of list.keys) {
        await this.env.CACHE.delete(key.name);
        deleted++;
      }
      
      return deleted;
    } catch (error) {
      console.error('Cache invalidation error:', error);
      return 0;
    }
  }

  /**
   * Invalidate product-related caches
   */
  async invalidateProduct(productId: string, handle?: string): Promise<void> {
    const patterns = [
      `product:${productId}`,
      `product_variants:${productId}`,
      `pricing:${productId}`,
      'products:list',
      'popular_products:',
      'homepage_data',
      'featured_collections'
    ];

    if (handle) {
      patterns.push(`product:${handle}`);
    }

    for (const pattern of patterns) {
      await this.invalidatePattern(pattern);
    }

    console.log(`Invalidated cache for product ${productId}`);
  }

  /**
   * Invalidate collection-related caches
   */
  async invalidateCollection(collectionId: string, handle?: string): Promise<void> {
    const patterns = [
      `collection:${collectionId}`,
      `collection_products:${collectionId}`,
      'collections:list',
      'featured_collections',
      'homepage_data',
      'products:list' // Product lists might be filtered by collection
    ];

    if (handle) {
      patterns.push(`collection:${handle}`);
      patterns.push(`collection_products:${handle}`);
    }

    for (const pattern of patterns) {
      await this.invalidatePattern(pattern);
    }

    console.log(`Invalidated cache for collection ${collectionId}`);
  }

  /**
   * Invalidate cart-related caches
   */
  async invalidateCart(cartId: string): Promise<void> {
    const patterns = [
      `cart:${cartId}`,
      `cart_totals:${cartId}`,
      `payment_methods:${cartId}`,
      `shipping_methods:` // Shipping might depend on cart contents
    ];

    for (const pattern of patterns) {
      await this.invalidatePattern(pattern);
    }

    console.log(`Invalidated cache for cart ${cartId}`);
  }

  /**
   * Invalidate order-related caches
   */
  async invalidateOrder(orderId: string, customerId?: string): Promise<void> {
    const patterns = [
      `order:${orderId}`,
      'dashboard:stats'
    ];

    if (customerId) {
      patterns.push(`customer:${customerId}`);
    }

    for (const pattern of patterns) {
      await this.invalidatePattern(pattern);
    }

    console.log(`Invalidated cache for order ${orderId}`);
  }

  /**
   * Invalidate payment-related caches
   */
  async invalidatePaymentMethods(): Promise<void> {
    await this.invalidatePattern('payment_methods:');
    console.log('Invalidated payment methods cache');
  }

  /**
   * Invalidate shipping-related caches
   */
  async invalidateShippingMethods(region?: string): Promise<void> {
    if (region) {
      await this.invalidatePattern(`shipping_methods:${region}`);
    } else {
      await this.invalidatePattern('shipping_methods:');
    }
    console.log(`Invalidated shipping methods cache${region ? ` for region ${region}` : ''}`);
  }

  /**
   * Invalidate regional pricing
   */
  async invalidateRegionalPricing(productId?: string, regionId?: string): Promise<void> {
    if (productId && regionId) {
      await this.env.CACHE.delete(`pricing:${productId}:${regionId}`);
    } else if (productId) {
      await this.invalidatePattern(`pricing:${productId}:`);
    } else {
      await this.invalidatePattern('pricing:');
    }

    console.log('Invalidated regional pricing cache');
  }

  /**
   * Invalidate customer-related caches
   */
  async invalidateCustomer(customerId: string): Promise<void> {
    const patterns = [
      `customer:${customerId}`
    ];

    for (const pattern of patterns) {
      await this.invalidatePattern(pattern);
    }

    console.log(`Invalidated cache for customer ${customerId}`);
  }

  /**
   * Invalidate homepage and featured content
   */
  async invalidateHomepage(): Promise<void> {
    const patterns = [
      'homepage_data',
      'featured_collections',
      'popular_products:'
    ];

    for (const pattern of patterns) {
      await this.invalidatePattern(pattern);
    }

    console.log('Invalidated homepage cache');
  }

  /**
   * Full cache clear (emergency use only)
   */
  async clearAllCache(): Promise<number> {
    try {
      const list = await this.env.CACHE.list();
      let deleted = 0;
      
      for (const key of list.keys) {
        await this.env.CACHE.delete(key.name);
        deleted++;
      }
      
      console.log(`Cleared all cache: ${deleted} keys deleted`);
      return deleted;
    } catch (error) {
      console.error('Failed to clear all cache:', error);
      return 0;
    }
  }

  /**
   * Get cache statistics
   */
  async getCacheStats(): Promise<{keys: number, keysByPattern: Record<string, number>}> {
    try {
      const list = await this.env.CACHE.list();
      const keysByPattern: Record<string, number> = {};
      
      for (const key of list.keys) {
        const pattern = key.name.split(':')[0];
        keysByPattern[pattern] = (keysByPattern[pattern] || 0) + 1;
      }
      
      return {
        keys: list.keys.length,
        keysByPattern
      };
    } catch (error) {
      console.error('Failed to get cache stats:', error);
      return { keys: 0, keysByPattern: {} };
    }
  }
}

/**
 * Middleware to automatically invalidate cache when data is modified
 */
export function autoInvalidateMiddleware(patterns: string[]) {
  return async (c: Context, next: any) => {
    await next();
    
    // Only invalidate on successful modifications (POST, PUT, DELETE)
    if (c.res.status >= 200 && c.res.status < 300 && 
        ['POST', 'PUT', 'DELETE', 'PATCH'].includes(c.req.method)) {
      
      const invalidationService = new CacheInvalidationService(c.env);
      
      for (const pattern of patterns) {
        await invalidationService.invalidatePattern(pattern);
      }
      
      console.log(`Auto-invalidated cache patterns: ${patterns.join(', ')}`);
    }
  };
}

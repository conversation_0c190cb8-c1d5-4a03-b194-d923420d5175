import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { sessionMiddleware, getSessionFromContext, getSessionServiceFromContext } from '../../middleware/session';

type Bindings = {
  DB: D1Database;
  JWT_SECRET: string;
  FRONTEND_URL: string;
  ADMIN_URL: string;
  CACHE: KVNamespace;
  SESSIONS: KVNamespace;
};

const app = new Hono<{ Bindings: Bindings }>();

// CORS middleware
app.use('/*', cors({
  origin: (origin) => {
    const allowedOrigins = [
      'http://localhost:3000',
      'http://localhost:3001',
      'http://localhost:3002',
      'https://handmadein.ro',
      'https://www.handmadein.ro',
      'https://staging.handmadein.ro'
    ];
    return allowedOrigins.includes(origin) || origin?.endsWith('.handmadein.ro') ? origin : null;
  },
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization', 'X-Session-ID'],
  credentials: true,
}));

// Session middleware with auto-creation
app.use('*', sessionMiddleware({
  autoCreate: true,
  trackActivity: true,
  trackPages: true
}));

// Get current session info
app.get('/info', async (c) => {
  try {
    const session = getSessionFromContext(c);
    
    if (!session) {
      return c.json({
        success: false,
        error: 'No session found'
      }, 404);
    }

    // Return safe session data (excluding sensitive info)
    // PERFORMANCE: Removed cart_data to reduce session payload size
    const safeSession = {
      id: session.id,
      is_authenticated: session.is_authenticated,
      preferences: session.preferences,
      cart_id: session.cart_id,
      flags: session.flags,
      activity: {
        last_seen: session.activity.last_seen,
        pages_visited: session.activity.pages_visited,
        time_spent: session.activity.time_spent
      },
      created_at: session.created_at,
      updated_at: session.updated_at
    };

    return c.json({
      success: true,
      session: safeSession
    });
  } catch (error) {
    console.error('Error getting session info:', error);
    return c.json({
      success: false,
      error: 'Failed to get session info'
    }, 500);
  }
});

// Update session preferences
app.put('/preferences', async (c) => {
  try {
    const session = getSessionFromContext(c);
    const sessionService = getSessionServiceFromContext(c);
    
    if (!session) {
      return c.json({
        success: false,
        error: 'No session found'
      }, 404);
    }

    const body = await c.req.json();
    const { language, currency, region, theme } = body;

    const updatedSession = await sessionService.updatePreferences(session.id, {
      language,
      currency,
      region,
      theme
    });

    return c.json({
      success: true,
      preferences: updatedSession?.preferences
    });
  } catch (error) {
    console.error('Error updating preferences:', error);
    return c.json({
      success: false,
      error: 'Failed to update preferences'
    }, 500);
  }
});

// PERFORMANCE: Simplified cart tracking - removed complex cart-session association
// Cart ID is now stored directly in session without redundant cart data
app.post('/cart', async (c) => {
  try {
    const session = getSessionFromContext(c);
    const sessionService = getSessionServiceFromContext(c);

    if (!session) {
      return c.json({
        success: false,
        error: 'No session found'
      }, 404);
    }

    const body = await c.req.json();
    const { cart_id } = body;

    if (!cart_id) {
      return c.json({
        success: false,
        error: 'cart_id is required'
      }, 400);
    }

    // Only store cart ID, not redundant cart data
    const updatedSession = await sessionService.updateSession(session.id, {
      cart_id
    });

    return c.json({
      success: true,
      cart_id: updatedSession?.cart_id
    });
  } catch (error) {
    console.error('Error associating cart:', error);
    return c.json({
      success: false,
      error: 'Failed to associate cart'
    }, 500);
  }
});

// Update session flags (for UI state management)
app.put('/flags', async (c) => {
  try {
    const session = getSessionFromContext(c);
    const sessionService = getSessionServiceFromContext(c);
    
    if (!session) {
      return c.json({
        success: false,
        error: 'No session found'
      }, 404);
    }

    const body = await c.req.json();
    const flags = body.flags;

    if (!flags || typeof flags !== 'object') {
      return c.json({
        success: false,
        error: 'flags object is required'
      }, 400);
    }

    const updatedSession = await sessionService.updateFlags(session.id, flags);

    return c.json({
      success: true,
      flags: updatedSession?.flags
    });
  } catch (error) {
    console.error('Error updating flags:', error);
    return c.json({
      success: false,
      error: 'Failed to update flags'
    }, 500);
  }
});

// Track specific events
app.post('/track', async (c) => {
  try {
    const session = getSessionFromContext(c);
    const sessionService = getSessionServiceFromContext(c);
    
    if (!session) {
      return c.json({
        success: false,
        error: 'No session found'
      }, 404);
    }

    const body = await c.req.json();
    const { event, data } = body;

    // Track different types of events
    switch (event) {
      case 'page_view':
        await sessionService.touchSession(session.id, {
          activity: {
            last_seen: new Date().toISOString(),
            pages_visited: session.activity.pages_visited + 1,
            time_spent: session.activity.time_spent + (data?.time_spent || 0),
            ip_address: session.activity.ip_address,
            user_agent: session.activity.user_agent
          }
        });
        break;
        
      case 'newsletter_popup_shown':
        await sessionService.updateFlags(session.id, {
          newsletter_popup_shown: true
        });
        break;
        
      case 'high_value_customer':
        await sessionService.markHighValueCustomer(session.id);
        break;
        
      default:
        return c.json({
          success: false,
          error: `Unknown event type: ${event}`
        }, 400);
    }

    return c.json({
      success: true,
      message: `Event '${event}' tracked successfully`
    });
  } catch (error) {
    console.error('Error tracking event:', error);
    return c.json({
      success: false,
      error: 'Failed to track event'
    }, 500);
  }
});

// Authenticate session (login)
app.post('/authenticate', async (c) => {
  try {
    const session = getSessionFromContext(c);
    const sessionService = getSessionServiceFromContext(c);
    
    if (!session) {
      return c.json({
        success: false,
        error: 'No session found'
      }, 404);
    }

    const body = await c.req.json();
    const { user_id, customer_id, email, role, login_method } = body;

    const updatedSession = await sessionService.authenticateSession(session.id, {
      user_id,
      customer_id,
      email,
      role: role || 'customer',
      login_method: login_method || 'email'
    });

    return c.json({
      success: true,
      is_authenticated: updatedSession?.is_authenticated,
      authentication: updatedSession?.authentication
    });
  } catch (error) {
    console.error('Error authenticating session:', error);
    return c.json({
      success: false,
      error: 'Failed to authenticate session'
    }, 500);
  }
});

// Logout (clear authentication but keep session)
app.post('/logout', async (c) => {
  try {
    const session = getSessionFromContext(c);
    const sessionService = getSessionServiceFromContext(c);
    
    if (!session) {
      return c.json({
        success: false,
        error: 'No session found'
      }, 404);
    }

    const updatedSession = await sessionService.updateSession(session.id, {
      user_id: undefined,
      customer_id: undefined,
      email: undefined,
      is_authenticated: false,
      authentication: undefined
    });

    return c.json({
      success: true,
      message: 'Logged out successfully',
      is_authenticated: false
    });
  } catch (error) {
    console.error('Error logging out:', error);
    return c.json({
      success: false,
      error: 'Failed to logout'
    }, 500);
  }
});

// Extend session TTL
app.post('/extend', async (c) => {
  try {
    const session = getSessionFromContext(c);
    const sessionService = getSessionServiceFromContext(c);
    
    if (!session) {
      return c.json({
        success: false,
        error: 'No session found'
      }, 404);
    }

    const body = await c.req.json();
    const additionalSeconds = body.additional_seconds || 30 * 24 * 60 * 60; // 30 days default

    const success = await sessionService.extendSession(session.id, additionalSeconds);

    return c.json({
      success,
      message: success ? 'Session extended successfully' : 'Failed to extend session'
    });
  } catch (error) {
    console.error('Error extending session:', error);
    return c.json({
      success: false,
      error: 'Failed to extend session'
    }, 500);
  }
});

// Delete current session
app.delete('/', async (c) => {
  try {
    const session = getSessionFromContext(c);
    const sessionService = getSessionServiceFromContext(c);
    
    if (!session) {
      return c.json({
        success: false,
        error: 'No session found'
      }, 404);
    }

    const success = await sessionService.deleteSession(session.id);

    return c.json({
      success,
      message: success ? 'Session deleted successfully' : 'Failed to delete session'
    });
  } catch (error) {
    console.error('Error deleting session:', error);
    return c.json({
      success: false,
      error: 'Failed to delete session'
    }, 500);
  }
});

export { app as sessionRoutes };

import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { jwt, sign } from 'hono/jwt';
import { Context } from 'hono';
import { adminShippingRoutes } from './shipping';
import { fileUploadRoutes } from './file-uploads';
import { cacheManagementRoutes } from './cache-management';
import { DatabaseSessionService, getDbSession } from '../../services/database-session';
import { triggerProductSync, triggerProductRemoval } from '../../services/product-sync';

type Bindings = {
  DB: D1Database;
  AI: Ai;
  VECTORIZE_INDEX: VectorizeIndex;
  JWT_SECRET: string;
  FRONTEND_URL: string;
  ADMIN_URL: string;
  ENVIRONMENT: string;
  CACHE: KVNamespace;
  SESSIONS: KVNamespace;
  ASSETS: R2Bucket;
  UPLOADS: R2Bucket;
};

const app = new Hono<{ Bindings: Bindings }>();

// CORS middleware
app.use('/*', cors({
  origin: ['http://localhost:3000', 'http://localhost:3001'],
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization'],
  credentials: true,
}));

// JWT middleware for protected routes
const requireAuth = jwt({
  secret: 'dev-jwt-secret-change-in-production-please-make-this-very-long-and-secure-2024',
});

// ===========================================
// UTILITIES
// ===========================================

function generateId(prefix: string): string {
  return `${prefix}_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
}

function handleError(c: Context, error: any, message = 'Internal server error') {
  console.error(error);
  return c.json({ error: message, details: error.message }, 500);
}

function safeParseDate(dateValue: any): string | null {
  if (!dateValue) return null;
  
  try {
    // Handle double-encoded JSON dates
    if (typeof dateValue === 'string' && dateValue.startsWith('"') && dateValue.endsWith('"')) {
      dateValue = JSON.parse(dateValue);
    }
    
    // Validate the date
    const date = new Date(dateValue);
    if (isNaN(date.getTime())) {
      return null;
    }
    
    return date.toISOString();
  } catch (error) {
    console.warn('Failed to parse date:', dateValue, error);
    return null;
  }
}

// ===========================================
// AUTHENTICATION ROUTES
// ===========================================

app.post('/auth/login', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    
    console.log('🔍 SIMPLIFIED AUTH DEBUG: Login endpoint hit');
    console.log('🔍 SIMPLIFIED AUTH DEBUG: Request URL:', c.req.url);
    console.log('🔍 SIMPLIFIED AUTH DEBUG: Request method:', c.req.method);
    console.log('🔍 SIMPLIFIED AUTH DEBUG: Request headers:', Object.fromEntries(c.req.raw.headers.entries()));

    const body = await c.req.json();
    console.log('🔍 SIMPLIFIED AUTH DEBUG: Request body received:', body);
    
    const { email, password } = body;
    console.log('🔍 SIMPLIFIED AUTH DEBUG: Extracted credentials:', { email, password: password ? '***' : 'MISSING' });
    
    if (!email || !password) {
      console.log('🔍 SIMPLIFIED AUTH DEBUG: Missing email or password');
      return c.json({ error: 'Email and password are required' }, 400);
    }

    // Check user credentials
    const userQuery = `
      SELECT id, email, password_hash, first_name, last_name, role, is_active
      FROM users 
      WHERE email = ? AND deleted_at IS NULL
    `;
    console.log('🔍 SIMPLIFIED AUTH DEBUG: Executing user query:', userQuery);
    console.log('🔍 SIMPLIFIED AUTH DEBUG: Query parameter:', email);
    
    const user = await dbSession.query(
      (session) => session.prepare(userQuery).bind(email).first(),
      { queryName: 'auth-login-user-lookup' }
    );
    console.log('🔍 SIMPLIFIED AUTH DEBUG: User query result:', user ? 'User found' : 'User not found');
    if (user) {
      console.log('🔍 SIMPLIFIED AUTH DEBUG: User details:', { 
        id: user.id, 
        email: user.email, 
        role: user.role,
        is_active: user.is_active,
        has_password_hash: !!user.password_hash
      });
    }

    if (!user || !user.is_active) {
      console.log('🔍 SIMPLIFIED AUTH DEBUG: User not found or inactive');
      return c.json({ error: 'Invalid credentials' }, 401);
    }

    // Note: In production, you should properly hash/verify passwords
    // For now, we'll use a simple check
    console.log('🔍 SIMPLIFIED AUTH DEBUG: Checking password against hardcoded value');
    console.log('🔍 SIMPLIFIED AUTH DEBUG: Expected password: admin123');
    console.log('🔍 SIMPLIFIED AUTH DEBUG: Received password length:', password.length);
    console.log('🔍 SIMPLIFIED AUTH DEBUG: Password match:', password === 'admin123');
    
    if (password !== 'admin123') {
      console.log('🔍 SIMPLIFIED AUTH DEBUG: Password mismatch - AUTHENTICATION FAILED');
      return c.json({ error: 'Invalid credentials' }, 401);
    }

    console.log('🔍 SIMPLIFIED AUTH DEBUG: Password match - proceeding with login');
    
    // Update last login
    console.log('🔍 SIMPLIFIED AUTH DEBUG: Updating last login timestamp');
    await dbSession.query(
      (session) => session.prepare(`
        UPDATE users 
        SET last_login_at = CURRENT_TIMESTAMP 
        WHERE id = ?
      `).bind(user.id).run(),
      { usePrimary: true, queryName: 'auth-update-last-login' }
    );

    // Generate JWT token
    const payload = {
      id: user.id,
      email: user.email,
      role: user.role,
      exp: Math.floor(Date.now() / 1000) + (60 * 60 * 24), // 24 hours
    };
    console.log('🔍 SIMPLIFIED AUTH DEBUG: JWT payload created:', { 
      id: payload.id, 
      email: payload.email, 
      role: payload.role,
      exp: new Date(payload.exp * 1000).toISOString()
    });

    const jwtSecret = c.env.JWT_SECRET || 'dev-jwt-secret-change-in-production-please-make-this-very-long-and-secure-2024';
    console.log('🔍 SIMPLIFIED AUTH DEBUG: Using JWT secret length:', jwtSecret.length);
    
    const token = await sign(payload, jwtSecret);
    console.log('🔍 SIMPLIFIED AUTH DEBUG: JWT token generated, length:', token.length);

    const responseData = {
      token,
      user: {
        id: user.id,
        email: user.email,
        first_name: user.first_name,
        last_name: user.last_name,
        role: user.role,
      }
    };
    console.log('🔍 SIMPLIFIED AUTH DEBUG: Login successful, returning response:', { 
      user: responseData.user,
      tokenLength: token.length
    });

    return c.json(responseData);

  } catch (error) {
    return handleError(c, error, 'Login failed');
  }
});

// Test endpoint to verify auth is working
app.get('/auth/me', requireAuth, async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    
    // Get JWT payload from the middleware
    const payload = c.get('jwtPayload');
    
    // Get user details from database
    const user = await dbSession.query(
      (session) => session.prepare(`
        SELECT id, email, first_name, last_name, role, is_active
        FROM users 
        WHERE id = ? AND deleted_at IS NULL
      `).bind(payload.id).first(),
      { queryName: 'auth-me-user-lookup' }
    );

    if (!user) {
      return c.json({ error: 'User not found' }, 404);
    }

    return c.json({
      user: {
        id: user.id,
        email: user.email,
        first_name: user.first_name,
        last_name: user.last_name,
        role: user.role,
      }
    });

  } catch (error) {
    return handleError(c, error, 'Failed to get user info');
  }
});

// Apply authentication middleware to all /api/* routes except login endpoints
app.use('/api/*', async (c, next) => {
  // Skip authentication for login endpoints
  const path = c.req.path;
  if (path.endsWith('/auth/login') || path.endsWith('/auth/logout')) {
    return next();
  }
  
  // Apply authentication for all other API routes
  return requireAuth(c, next);
});

// ===========================================
// PRODUCT ROUTES (Protected)
// ===========================================

// Get all products
app.get('/api/products', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '20');
    const search = c.req.query('search') || '';
    const status = c.req.query('status') || '';
    const collection_id = c.req.query('collection_id') || '';
    const sort = c.req.query('sort') || 'created_at:desc';
    const offset = (page - 1) * limit;

    // Parse sort parameter (format: "field:direction")
    const [sortField, sortDirection = 'desc'] = sort.split(':');
    const validSortFields = ['created_at', 'updated_at', 'title', 'status'];
    const validSortDirections = ['asc', 'desc'];
    
    const finalSortField = validSortFields.includes(sortField) ? sortField : 'created_at';
    const finalSortDirection = validSortDirections.includes(sortDirection.toLowerCase()) ? sortDirection.toUpperCase() : 'DESC';

    let query = `
      SELECT 
        p.id, p.handle, p.status, p.collection_id, p.type_id, p.thumbnail,
        p.weight, p.dimensions, p.requires_shipping, p.is_giftcard, p.is_discountable,
        p.tags, p.metadata, p.created_at, p.updated_at,
        pt.title, pt.subtitle, pt.description,
        c.handle as collection_handle,
        ct.title as collection_title,
        ptype.name as type_name,
        COUNT(pv.id) as variant_count
      FROM products p
      LEFT JOIN product_translations pt ON p.id = pt.product_id AND pt.language_code = 'ro'
      LEFT JOIN collections c ON p.collection_id = c.id
      LEFT JOIN collection_translations ct ON c.id = ct.collection_id AND ct.language_code = 'ro'
      LEFT JOIN product_types ptype ON p.type_id = ptype.id
      LEFT JOIN product_variants pv ON p.id = pv.product_id AND pv.deleted_at IS NULL
      WHERE p.deleted_at IS NULL
    `;

    const params: any[] = [];
    
    if (search) {
      query += ` AND (pt.title LIKE ? OR p.handle LIKE ?)`;
      params.push(`%${search}%`, `%${search}%`);
    }
    
    if (status) {
      query += ` AND p.status = ?`;
      params.push(status);
    }
    
    if (collection_id) {
      query += ` AND p.collection_id = ?`;
      params.push(collection_id);
    }

    // Add GROUP BY and sorting
    query += ` GROUP BY p.id`;
    
    if (finalSortField === 'title') {
      query += ` ORDER BY pt.title ${finalSortDirection}`;
    } else {
      query += ` ORDER BY p.${finalSortField} ${finalSortDirection}`;
    }
    
    query += ` LIMIT ? OFFSET ?`;
    params.push(limit, offset);

    // Execute products query with session
    const products = await dbSession.query(
      (session) => session.prepare(query).bind(...params).all(),
      { queryName: 'products-list' }
    );

    // Get total count
    let countQuery = `SELECT COUNT(DISTINCT p.id) as total FROM products p`;
    let countParams: any[] = [];
    
    if (search || status || collection_id) {
      countQuery += ` LEFT JOIN product_translations pt ON p.id = pt.product_id AND pt.language_code = 'ro' WHERE p.deleted_at IS NULL`;
      
      if (search) {
        countQuery += ` AND (pt.title LIKE ? OR p.handle LIKE ?)`;
        countParams.push(`%${search}%`, `%${search}%`);
      }
      
      if (status) {
        countQuery += ` AND p.status = ?`;
        countParams.push(status);
      }
      
      if (collection_id) {
        countQuery += ` AND p.collection_id = ?`;
        countParams.push(collection_id);
      }
    } else {
      countQuery += ` WHERE p.deleted_at IS NULL`;
    }

    const count = await dbSession.query(
      (session) => session.prepare(countQuery).bind(...countParams).first(),
      { queryName: 'products-count' }
    );

    // Process products to match the simplified schema structure
    const processedProducts = [];
    
    for (const product of (products.results || [])) {
      const productData = product as any;
      
      // Get inventory information for this product
      const inventoryQuery = `
        SELECT 
          SUM(pv.inventory_quantity) as total_inventory,
          COUNT(pv.id) as variant_count,
          SUM(CASE WHEN pv.manage_inventory = 1 THEN 1 ELSE 0 END) as managed_variants,
          SUM(CASE WHEN pv.allow_backorder = 1 THEN 1 ELSE 0 END) as backorder_variants,
          MIN(pv.inventory_quantity) as min_inventory,
          MAX(pv.inventory_quantity) as max_inventory
        FROM product_variants pv
        WHERE pv.product_id = ? AND pv.deleted_at IS NULL
      `;
      
      const inventoryData = await dbSession.query(
        (session) => session.prepare(inventoryQuery).bind(productData.id).first(),
        { queryName: 'product-inventory' }
      ) as any;
      
      // Calculate stock status
      const totalInventory = inventoryData?.total_inventory || 0;
      const isManagingInventory = (inventoryData?.managed_variants || 0) > 0;
      const hasBackorderAllowed = (inventoryData?.backorder_variants || 0) > 0;
      
      let stockStatus = 'in_stock';
      if (isManagingInventory) {
        if (totalInventory === 0) {
          stockStatus = hasBackorderAllowed ? 'backorder' : 'out_of_stock';
        } else if (totalInventory <= 10) {
          stockStatus = 'low_stock';
        }
      }
      
      processedProducts.push({
        ...productData,
        tags: productData.tags ? JSON.parse(productData.tags) : [],
        metadata: productData.metadata ? JSON.parse(productData.metadata) : {},
        dimensions: productData.dimensions ? JSON.parse(productData.dimensions) : null,
        
        // Add inventory information
        inventory_quantity: totalInventory,
        manage_inventory: isManagingInventory,
        allow_backorder: hasBackorderAllowed,
        stock_status: stockStatus,
        is_available: stockStatus !== 'out_of_stock',
        
        // Add inventory details for admin interface
        inventory_details: {
          total_inventory: totalInventory,
          variant_count: inventoryData?.variant_count || 0,
          managed_variants: inventoryData?.managed_variants || 0,
          min_variant_inventory: inventoryData?.min_inventory || 0,
          max_variant_inventory: inventoryData?.max_inventory || 0
        },
        
        collection: productData.collection_id ? {
          id: productData.collection_id,
          handle: productData.collection_handle,
                      translations: [{
            collection_id: productData.collection_id,
            language_code: 'ro',
            title: productData.collection_title
          }].filter(t => t.title)
        } : null,
        type: productData.type_id ? {
          id: productData.type_id,
          name: productData.type_name
        } : null,
        translations: [{
          product_id: productData.id,
          language_code: 'ro',
          title: productData.title,
          subtitle: productData.subtitle,
          description: productData.description
        }].filter(t => t.title),
        variants: [], // Will be populated separately if needed
        images: [], // Will be populated separately if needed
        options: [] // Will be populated separately if needed
      });
    }

    return c.json({
      success: true,
      data: processedProducts,
      pagination: {
        page,
        limit,
        total: count?.total || 0,
        totalPages: Math.ceil((Number(count?.total) || 0) / limit)
      }
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch products');
  }
});

// Get single product
app.get('/api/products/:id', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    
    const id = c.req.param('id');
    const currency_code = c.req.query('currency_code') || 'RON';

    const product = await dbSession.query(
      (session) => session.prepare(`
        SELECT 
          p.id, p.handle, p.status, p.collection_id, p.type_id, p.thumbnail,
          p.weight, p.dimensions, p.requires_shipping, p.is_giftcard, p.is_discountable,
          p.tags, p.metadata, p.created_at, p.updated_at,
          pt.title, pt.subtitle, pt.description,
          c.handle as collection_handle,
          ct.title as collection_title,
          ptype.name as type_name
        FROM products p
        LEFT JOIN product_translations pt ON p.id = pt.product_id AND pt.language_code = 'ro'
        LEFT JOIN collections c ON p.collection_id = c.id
        LEFT JOIN collection_translations ct ON c.id = ct.collection_id AND ct.language_code = 'ro'
        LEFT JOIN product_types ptype ON p.type_id = ptype.id
        WHERE p.id = ? AND p.deleted_at IS NULL
      `).bind(id).first(),
      { queryName: 'single-product' }
    );

    if (!product) {
      return c.json({ error: 'Product not found' }, 404);
    }

    // Get variants with pricing
    const variants = await dbSession.query(
      (session) => session.prepare(`
        SELECT
          pv.id, pv.title, pv.sku, pv.barcode, pv.allow_backorder,
          pv.manage_inventory, pv.weight, pv.dimensions, pv.sort_order,
          pv.inventory_quantity, pv.option_values, pv.metadata,
          pv.created_at, pv.updated_at,
          vp.price, vp.compare_at_price, vp.cost_price, vp.currency_code, vp.region_id
        FROM product_variants pv
        LEFT JOIN variant_prices vp ON pv.id = vp.variant_id
          AND UPPER(vp.currency_code) = UPPER(?)
          AND (vp.starts_at IS NULL OR vp.starts_at <= datetime('now'))
          AND (vp.ends_at IS NULL OR vp.ends_at >= datetime('now'))
          AND vp.id = (
            SELECT vp2.id 
            FROM variant_prices vp2 
            WHERE vp2.variant_id = pv.id 
              AND UPPER(vp2.currency_code) = UPPER(?)
              AND (vp2.starts_at IS NULL OR vp2.starts_at <= datetime('now'))
              AND (vp2.ends_at IS NULL OR vp2.ends_at >= datetime('now'))
            ORDER BY 
              CASE WHEN vp2.region_id IS NOT NULL THEN 1 ELSE 2 END,
              vp2.created_at DESC
            LIMIT 1
          )
        WHERE pv.product_id = ? AND pv.deleted_at IS NULL
      `).bind(currency_code, currency_code, id).all(),
      { queryName: 'product-variants' }
    );

    // Get images
    const images = await dbSession.query(
      (session) => session.prepare(`
        SELECT pi.id, pi.url, pi.alt_text, pi.sort_order, pi.metadata, pi.created_at
        FROM product_images pi
        WHERE pi.product_id = ?
        ORDER BY pi.sort_order, pi.created_at
      `).bind(id).all(),
      { queryName: 'product-images' }
    );

    // Get options
    const options = await dbSession.query(
      (session) => session.prepare(`
        SELECT po.id, po.name, po.type, po.sort_order, po.created_at
        FROM product_options po
        WHERE po.product_id = ?
        ORDER BY po.sort_order, po.created_at
      `).bind(id).all(),
      { queryName: 'product-options' }
    );

    return c.json({
      success: true,
      data: {
        ...product,
        variants: variants.results || [],
        images: images.results || [],
        options: options.results || []
      }
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch product');
  }
});

// Create product
app.post('/api/products', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    
    const data = await c.req.json();
    const productId = generateId('prod');

    // Create product
    await dbSession.query(
      (session) => session.prepare(`
        INSERT INTO products (
          id, handle, status, collection_id, type_id, thumbnail, weight, dimensions,
          requires_shipping, is_giftcard, is_discountable, tags, metadata
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).bind(
        productId,
        data.handle,
        data.status || 'draft',
        data.collection_id || null,
        data.type_id || null,
        data.thumbnail || null,
        data.weight || null,
        JSON.stringify(data.dimensions || {}),
        data.requires_shipping !== false,
        data.is_giftcard || false,
        data.is_discountable !== false,
        JSON.stringify(data.tags || []),
        JSON.stringify(data.metadata || {})
      ).run(),
      { usePrimary: true, queryName: 'create-product' }
    );

    // Create product translation
    if (data.title) {
      await dbSession.query(
        (session) => session.prepare(`
          INSERT INTO product_translations (
            product_id, language_code, title, subtitle, description, seo_title, seo_description
          ) VALUES (?, 'ro', ?, ?, ?, ?, ?)
        `).bind(
          productId,
          data.title,
          data.subtitle || null,
          data.description || null,
          data.seo_title || null,
          data.seo_description || null
        ).run(),
        { usePrimary: true, queryName: 'create-product-translation' }
      );
    }

    // Trigger Vectorize sync for the new product (non-blocking)
    try {
      await triggerProductSync(c.env, productId);
    } catch (syncError) {
      console.error('Failed to sync product to Vectorize:', syncError);
      // Don't fail the entire request if sync fails
    }

    return c.json({ id: productId, message: 'Product created successfully' }, 201);

  } catch (error) {
    return handleError(c, error, 'Failed to create product');
  }
});

// Update product
app.put('/api/products/:id', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const id = c.req.param('id');
    const data = await c.req.json();

    // Update product
    await c.env.DB.prepare(`
      UPDATE products SET
        handle = ?, status = ?, collection_id = ?, type_id = ?, thumbnail = ?,
        weight = ?, dimensions = ?, requires_shipping = ?, is_giftcard = ?,
        is_discountable = ?, tags = ?, metadata = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `).bind(
      data.handle,
      data.status,
      data.collection_id || null,
      data.type_id || null,
      data.thumbnail || null,
      data.weight || null,
      JSON.stringify(data.dimensions || {}),
      data.requires_shipping !== false,
      data.is_giftcard || false,
      data.is_discountable !== false,
      JSON.stringify(data.tags || []),
      JSON.stringify(data.metadata || {}),
      id
    ).run();

    // Update or create translation
    const existingTranslation = await dbSession.query(
      (session) => session.prepare(`
      SELECT product_id FROM product_translations 
      WHERE product_id = ? AND language_code = 'ro'
    `).bind(id).first(),
      { queryName: 'db-query' }
    );

    if (existingTranslation) {
      await dbSession.query(
      (session) => session.prepare(`
        UPDATE product_translations SET
          title = ?, subtitle = ?, description = ?, seo_title = ?, seo_description = ?
        WHERE product_id = ? AND language_code = 'ro'
      `).bind(
        data.title,
        data.subtitle || null,
        data.description || null,
        data.seo_title || null,
        data.seo_description || null,
        id
      ).run(),
      { usePrimary: true, queryName: 'db-write' }
    );
    } else if (data.title) {
      await dbSession.query(

        (session) => session.prepare(`
        INSERT INTO product_translations (
          product_id, language_code, title, subtitle, description, seo_title, seo_description
        ) VALUES (?, 'ro', ?, ?, ?, ?, ?)
      `).bind(
        id,
        data.title,
        data.subtitle || null,
        data.description || null,
        data.seo_title || null,
        data.seo_description || null
      ).run(),

        { usePrimary: true, queryName: 'db-write' }

      );
    }

    // Trigger Vectorize sync for the updated product (non-blocking)
    try {
      await triggerProductSync(c.env, id);
    } catch (syncError) {
      console.error('Failed to sync updated product to Vectorize:', syncError);
      // Don't fail the entire request if sync fails
    }

    return c.json({ message: 'Product updated successfully' });

  } catch (error) {
    return handleError(c, error, 'Failed to update product');
  }
});

// Delete product
app.delete('/api/products/:id', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const id = c.req.param('id');

    await dbSession.query(
      (session) => session.prepare(`
      UPDATE products 
      SET deleted_at = CURRENT_TIMESTAMP 
      WHERE id = ?
    `).bind(id).run(),
      { usePrimary: true, queryName: 'db-write' }
    );

    // Remove product from Vectorize index (non-blocking)
    try {
      await triggerProductRemoval(c.env, id);
    } catch (syncError) {
      console.error('Failed to remove product from Vectorize:', syncError);
      // Don't fail the entire request if sync fails
    }

    return c.json({ message: 'Product deleted successfully' });

  } catch (error) {
    return handleError(c, error, 'Failed to delete product');
  }
});

// Bulk delete products
app.post('/api/products/bulk-delete', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const { productIds } = await c.req.json();

    if (!productIds || !Array.isArray(productIds) || productIds.length === 0) {
      return c.json({ error: 'Product IDs are required' }, 400);
    }

    const placeholders = productIds.map(() => '?').join(',');
    
    await dbSession.query(
      (session) => session.prepare(`
      UPDATE products 
      SET deleted_at = CURRENT_TIMESTAMP 
      WHERE id IN (${placeholders})
    `).bind(...productIds).run(),
      { usePrimary: true, queryName: 'db-write' }
    );

    // Remove products from Vectorize index (non-blocking)
    try {
      for (const productId of productIds) {
        await triggerProductRemoval(c.env, productId);
      }
    } catch (syncError) {
      console.error('Failed to remove products from Vectorize:', syncError);
      // Don't fail the entire request if sync fails
    }

    return c.json({ message: `${productIds.length} products deleted successfully` });

  } catch (error) {
    return handleError(c, error, 'Failed to bulk delete products');
  }
});

// Bulk update product status
app.post('/api/products/bulk-update', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const { productIds, status } = await c.req.json();

    if (!productIds || !Array.isArray(productIds) || productIds.length === 0) {
      return c.json({ error: 'Product IDs are required' }, 400);
    }

    if (!status) {
      return c.json({ error: 'Status is required' }, 400);
    }

    const placeholders = productIds.map(() => '?').join(',');
    
    await dbSession.query(
      (session) => session.prepare(`
      UPDATE products 
      SET status = ?, updated_at = CURRENT_TIMESTAMP 
      WHERE id IN (${placeholders})
    `).bind(status, ...productIds).run(),
      { usePrimary: true, queryName: 'db-write' }
    );

    // Sync updated products to Vectorize index (non-blocking)
    try {
      for (const productId of productIds) {
        await triggerProductSync(c.env, productId);
      }
    } catch (syncError) {
      console.error('Failed to sync updated products to Vectorize:', syncError);
      // Don't fail the entire request if sync fails
    }

    return c.json({ message: `${productIds.length} products updated successfully` });

  } catch (error) {
    return handleError(c, error, 'Failed to bulk update products');
  }
});

// ===========================================
// PRODUCT VARIANT ROUTES
// ===========================================

// Get product variants
app.get('/api/products/:id/variants', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const id = c.req.param('id');
    const currency_code = c.req.query('currency_code') || 'RON';

    const variants = await dbSession.query(
   (session) => session.prepare(`
      SELECT
        pv.id, pv.title, pv.sku, pv.barcode, pv.allow_backorder,
        pv.manage_inventory, pv.weight, pv.dimensions, pv.sort_order,
        pv.inventory_quantity, pv.option_values, pv.metadata,
        pv.created_at, pv.updated_at,
        vp.price, vp.compare_at_price, vp.cost_price, vp.currency_code, vp.region_id
      FROM product_variants pv
      LEFT JOIN variant_prices vp ON pv.id = vp.variant_id
        AND UPPER(vp.currency_code) = UPPER(?)
        AND (vp.starts_at IS NULL OR vp.starts_at <= datetime('now'))
        AND (vp.ends_at IS NULL OR vp.ends_at >= datetime('now'))
        AND vp.id = (
          SELECT vp2.id 
          FROM variant_prices vp2 
          WHERE vp2.variant_id = pv.id 
            AND UPPER(vp2.currency_code) = UPPER(?)
            AND (vp2.starts_at IS NULL OR vp2.starts_at <= datetime('now'))
            AND (vp2.ends_at IS NULL OR vp2.ends_at >= datetime('now'))
          ORDER BY 
            CASE WHEN vp2.region_id IS NOT NULL THEN 1 ELSE 2 END,
            vp2.created_at DESC
          LIMIT 1
        )
      WHERE pv.product_id = ? AND pv.deleted_at IS NULL
      ORDER BY pv.sort_order, pv.created_at
    `).bind(currency_code, currency_code, id).all(),
   { queryName: 'db-query' }
 );

    return c.json({
      success: true,
      data: variants.results || []
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch product variants');
  }
});

// Get product images
app.get('/api/products/:id/images', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const id = c.req.param('id');

    const images = await dbSession.query(
      (session) => session.prepare(`
      SELECT pi.id, pi.url, pi.alt_text, pi.sort_order, pi.metadata, pi.created_at
      FROM product_images pi
      WHERE pi.product_id = ?
      ORDER BY pi.sort_order, pi.created_at
    `).bind(id).all(),
      { queryName: 'db-query' }
    );

    return c.json({
      success: true,
      data: images.results || []
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch product images');
  }
});

// Get product options
app.get('/api/products/:id/options', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const id = c.req.param('id');

    const options = await dbSession.query(
      (session) => session.prepare(`
      SELECT po.id, po.name, po.type, po.sort_order, po.created_at
      FROM product_options po
      WHERE po.product_id = ?
      ORDER BY po.sort_order, po.created_at
    `).bind(id).all(),
      { queryName: 'db-query' }
    );

    return c.json({
      success: true,
      data: options.results || []
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch product options');
  }
});

// Create variant
app.post('/api/products/:productId/variants', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const productId = c.req.param('productId');
    const data = await c.req.json();
    const variantId = generateId('variant');

    await c.env.DB.prepare(`
      INSERT INTO product_variants (
        id, product_id, title, sku, barcode, weight, dimensions, sort_order,
        option_values, manage_inventory, allow_backorder, inventory_quantity, metadata
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `).bind(
      variantId,
      productId,
      data.title,
      data.sku || null,
      data.barcode || null,
      data.weight || null,
      JSON.stringify(data.dimensions || {}),
      data.sort_order || 0,
      JSON.stringify(data.option_values || []),
      data.manage_inventory !== false,
      data.allow_backorder || false,
      data.inventory_quantity || 0,
      JSON.stringify(data.metadata || {})
    ).run();

    return c.json({
      success: true,
      data: { id: variantId },
      message: 'Variant created successfully'
    }, 201);

  } catch (error) {
    return handleError(c, error, 'Failed to create variant');
  }
});

// Update variant
app.put('/api/products/:productId/variants/:variantId', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const productId = c.req.param('productId');
    const variantId = c.req.param('variantId');
    const data = await c.req.json();

    // Check if variant exists
    const variant = await dbSession.query(
      (session) => session.prepare(`
      SELECT id FROM product_variants
      WHERE id = ? AND product_id = ? AND deleted_at IS NULL
    `).bind(variantId, productId).first(),
      { queryName: 'db-query' }
    );

    if (!variant) {
      return c.json({ error: 'Variant not found' }, 404);
    }

    await c.env.DB.prepare(`
      UPDATE product_variants SET
        title = ?, sku = ?, barcode = ?, weight = ?, dimensions = ?, sort_order = ?,
        option_values = ?, manage_inventory = ?, allow_backorder = ?, inventory_quantity = ?,
        metadata = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ? AND product_id = ?
    `).bind(
      data.title,
      data.sku || null,
      data.barcode || null,
      data.weight || null,
      JSON.stringify(data.dimensions || {}),
      data.sort_order || 0,
      JSON.stringify(data.option_values || []),
      data.manage_inventory !== false,
      data.allow_backorder || false,
      data.inventory_quantity || 0,
      JSON.stringify(data.metadata || {}),
      variantId,
      productId
    ).run();

    return c.json({
      success: true,
      data: { id: variantId },
      message: 'Variant updated successfully'
    });

  } catch (error) {
    return handleError(c, error, 'Failed to update variant');
  }
});

// Create variant price
app.post('/api/products/:productId/variants/:variantId/prices', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const productId = c.req.param('productId');
    const variantId = c.req.param('variantId');
    const data = await c.req.json();
    const priceId = generateId('price');

    // Check if variant exists
    const variant = await dbSession.query(
      (session) => session.prepare(`
      SELECT id FROM product_variants
      WHERE id = ? AND product_id = ? AND deleted_at IS NULL
    `).bind(variantId, productId).first(),
      { queryName: 'db-query' }
    );

    if (!variant) {
      return c.json({ error: 'Variant not found' }, 404);
    }

    // Use RON as default currency and convert to uppercase for consistency
    const currencyCode = (data.currency_code || 'RON').toUpperCase();

    // Check if price already exists for this variant and currency
    const existingPrice = await dbSession.query(
      (session) => session.prepare(`
      SELECT id FROM variant_prices
      WHERE variant_id = ? AND UPPER(currency_code) = ? AND (region_id = ? OR (region_id IS NULL AND ? IS NULL))
    `).bind(variantId, currencyCode, data.region_id || null, data.region_id || null).first(),
      { queryName: 'db-query' }
    );

    if (existingPrice) {
      // Update existing price
      await dbSession.query(
      (session) => session.prepare(`
        UPDATE variant_prices
        SET price = ?, compare_at_price = ?, cost_price = ?, min_quantity = ?, max_quantity = ?,
            starts_at = ?, ends_at = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `).bind(
        data.price,
        data.compare_at_price || null,
        data.cost_price || null,
        data.min_quantity || 1,
        data.max_quantity || null,
        data.starts_at || null,
        data.ends_at || null,
        existingPrice.id
      ).run(),
      { usePrimary: true, queryName: 'db-write' }
    );

      return c.json({
        success: true,
        data: { id: existingPrice.id },
        message: 'Variant price updated successfully'
      });
    } else {
      // Create new price
      await dbSession.query(
      (session) => session.prepare(`
        INSERT INTO variant_prices (
          id, variant_id, currency_code, region_id, price, compare_at_price, cost_price,
          min_quantity, max_quantity, starts_at, ends_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).bind(
        priceId,
        variantId,
        currencyCode,
        data.region_id || null,
        data.price,
        data.compare_at_price || null,
        data.cost_price || null,
        data.min_quantity || 1,
        data.max_quantity || null,
        data.starts_at || null,
        data.ends_at || null
      ).run(),
      { usePrimary: true, queryName: 'db-write' }
    );

      return c.json({
        success: true,
        data: { id: priceId },
        message: 'Variant price created successfully'
      }, 201);
    }

  } catch (error) {
    return handleError(c, error, 'Failed to create/update variant price');
  }
});

// ===========================================
// CURRENCIES AND REGIONS ROUTES
// ===========================================

// Get all currencies
app.get('/api/currencies', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const currencies = await dbSession.query(
      (session) => session.prepare(`
      SELECT code, name, symbol, decimal_places, is_default, is_active
      FROM currencies
      WHERE is_active = 1
      ORDER BY is_default DESC, name ASC
    `).all(),
      { queryName: 'db-query' }
    );

    return c.json({
      success: true,
      data: currencies.results || []
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch currencies');
  }
});

// Get all regions
app.get('/api/regions', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const regions = await dbSession.query(
      (session) => session.prepare(`
      SELECT id, name, code, currency_code, language_code, countries, is_active
      FROM regions
      WHERE is_active = 1
      ORDER BY name ASC
    `).all(),
      { queryName: 'db-query' }
    );

    return c.json({
      success: true,
      data: regions.results || []
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch regions');
  }
});

// ===========================================
// COLLECTIONS ROUTES
// ===========================================

// Get all collections
app.get('/api/collections', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const collections = await dbSession.query(

      (session) => session.prepare(`
      SELECT 
        c.id, c.handle, c.image_url, c.sort_order, c.is_active, c.metadata, c.created_at, c.updated_at,
        ct.title, ct.description, ct.seo_title, ct.seo_description,
        COUNT(p.id) as products_count
      FROM collections c
      LEFT JOIN collection_translations ct ON c.id = ct.collection_id AND ct.language_code ='ro'
      LEFT JOIN products p ON c.id = p.collection_id AND p.deleted_at IS NULL AND p.status = 'published'
      WHERE c.deleted_at IS NULL
      GROUP BY c.id, c.handle, c.image_url, c.sort_order, c.is_active, c.metadata, c.created_at, c.updated_at,
               ct.title, ct.description, ct.seo_title, ct.seo_description
      ORDER BY c.sort_order, c.created_at DESC
    `).all(),

      { queryName: 'db-query' }

    );

    // Process collections to match the simplified schema structure
    const processedCollections = (collections.results || []).map((collection: any) => ({
      ...collection,
      metadata: collection.metadata ? JSON.parse(collection.metadata as string) : {},
      products_count: collection.products_count || 0,
      // Add translations array for compatibility
      translations: [{
        collection_id: collection.id,
        language_code: 'ro',
        title: collection.title,
        description: collection.description,
        seo_title: collection.seo_title,
        seo_description: collection.seo_description
      }]
    }));

    return c.json({ success: true, data: processedCollections });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch collections');
  }
});

// Bulk delete collections
app.post('/api/collections/bulk-delete', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const { collectionIds } = await c.req.json();

    if (!collectionIds || !Array.isArray(collectionIds) || collectionIds.length === 0) {
      return c.json({ error: 'Collection IDs are required' }, 400);
    }

    const placeholders = collectionIds.map(() => '?').join(',');
    
    await dbSession.query(
      (session) => session.prepare(`
      UPDATE collections 
      SET deleted_at = CURRENT_TIMESTAMP 
      WHERE id IN (${placeholders})
    `).bind(...collectionIds).run(),
      { usePrimary: true, queryName: 'db-write' }
    );

    return c.json({ message: `${collectionIds.length} collections deleted successfully` });

  } catch (error) {
    return handleError(c, error, 'Failed to bulk delete collections');
  }
});

// Get single collection
app.get('/api/collections/:id', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const id = c.req.param('id');

    const collection = await dbSession.query(
   (session) => session.prepare(`
      SELECT 
        c.id, c.handle, c.image_url, c.sort_order, c.is_active, c.metadata, c.created_at, c.updated_at,
        ct.title, ct.description, ct.seo_title, ct.seo_description,
        COUNT(p.id) as products_count
      FROM collections c
      LEFT JOIN collection_translations ct ON c.id = ct.collection_id AND ct.language_code ='ro'
      LEFT JOIN products p ON c.id = p.collection_id AND p.deleted_at IS NULL AND p.status = 'published'
      WHERE c.id = ? AND c.deleted_at IS NULL
      GROUP BY c.id, c.handle, c.image_url, c.sort_order, c.is_active, c.metadata, c.created_at, c.updated_at,
               ct.title, ct.description, ct.seo_title, ct.seo_description
    `).bind(id).first(),
   { queryName: 'db-query' }
 )as any;

    if (!collection) {
      return c.json({ error: 'Collection not found' }, 404);
    }

    // Process the collection to match the simplified schema structure
    const processedCollection = {
      ...collection,
      metadata: collection.metadata ? JSON.parse(collection.metadata as string) : {},
      products_count: collection.products_count || 0,
      translations: [{
        collection_id: collection.id,
        language_code: 'ro',
        title: collection.title,
        description: collection.description,
        seo_title: collection.seo_title,
        seo_description: collection.seo_description
      }].filter(t => t.title)
    };

    return c.json({
      success: true,
      data: processedCollection
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch collection');
  }
});

// Update collection
app.put('/api/collections/:id', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const id = c.req.param('id');
    const data = await c.req.json();

    // Update collection - now including image_url
    await c.env.DB.prepare(`
      UPDATE collections 
      SET handle = ?, image_url = ?, sort_order = ?, is_active = ?, metadata = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `).bind(
      data.handle || data.title?.toLowerCase().replace(/[^a-z0-9]+/g, '-'),
      data.image_url || null,
      data.sort_order || 0,
      data.is_active !== false ? 1 : 0,
      JSON.stringify(data.metadata || {}),
      id
    ).run();

    // Update or create translation
    const updateTranslationResult = await dbSession.query(
      (session) => session.prepare(`
      UPDATE collection_translations 
      SET title = ?, description = ?, seo_title = ?, seo_description = ?
      WHERE collection_id = ? AND language_code ='ro'
    `).bind(
      data.title,
      data.description || null,
      data.seo_title || null,
      data.seo_description || null,
      id
    ).run(),
      { usePrimary: true, queryName: 'db-write' }
    );

    // If no rows were affected, insert new translation
    if (updateTranslationResult.meta.changes === 0) {
      await dbSession.query(

        (session) => session.prepare(`
        INSERT INTO collection_translations (collection_id, language_code, title, description, seo_title, seo_description)
        VALUES (?, 'ro', ?, ?, ?, ?)
      `).bind(
        id,
        data.title,
        data.description || null,
        data.seo_title || null,
        data.seo_description || null
      ).run(),

        { usePrimary: true, queryName: 'db-write' }

      );
    }

    // Get updated collection - now including image_url and product count
    const updatedCollection = await dbSession.query(
   (session) => session.prepare(`
      SELECT 
        c.id, c.handle, c.image_url, c.sort_order, c.is_active, c.metadata, c.created_at, c.updated_at,
        ct.title, ct.description, ct.seo_title, ct.seo_description,
        COUNT(p.id) as products_count
      FROM collections c
      LEFT JOIN collection_translations ct ON c.id = ct.collection_id AND ct.language_code ='ro'
      LEFT JOIN products p ON c.id = p.collection_id AND p.deleted_at IS NULL AND p.status = 'published'
      WHERE c.id = ?
      GROUP BY c.id, c.handle, c.image_url, c.sort_order, c.is_active, c.metadata, c.created_at, c.updated_at,
               ct.title, ct.description, ct.seo_title, ct.seo_description
    `).bind(id).first(),
   { queryName: 'db-query' }
 ) as any;

    if (!updatedCollection) {
      return c.json({ error: 'Collection not found after update' }, 404);
    }

    const processedCollection = {
      ...updatedCollection,
      metadata: updatedCollection.metadata ? JSON.parse(updatedCollection.metadata as string) : {},
      products_count: updatedCollection.products_count || 0,
      translations: [{
        collection_id: updatedCollection.id,
        language_code: 'ro',
        title: updatedCollection.title,
        description: updatedCollection.description,
        seo_title: updatedCollection.seo_title,
        seo_description: updatedCollection.seo_description
      }].filter(t => t.title)
    };

    return c.json({
      success: true,
      data: processedCollection
    });

  } catch (error) {
    return handleError(c, error, 'Failed to update collection');
  }
});

// Delete collection
app.delete('/api/collections/:id', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const id = c.req.param('id');

    await dbSession.query(
      (session) => session.prepare(`
      UPDATE collections 
      SET deleted_at = CURRENT_TIMESTAMP 
      WHERE id = ?
    `).bind(id).run(),
      { usePrimary: true, queryName: 'db-write' }
    );

    return c.json({ message: 'Collection deleted successfully' });

  } catch (error) {
    return handleError(c, error, 'Failed to delete collection');
  }
});

// Create collection
app.post('/api/collections', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const data = await c.req.json();

    if (!data.title) {
      return c.json({ error: 'Title is required' }, 400);
    }

    const collectionId = generateId('coll');
    const handle = data.handle || data.title.toLowerCase().replace(/[^a-z0-9]+/g, '-');

    // Create collection - now including image_url
    await c.env.DB.prepare(`
      INSERT INTO collections (id, handle, image_url, sort_order, is_active, metadata, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `).bind(
      collectionId,
      handle,
      data.image_url || null,
      data.sort_order || 0,
      data.is_active !== false ? 1 : 0,
      JSON.stringify(data.metadata || {})
    ).run();

    // Create translation
    await dbSession.query(

      (session) => session.prepare(`
      INSERT INTO collection_translations (collection_id, language_code, title, description, seo_title, seo_description)
      VALUES (?, 'ro', ?, ?, ?, ?)
    `).bind(
      collectionId,
      data.title,
      data.description || null,
      data.seo_title || null,
      data.seo_description || null
    ).run(),

      { usePrimary: true, queryName: 'db-write' }

    );

    // Get created collection - now including image_url and product count
    const createdCollection = await dbSession.query(
      (session) => session.prepare(`
      SELECT 
        c.id, c.handle, c.image_url, c.sort_order, c.is_active, c.metadata, c.created_at, c.updated_at,
        ct.title, ct.description, ct.seo_title, ct.seo_description
      FROM collections c
      LEFT JOIN collection_translations ct ON c.id = ct.collection_id AND ct.language_code ='ro'
      WHERE c.id = ?
    `).bind(collectionId).first(),
      { queryName: 'db-query' }
    ) as any;

    if (!createdCollection) {
      return c.json({ error: 'Collection not found after creation' }, 500);
    }

    const processedCollection = {
      ...createdCollection,
      metadata: createdCollection.metadata ? JSON.parse(createdCollection.metadata as string) : {},
      translations: [{
        collection_id: createdCollection.id,
        language_code: 'ro',
        title: createdCollection.title,
        description: createdCollection.description,
        seo_title: createdCollection.seo_title,
        seo_description: createdCollection.seo_description
      }].filter(t => t.title)
    };

    return c.json({
      success: true,
      data: processedCollection
    });

  } catch (error) {
    return handleError(c, error, 'Failed to create collection');
  }
});

// ===========================================
// CUSTOMERS ROUTES
// ===========================================

// Get all customers
app.get('/api/customers', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '20');
    const search = c.req.query('search') || '';
    const offset = (page - 1) * limit;

    let query = `
      SELECT 
        c.id, c.email, c.phone, c.first_name, c.last_name, c.date_of_birth,
        c.group_id, c.accepts_marketing, c.tax_exempt, c.metadata, c.created_at, c.updated_at,
        cg.name as group_name,
        COALESCE(order_stats.orders_count, 0) as orders_count,
        COALESCE(order_stats.total_spent, 0) as total_spent,
        ca.city as address_city,
        ca.country_code as address_country
      FROM customers c
      LEFT JOIN customer_groups cg ON c.group_id = cg.id
      LEFT JOIN (
        SELECT 
          customer_id,
          COUNT(id) as orders_count,
          SUM(total_amount) as total_spent
        FROM orders 
        GROUP BY customer_id
      ) order_stats ON c.id = order_stats.customer_id
      LEFT JOIN customer_addresses ca ON c.id = ca.customer_id AND ca.is_default = 1 
      WHERE 1=1
    `;

    const params: any[] = [];
    
    if (search) {
      query += ` AND (c.email LIKE ? OR c.first_name LIKE ? OR c.last_name LIKE ?)`;
      params.push(`%${search}%`, `%${search}%`, `%${search}%`);
    }

    query += ` ORDER BY c.created_at DESC LIMIT ? OFFSET ?`;
    params.push(limit, offset);

    const customersResult = await dbSession.query(
      (session) => session.prepare(query).bind(...params).all(),
      { queryName: 'db-query' }
    );

    // Process customers data to match frontend expectations
    const customers = (customersResult.results || []).map((customer: any) => ({
      id: customer.id,
      email: customer.email,
      phone: customer.phone,
      first_name: customer.first_name,
      last_name: customer.last_name,
      date_of_birth: safeParseDate(customer.date_of_birth),
      group_id: customer.group_id,
      accepts_marketing: Boolean(customer.accepts_marketing),
      tax_exempt: Boolean(customer.tax_exempt),
      has_account: Boolean(customer.email), // Simple logic: if has email, considered as having account
      metadata: customer.metadata ? (typeof customer.metadata === 'string' ? JSON.parse(customer.metadata) : customer.metadata) : null,
      created_at: safeParseDate(customer.created_at),
      updated_at: safeParseDate(customer.updated_at),
      orders_count: Number(customer.orders_count) || 0,
      total_spent: Number(customer.total_spent) || 0,
      addresses: customer.address_city ? [{
        id: 'default',
        city: customer.address_city,
        country_code: customer.address_country,
        is_default: true
      }] : [],
      groups: customer.group_name ? [{ id: customer.group_id, name: customer.group_name }] : [],
      group_name: customer.group_name
    }));

    // Get total count
    let countQuery = `SELECT COUNT(*) as total FROM customers WHERE 1=1`;
    let countParams: any[] = [];
    
    if (search) {
      countQuery += ` AND (email LIKE ? OR first_name LIKE ? OR last_name LIKE ?)`;
      countParams.push(`%${search}%`, `%${search}%`, `%${search}%`);
    }

    const count = await dbSession.query(
      (session) => session.prepare(countQuery).bind(...countParams).first(),
      { queryName: 'db-query' }
    );

    return c.json({
      success: true,
      data: customers,
      pagination: {
        page,
        limit,
        total: count?.total || 0,
        pages: Math.ceil((Number(count?.total) || 0) / limit)
      },
      stats: {
        withAccounts: customers.filter((c: any) => c.has_account).length,
        newThisMonth: 0, // TODO: Calculate new customers this month
        avgLifetimeValue: customers.length > 0 ? customers.reduce((sum: number, c: any) => sum + c.total_spent, 0) / customers.length : 0
      }
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch customers');
  }
});

// Get customer by ID
app.get('/api/customers/:id', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const id = c.req.param('id');

    // Get customer with group and address data
    const customer = await dbSession.query(
      (session) => session.prepare(`
      SELECT 
        c.id, c.email, c.phone, c.first_name, c.last_name, c.date_of_birth,
        c.group_id, c.accepts_marketing, c.tax_exempt, c.metadata, c.created_at, c.updated_at,
        cg.name as group_name,
        COALESCE(order_stats.orders_count, 0) as orders_count,
        COALESCE(order_stats.total_spent, 0) as total_spent
      FROM customers c
      LEFT JOIN customer_groups cg ON c.group_id = cg.id
      LEFT JOIN (
        SELECT 
          customer_id,
          COUNT(id) as orders_count,
          SUM(total_amount) as total_spent
        FROM orders 
        WHERE customer_id = ?
        GROUP BY customer_id
      ) order_stats ON c.id = order_stats.customer_id
      WHERE c.id = ?
    `).bind(id, id).first(),
      { queryName: 'db-query' }
    );

    if (!customer) {
      return c.json({ error: 'Customer not found' }, 404);
    }

    // Get customer addresses
    const addresses = await dbSession.query(
      (session) => session.prepare(`
      SELECT * FROM customer_addresses 
      WHERE customer_id = ? 
      ORDER BY is_default DESC, created_at ASC
    `).bind(id).all(),
      { queryName: 'db-query' }
    );

    // Process customer data
    const processedCustomer = {
      id: customer.id,
      email: customer.email,
      phone: customer.phone,
      first_name: customer.first_name,
      last_name: customer.last_name,
      date_of_birth: safeParseDate(customer.date_of_birth),
      group_id: customer.group_id,
      accepts_marketing: Boolean(customer.accepts_marketing),
      tax_exempt: Boolean(customer.tax_exempt),
      has_account: Boolean(customer.email),
      metadata: customer.metadata ? (typeof customer.metadata === 'string' ? JSON.parse(customer.metadata) : customer.metadata) : null,
      created_at: safeParseDate(customer.created_at),
      updated_at: safeParseDate(customer.updated_at),
      orders_count: Number(customer.orders_count) || 0,
      total_spent: Number(customer.total_spent) || 0,
      addresses: addresses.results || [],
      groups: customer.group_name ? [{ id: customer.group_id, name: customer.group_name }] : [],
      group_name: customer.group_name
    };

    return c.json({ success: true, data: processedCustomer });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch customer');
  }
});

// Create customer
app.post('/api/customers', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const body = await c.req.json();
    const { 
      email, 
      first_name, 
      last_name, 
      phone, 
      date_of_birth,
      group_id,
      accepts_marketing = false,
      tax_exempt = false,
      metadata 
    } = body;

    if (!email) {
      return c.json({ error: 'Email is required' }, 400);
    }

    // Check if customer already exists
    const existingCustomer = await dbSession.query(
      (session) => session.prepare(`
      SELECT id FROM customers WHERE email = ?
    `).bind(email).first(),
      { queryName: 'db-query' }
    );

    if (existingCustomer) {
      return c.json({ error: 'Customer with this email already exists' }, 409);
    }

    // Create customer
    const customerId = generateId('cus');
    const now = new Date().toISOString();
    
    await dbSession.query(
      (session) => session.prepare(`
      INSERT INTO customers (
        id, email, first_name, last_name, phone, date_of_birth,
        group_id, accepts_marketing, tax_exempt, metadata, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `).bind(
      customerId,
      email,
      first_name || null,
      last_name || null,
      phone || null,
      date_of_birth || null,
      group_id || null,
      accepts_marketing ? 1 : 0,
      tax_exempt ? 1 : 0,
      metadata ? JSON.stringify(metadata) : null,
      now,
      now
    ).run(),
      { usePrimary: true, queryName: 'db-write' }
    );

    // Get created customer
    const customer = await dbSession.query(
      (session) => session.prepare(`
      SELECT * FROM customers WHERE id = ?
    `).bind(customerId).first(),
      { queryName: 'db-query' }
    );

    return c.json({ 
      success: true, 
      data: customer,
      message: 'Customer created successfully' 
    });

  } catch (error) {
    return handleError(c, error, 'Failed to create customer');
  }
});

// Update customer
app.put('/api/customers/:id', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const id = c.req.param('id');
    const body = await c.req.json();
    const { 
      first_name, 
      last_name, 
      phone, 
      date_of_birth,
      group_id,
      accepts_marketing,
      tax_exempt,
      metadata 
    } = body;

    // Verify customer exists
    const customer = await dbSession.query(
      (session) => session.prepare(`
      SELECT id FROM customers WHERE id = ?
    `).bind(id).first(),
      { queryName: 'db-query' }
    );

    if (!customer) {
      return c.json({ error: 'Customer not found' }, 404);
    }

    const now = new Date().toISOString();
    const updateData: any = { updated_at: now };

    // Update fields
    if (first_name !== undefined) updateData.first_name = first_name;
    if (last_name !== undefined) updateData.last_name = last_name;
    if (phone !== undefined) updateData.phone = phone;
    if (date_of_birth !== undefined) updateData.date_of_birth = date_of_birth;
    if (group_id !== undefined) updateData.group_id = group_id;
    if (accepts_marketing !== undefined) updateData.accepts_marketing = accepts_marketing ? 1 : 0;
    if (tax_exempt !== undefined) updateData.tax_exempt = tax_exempt ? 1 : 0;
    if (metadata !== undefined) updateData.metadata = JSON.stringify(metadata);

    // Update customer
    const updateFields = Object.keys(updateData).map(key => `${key} = ?`).join(', ');
    const updateValues = Object.values(updateData);
    
    await dbSession.query(
      (session) => session.prepare(`
      UPDATE customers SET ${updateFields} WHERE id = ?
    `).bind(...updateValues, id).run(),
      { usePrimary: true, queryName: 'db-write' }
    );

    // Get updated customer
    const updatedCustomer = await dbSession.query(
      (session) => session.prepare(`
      SELECT * FROM customers WHERE id = ?
    `).bind(id).first(),
      { queryName: 'db-query' }
    );

    return c.json({ 
      success: true, 
      data: updatedCustomer,
      message: 'Customer updated successfully' 
    });

  } catch (error) {
    return handleError(c, error, 'Failed to update customer');
  }
});

// Delete customer (HARD DELETE)
app.delete('/api/customers/:id', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const id = c.req.param('id');

    // Verify customer exists
    const customer = await dbSession.query(
      (session) => session.prepare(`
      SELECT id FROM customers WHERE id = ?
    `).bind(id).first(),
      { queryName: 'db-query' }
    );

    if (!customer) {
      return c.json({ error: 'Customer not found' }, 404);
    }

    // Hard delete customer addresses first (foreign key constraint)
    await dbSession.query(
      (session) => session.prepare(`
      DELETE FROM customer_addresses WHERE customer_id = ?
    `).bind(id).run(),
      { usePrimary: true, queryName: 'db-write' }
    );

    // Hard delete customer carts (foreign key constraint)
    // Note: cart_items will be automatically deleted due to ON DELETE CASCADE
    await dbSession.query(
      (session) => session.prepare(`
      DELETE FROM carts WHERE customer_id = ?
    `).bind(id).run(),
      { usePrimary: true, queryName: 'db-write' }
    );

    // Update orders to remove customer reference (preserve order history)
    await dbSession.query(
      (session) => session.prepare(`
      UPDATE orders SET customer_id = NULL WHERE customer_id = ?
    `).bind(id).run(),
      { usePrimary: true, queryName: 'db-write' }
    );

    // Hard delete the customer
    await dbSession.query(
      (session) => session.prepare(`
      DELETE FROM customers WHERE id = ?
    `).bind(id).run(),
      { usePrimary: true, queryName: 'db-write' }
    );

    return c.json({ 
      success: true, 
      message: 'Customer deleted successfully',
      deleted: true,
      id: id 
    });

  } catch (error) {
    return handleError(c, error, 'Failed to delete customer');
  }
});

// Bulk delete customers (HARD DELETE)
app.post('/api/customers/bulk-delete', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const { customerIds } = await c.req.json();

    if (!customerIds || !Array.isArray(customerIds) || customerIds.length === 0) {
      return c.json({ error: 'Customer IDs are required' }, 400);
    }

    const placeholders = customerIds.map(() => '?').join(',');
    
    // Hard delete customer addresses first (foreign key constraint)
    await dbSession.query(
      (session) => session.prepare(`
      DELETE FROM customer_addresses
      WHERE customer_id IN (${placeholders})
    `).bind(...customerIds).run(),
      { usePrimary: true, queryName: 'db-write' }
    );

    // Hard delete customer carts (foreign key constraint)
    // Note: cart_items will be automatically deleted due to ON DELETE CASCADE
    await dbSession.query(
      (session) => session.prepare(`
      DELETE FROM carts
      WHERE customer_id IN (${placeholders})
    `).bind(...customerIds).run(),
      { usePrimary: true, queryName: 'db-write' }
    );

    // Update orders to remove customer reference (preserve order history)
    await dbSession.query(
      (session) => session.prepare(`
      UPDATE orders SET customer_id = NULL
      WHERE customer_id IN (${placeholders})
    `).bind(...customerIds).run(),
      { usePrimary: true, queryName: 'db-write' }
    );

    // Hard delete the customers
    await dbSession.query(
      (session) => session.prepare(`
      DELETE FROM customers
      WHERE id IN (${placeholders})
    `).bind(...customerIds).run(),
      { usePrimary: true, queryName: 'db-write' }
    );

    return c.json({ 
      success: true, 
      message: `${customerIds.length} customers deleted successfully`,
      deleted: true,
      count: customerIds.length 
    });

  } catch (error) {
    return handleError(c, error, 'Failed to delete customers');
  }
});

// Get customer orders
app.get('/api/customers/:id/orders', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const customerId = c.req.param('id');
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '10');
    const offset = (page - 1) * limit;

    // Verify customer exists
    const customer = await dbSession.query(
      (session) => session.prepare(`
      SELECT id FROM customers WHERE id = ?
    `).bind(customerId).first(),
      { queryName: 'db-query' }
    );

    if (!customer) {
      return c.json({ error: 'Customer not found' }, 404);
    }

    // Get customer orders
    const ordersQuery = `
      SELECT 
        o.id, o.number, o.status, o.financial_status, o.fulfillment_status,
        o.subtotal, o.tax_amount, o.shipping_amount, o.discount_amount, o.total_amount,
        o.currency_code, o.created_at, o.updated_at
      FROM orders o
      WHERE o.customer_id = ?
      ORDER BY o.created_at DESC
      LIMIT ? OFFSET ?
    `;

    const countQuery = `
      SELECT COUNT(*) as total FROM orders WHERE customer_id = ?
    `;

    const [ordersResult, countResult] = await Promise.all([
      dbSession.query(
        (session) => session.prepare(ordersQuery).bind(customerId, limit, offset).all(),
        { queryName: 'db-query' }
      ),
      dbSession.query(
        (session) => session.prepare(countQuery).bind(customerId).first(),
        { queryName: 'db-query' }
      )
    ]);

    const total = (countResult?.total as number) || 0;

    return c.json({
      success: true,
      data: ordersResult.results,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch customer orders');
  }
});

// ===========================================
// ORDERS ROUTES
// ===========================================

// Get all orders with pagination and filtering
app.get('/api/orders', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '20');
    const status = c.req.query('status');
    const search = c.req.query('search');
    const financial_status = c.req.query('financial_status');
    const fulfillment_status = c.req.query('fulfillment_status');
    const offset = (page - 1) * limit;

    let whereConditions = [];
    let params = [];

    if (status) {
      whereConditions.push('o.status = ?');
      params.push(status);
    }

    if (financial_status) {
      whereConditions.push('o.financial_status = ?');
      params.push(financial_status);
    }

    if (fulfillment_status) {
      whereConditions.push('o.fulfillment_status = ?');
      params.push(fulfillment_status);
    }

    if (search) {
      whereConditions.push('(o.number LIKE ? OR o.email LIKE ? OR c.first_name LIKE ? OR c.last_name LIKE ?)');
      const searchParam = `%${search}%`;
      params.push(searchParam, searchParam, searchParam, searchParam);
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    const ordersQuery = `
      SELECT 
        o.id, o.number, o.customer_id, o.email, o.currency_code, o.region_id,
        o.status, o.financial_status, o.fulfillment_status,
        o.subtotal, o.tax_amount, o.shipping_amount, o.discount_amount, o.total_amount,
        o.created_at, o.updated_at, o.cancelled_at,
        c.first_name, c.last_name, c.email as customer_email
      FROM orders o
      LEFT JOIN customers c ON o.customer_id = c.id
      ${whereClause}
      ORDER BY o.created_at DESC
      LIMIT ? OFFSET ?
    `;

    const countQuery = `
      SELECT COUNT(*) as total
      FROM orders o
      LEFT JOIN customers c ON o.customer_id = c.id
      ${whereClause}
    `;

    const [ordersResult, countResult] = await Promise.all([
      dbSession.query(
        (session) => session.prepare(ordersQuery).bind(...params, limit, offset).all(),
        { queryName: 'db-query' }
      ),
      dbSession.query(
        (session) => session.prepare(countQuery).bind(...params).first(),
        { queryName: 'db-query' }
      )
    ]);

    const total = (countResult?.total as number) || 0;
    const totalPages = Math.ceil(total / limit);

    // Process orders and fetch items for each order
    const processedOrders = [];
    for (const order of (ordersResult.results || [])) {
      // Get order items for each order
      const itemsResult = await dbSession.query(
      (session) => session.prepare(`
        SELECT 
          oi.*,
          pv.title as variant_title,
          pv.sku,
          pt.title as product_title
        FROM order_items oi
        LEFT JOIN product_variants pv ON oi.variant_id = pv.id
        LEFT JOIN products p ON pv.product_id = p.id
        LEFT JOIN product_translations pt ON p.id = pt.product_id AND pt.language_code ='ro'
        WHERE oi.order_id = ?
        ORDER BY oi.created_at ASC
      `).bind(order.id).all(),
      { queryName: 'db-query' }
    );

      processedOrders.push({
        ...order,
        billing_address: order.billing_address ? JSON.parse(String(order.billing_address)) : null,
        shipping_address: order.shipping_address ? JSON.parse(String(order.shipping_address)) : null,
        metadata: order.metadata ? JSON.parse(String(order.metadata)) : {},
        customer: order.customer_id ? {
          id: order.customer_id,
          first_name: order.first_name,
          last_name: order.last_name,
          email: order.customer_email || order.email
        } : null,
        items: itemsResult.results || [],
        payments: [], // Will be populated separately if needed
        fulfillments: [] // Will be populated separately if needed
      });
    }

    return c.json({
      success: true,
      data: processedOrders,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch orders');
  }
});

// Get single order with full details
app.get('/api/orders/:id', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const id = c.req.param('id');

    // Get order details
    const order = await dbSession.query(
      (session) => session.prepare(`
      SELECT 
        o.*,
        c.first_name, c.last_name, c.email as customer_email, c.phone as customer_phone
      FROM orders o
      LEFT JOIN customers c ON o.customer_id = c.id
      WHERE o.id = ? 
    `).bind(id).first(),
      { queryName: 'db-query' }
    );

    if (!order) {
      return c.json({ error: 'Order not found' }, 404);
    }

    // Get order items with proper product title from translations
    const items = await dbSession.query(
      (session) => session.prepare(`
      SELECT 
        oi.*,
        pv.title as variant_title,
        pv.sku,
        pt.title as product_title
      FROM order_items oi
      LEFT JOIN product_variants pv ON oi.variant_id = pv.id
      LEFT JOIN products p ON pv.product_id = p.id
      LEFT JOIN product_translations pt ON p.id = pt.product_id AND pt.language_code ='ro'
      WHERE oi.order_id = ?
      ORDER BY oi.created_at
    `).bind(id).all(),
      { queryName: 'db-query' }
    );

    // Get payments
    const payments = await dbSession.query(
      (session) => session.prepare(`
      SELECT * FROM payments 
      WHERE order_id = ? 
      ORDER BY created_at DESC
    `).bind(id).all(),
      { queryName: 'db-query' }
    );

    // Get fulfillments (if implemented)
    const fulfillments = await dbSession.query(
      (session) => session.prepare(`
      SELECT * FROM fulfillments 
      WHERE order_id = ? 
      ORDER BY created_at DESC
    `).bind(id).all(),
      { queryName: 'db-query' }
    );

    // Helper function to safely parse JSON
    function safeParseJson(value: any): any {
      if (!value) return null;
      if (typeof value === 'string') {
        try {
          return JSON.parse(value);
        } catch {
          return value;
        }
      }
      return value;
    }

    // Helper function to extract and structure easybox information from metadata
    function extractEasyboxInfo(metadata: any): any | null {
      if (!metadata || !metadata.easybox_locker_id) {
        return null;
      }
      
      return {
        locker_id: metadata.easybox_locker_id,
        locker_name: metadata.easybox_locker_name,
        locker_address: metadata.easybox_locker_address,
        locker_city: metadata.easybox_locker_city,
        locker_county: metadata.easybox_locker_county,
        full_address: `${metadata.easybox_locker_name}, ${metadata.easybox_locker_address}, ${metadata.easybox_locker_city}, ${metadata.easybox_locker_county}`
      };
    }

    // Helper function to check if delivery method is easybox
    function isEasyboxDelivery(metadata: any): boolean {
      return !!(metadata && metadata.easybox_locker_id);
    }

    // Parse metadata and extract easybox information
    const metadata = safeParseJson(order.metadata) || {};
    const easyboxInfo = extractEasyboxInfo(metadata);

    // Parse JSON fields
    const orderData = {
      ...order,
      metadata: metadata,
      billing_address: order.billing_address ? JSON.parse(String(order.billing_address)) : null,
      shipping_address: order.shipping_address ? JSON.parse(String(order.shipping_address)) : null,
      customer: order.customer_id ? {
        id: order.customer_id,
        first_name: order.first_name,
        last_name: order.last_name,
        email: order.customer_email,
        phone: order.customer_phone,
      } : null,
      items: items.results,
      payments: payments.results,
      fulfillments: fulfillments.results,
      easybox_delivery: easyboxInfo,
      is_easybox_delivery: isEasyboxDelivery(metadata),
      delivery_method: easyboxInfo ? 'easybox' : 'standard'
    };

    return c.json({ order: orderData });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch order');
  }
});

// Update order status
app.put('/api/orders/:id/status', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const id = c.req.param('id');
    const { status } = await c.req.json();

    const validStatuses = ['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded'];
    if (!validStatuses.includes(status)) {
      return c.json({ error: 'Invalid status' }, 400);
    }

    await dbSession.query(
      (session) => session.prepare(`
      UPDATE orders 
      SET status = ?, updated_at = CURRENT_TIMESTAMP 
      WHERE id = ? AND deleted_at IS NULL
    `).bind(status, id).run(),
      { usePrimary: true, queryName: 'db-write' }
    );

    return c.json({ message: 'Order status updated successfully' });

  } catch (error) {
    return handleError(c, error, 'Failed to update order status');
  }
});

// Fix payment provider for orders (useful for correcting Stripe payments recorded as manual)
app.post('/api/orders/:id/fix-payment', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const id = c.req.param('id');
    const { provider, transaction_id, status = 'completed' } = await c.req.json();

    if (!provider) {
      return c.json({ error: 'Payment provider is required' }, 400);
    }

    // Get the order
    const order = await dbSession.query(
      (session) => session.prepare(`
      SELECT id, total_amount, currency_code FROM orders WHERE id = ?
    `).bind(id).first(),
      { queryName: 'db-query' }
    );

    if (!order) {
      return c.json({ error: 'Order not found' }, 404);
    }

    // Check if payment record exists
    const existingPayment = await dbSession.query(
      (session) => session.prepare(`
      SELECT id FROM payments WHERE order_id = ?
    `).bind(id).first(),
      { queryName: 'db-query' }
    );

    if (existingPayment) {
      // Update existing payment record
      await dbSession.query(
        (session) => session.prepare(`
        UPDATE payments 
        SET provider = ?, provider_transaction_id = ?, status = ?, updated_at = CURRENT_TIMESTAMP 
        WHERE order_id = ?
      `).bind(provider, transaction_id || null, status, id).run(),
        { usePrimary: true, queryName: 'db-write' }
      );
    } else {
      // Create new payment record
      const paymentId = generateId('payment');
      await dbSession.query(
        (session) => session.prepare(`
        INSERT INTO payments (
          id, order_id, provider, provider_transaction_id, type, status, 
          amount, currency_code, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
      `).bind(
        paymentId, id, provider, transaction_id || null, 'payment', 
        status, order.total_amount, order.currency_code
      ).run(),
        { usePrimary: true, queryName: 'db-write' }
      );
    }

    // Update order financial status if payment is completed
    if (status === 'completed') {
      await dbSession.query(
        (session) => session.prepare(`
        UPDATE orders 
        SET financial_status = 'paid', status = 'confirmed', updated_at = CURRENT_TIMESTAMP 
        WHERE id = ?
      `).bind(id).run(),
        { usePrimary: true, queryName: 'db-write' }
      );
    }

    return c.json({ 
      success: true,
      message: `Payment provider updated to ${provider} and order status updated` 
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fix payment provider');
  }
});

// Update order notes
app.put('/api/orders/:id/notes', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const id = c.req.param('id');
    const { notes } = await c.req.json();

    await dbSession.query(
      (session) => session.prepare(`
      UPDATE orders 
      SET notes = ?, updated_at = CURRENT_TIMESTAMP 
      WHERE id = ? AND deleted_at IS NULL
    `).bind(notes || null, id).run(),
      { usePrimary: true, queryName: 'db-write' }
    );

    return c.json({ message: 'Order notes updated successfully' });

  } catch (error) {
    return handleError(c, error, 'Failed to update order notes');
  }
});

// Create fulfillment
app.post('/api/orders/:id/fulfillments', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const orderId = c.req.param('id');
    const { tracking_number, tracking_url, carrier, items } = await c.req.json();

    const fulfillmentId = crypto.randomUUID();

    await c.env.DB.prepare(`
      INSERT INTO fulfillments (
        id, order_id, status, tracking_number, tracking_url, carrier, 
        metadata, created_at, updated_at
      ) VALUES (?, ?, 'shipped', ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `).bind(
      fulfillmentId,
      orderId, 
      tracking_number || null,
      tracking_url || null,
      carrier || null,
      JSON.stringify({ items: items || [] })
    ).run();

    return c.json({ 
      message: 'Fulfillment created successfully',
      fulfillment_id: fulfillmentId 
    });

  } catch (error) {
    return handleError(c, error, 'Failed to create fulfillment');
  }
});

// Update fulfillment tracking
app.put('/api/orders/:orderId/fulfillments/:fulfillmentId', async (c) => {
  try {
    const orderId = c.req.param('orderId');
    const fulfillmentId = c.req.param('fulfillmentId');
    const { tracking_number, tracking_url, carrier, status } = await c.req.json();

    const updateStmt = c.env.DB.prepare(`
      UPDATE fulfillments 
      SET 
        tracking_number = COALESCE(?, tracking_number),
        tracking_url = COALESCE(?, tracking_url),
        carrier = COALESCE(?, carrier),
        status = COALESCE(?, status),
        updated_at = CURRENT_TIMESTAMP
      WHERE id = ? AND order_id = ?
    `);

    const result = await updateStmt.bind(
      tracking_number || null,
      tracking_url || null,
      carrier || null,
      status || null,
      fulfillmentId,
      orderId
    ).run();

    if (!result.success) {
      return c.json({
        success: false,
        error: 'Failed to update fulfillment'
      }, 400);
    }

    return c.json({ 
      success: true,
      message: 'Fulfillment updated successfully'
    });

  } catch (error) {
    return handleError(c, error, 'Failed to update fulfillment');
  }
});

// ===========================================
// RETURNS & EXCHANGES
// ===========================================

// Get all return reasons
app.get('/api/returns/reasons', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const reasons = await dbSession.query(
      (session) => session.prepare(`
      SELECT * FROM return_reasons 
      WHERE is_active = 1 
      ORDER BY sort_order, label
    `).all(),
      { queryName: 'db-query' }
    );

    return c.json({ success: true, data: reasons.results });
  } catch (error) {
    return handleError(c, error, 'Failed to fetch return reasons');
  }
});

// Get returns with pagination and filtering
app.get('/api/returns', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '20');
    const status = c.req.query('status');
    const search = c.req.query('search');
    const sort = c.req.query('sort') || 'created_at:desc';
    const offset = (page - 1) * limit;

    let whereClause = 'WHERE r.cancelled_at IS NULL';
    let queryParams = [];

    if (status) {
      whereClause += ' AND r.status = ?';
      queryParams.push(status);
    }

    if (search) {
      whereClause += ' AND (r.return_number LIKE ? OR o.number LIKE ? OR c.email LIKE ?)';
      const searchTerm = `%${search}%`;
      queryParams.push(searchTerm, searchTerm, searchTerm);
    }

    const [sortField, sortDirection] = sort.split(':');
    const validSortFields = ['created_at', 'return_number', 'status', 'total_amount'];
    const orderBy = validSortFields.includes(sortField) ? sortField : 'created_at';
    const direction = sortDirection === 'asc' ? 'ASC' : 'DESC';

    const query = `
      SELECT 
        r.*,
        o.number as order_number,
        o.customer_id,
        COALESCE(c.email, o.email) as customer_email,
        COALESCE(c.first_name, '') as customer_first_name,
        COALESCE(c.last_name, '') as customer_last_name,
        COUNT(ri.id) as item_count
      FROM returns r
      LEFT JOIN orders o ON r.order_id = o.id
      LEFT JOIN customers c ON o.customer_id = c.id
      LEFT JOIN return_items ri ON r.id = ri.return_id
      ${whereClause}
      GROUP BY r.id
      ORDER BY r.${orderBy} ${direction}
      LIMIT ? OFFSET ?
    `;

    const countQuery = `
      SELECT COUNT(DISTINCT r.id) as total
      FROM returns r
      LEFT JOIN orders o ON r.order_id = o.id
      LEFT JOIN customers c ON o.customer_id = c.id
      ${whereClause}
    `;

    const [results, countResult] = await Promise.all([
      dbSession.query(
        (session) => session.prepare(query).bind(...queryParams, limit, offset).all(),
        { queryName: 'db-query' }
      ),
      dbSession.query(
        (session) => session.prepare(countQuery).bind(...queryParams).first(),
        { queryName: 'db-query' }
      )
    ]);

    const total = (countResult?.total as number) || 0;

    return c.json({
      success: true,
      data: results.results,
      meta: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    return handleError(c, error, 'Failed to fetch returns');
  }
});

// Get single return with details
app.get('/api/returns/:id', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const returnId = c.req.param('id');

    const returnData = await dbSession.query(
   (session) => session.prepare(`
      SELECT 
        r.*,
        o.number as order_number,
        o.customer_id,
        o.total_amount as order_total,
        o.currency_code as order_currency,
        COALESCE(c.email, o.email) as customer_email,
        COALESCE(c.first_name, '') as customer_first_name,
        COALESCE(c.last_name, '') as customer_last_name
      FROM returns r
      LEFT JOIN orders o ON r.order_id = o.id
      LEFT JOIN customers c ON o.customer_id = c.id
      WHERE r.id = ?
    `).bind(returnId).first(),
   { queryName: 'db-query' }
 );

    if (!returnData) {
      return c.json({ error: 'Return not found' }, 404);
    }

    // Get return items with product details
    const returnItems = await dbSession.query(
      (session) => session.prepare(`
      SELECT 
        ri.*,
        oi.product_title,
        oi.variant_title,
        oi.unit_price,
        oi.total_price as original_total,
        rr.label as reason_label,
        rr.description as reason_description
      FROM return_items ri
      LEFT JOIN order_items oi ON ri.order_item_id = oi.id
      LEFT JOIN return_reasons rr ON ri.reason_id = rr.id
      WHERE ri.return_id = ?
      ORDER BY ri.created_at
    `).bind(returnId).all(),
      { queryName: 'db-query' }
    );

    return c.json({
      success: true,
      data: {
        ...returnData,
        items: returnItems.results
      }
    });
  } catch (error) {
    return handleError(c, error, 'Failed to fetch return');
  }
});

// Create new return
app.post('/api/returns', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const body = await c.req.json();
    const { 
      order_id, 
      items, // Array of {order_item_id, reason_id, quantity, note}
      customer_note,
      admin_note 
    } = body;

    if (!order_id || !items || !items.length) {
      return c.json({ error: 'Order ID and items are required' }, 400);
    }

    // Verify order exists
    const order = await dbSession.query(
      (session) => session.prepare('SELECT * FROM orders WHERE id = ?').bind(order_id).first(),
      { queryName: 'db-query' }
    );
    if (!order) {
      return c.json({ error: 'Order not found' }, 404);
    }

    const returnId = generateId('ret');
    const returnNumber = `RET-${Date.now()}`;

    // Calculate total refund amount
    let totalRefund = 0;
    for (const item of items) {
      const orderItem = await dbSession.query(
      (session) => session.prepare('SELECT * FROM order_items WHERE id = ?').bind(item.order_item_id).first(),
      { queryName: 'db-query' }
    );
      if (orderItem) {
        totalRefund += ((orderItem.unit_price as number) * item.quantity);
      }
    }

    // Create return
    await dbSession.query(
      (session) => session.prepare(`
      INSERT INTO returns (
        id, order_id, status, return_number, customer_note, admin_note,
        refund_amount, total_amount, currency_code, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `).bind(
      returnId, order_id, 'requested', returnNumber, customer_note || null, admin_note || null,
      totalRefund, totalRefund, order.currency_code
    ).run(),
      { usePrimary: true, queryName: 'db-write' }
    );

    // Create return items
    for (const item of items) {
      const itemId = generateId('ret_item');
      const orderItem = await dbSession.query(
      (session) => session.prepare('SELECT * FROM order_items WHERE id = ?').bind(item.order_item_id).first(),
      { queryName: 'db-query' }
    );
      const refundAmount = orderItem ? ((orderItem.unit_price as number) * item.quantity) : 0;

      await dbSession.query(
      (session) => session.prepare(`
        INSERT INTO return_items (
          id, return_id, order_item_id, reason_id, quantity, note, refund_amount, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      `).bind(
        itemId, returnId, item.order_item_id, item.reason_id, item.quantity, item.note || null, refundAmount
      ).run(),
      { usePrimary: true, queryName: 'db-write' }
    );
    }

    return c.json({ 
      success: true, 
      data: { id: returnId, return_number: returnNumber },
      message: 'Return created successfully' 
    }, 201);
  } catch (error) {
    return handleError(c, error, 'Failed to create return');
  }
});

// Update return status
app.put('/api/returns/:id/status', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const returnId = c.req.param('id');
    const { status, admin_note, tracking_number, tracking_url } = await c.req.json();

    const validStatuses = ['requested', 'approved', 'received', 'processing', 'completed', 'cancelled'];
    if (!validStatuses.includes(status)) {
      return c.json({ error: 'Invalid status' }, 400);
    }

    let updateData = [status];
    let setClauses = ['status = ?'];

    if (admin_note) {
      setClauses.push('admin_note = ?');
      updateData.push(admin_note);
    }

    if (tracking_number) {
      setClauses.push('tracking_number = ?');
      updateData.push(tracking_number);
    }

    if (tracking_url) {
      setClauses.push('tracking_url = ?');
      updateData.push(tracking_url);
    }

    // Add status-specific timestamp updates
    if (status === 'received') {
      setClauses.push('received_at = CURRENT_TIMESTAMP');
    } else if (status === 'processing') {
      setClauses.push('processed_at = CURRENT_TIMESTAMP');
    } else if (status === 'completed') {
      setClauses.push('completed_at = CURRENT_TIMESTAMP');
    } else if (status === 'cancelled') {
      setClauses.push('cancelled_at = CURRENT_TIMESTAMP');
    }

    setClauses.push('updated_at = CURRENT_TIMESTAMP');
    updateData.push(returnId);

    await dbSession.query(


      (session) => session.prepare(`
      UPDATE returns 
      SET ${setClauses.join(', ')}
      WHERE id = ?
    `).bind(...updateData).run(),


      { usePrimary: true, queryName: 'db-write' }


    );

    return c.json({ success: true, message: 'Return status updated successfully' });
  } catch (error) {
    return handleError(c, error, 'Failed to update return status');
  }
});

// Get exchanges with pagination and filtering
app.get('/api/exchanges', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '20');
    const status = c.req.query('status');
    const search = c.req.query('search');
    const sort = c.req.query('sort') || 'created_at:desc';
    const offset = (page - 1) * limit;

    let whereClause = 'WHERE e.cancelled_at IS NULL';
    let queryParams = [];

    if (status) {
      whereClause += ' AND e.status = ?';
      queryParams.push(status);
    }

    if (search) {
      whereClause += ' AND (e.exchange_number LIKE ? OR o.number LIKE ? OR c.email LIKE ?)';
      const searchTerm = `%${search}%`;
      queryParams.push(searchTerm, searchTerm, searchTerm);
    }

    const [sortField, sortDirection] = sort.split(':');
    const validSortFields = ['created_at', 'exchange_number', 'status', 'difference_amount'];
    const orderBy = validSortFields.includes(sortField) ? sortField : 'created_at';
    const direction = sortDirection === 'asc' ? 'ASC' : 'DESC';

    const query = `
      SELECT 
        e.*,
        o.number as order_number,
        o.customer_id,
        COALESCE(c.email, o.email) as customer_email,
        COALESCE(c.first_name, '') as customer_first_name,
        COALESCE(c.last_name, '') as customer_last_name,
        COUNT(DISTINCT eri.id) + COUNT(DISTINCT eni.id) as item_count
      FROM exchanges e
      LEFT JOIN orders o ON e.order_id = o.id
      LEFT JOIN customers c ON o.customer_id = c.id
      LEFT JOIN exchange_return_items eri ON e.id = eri.exchange_id
      LEFT JOIN exchange_new_items eni ON e.id = eni.exchange_id
      ${whereClause}
      GROUP BY e.id
      ORDER BY e.${orderBy} ${direction}
      LIMIT ? OFFSET ?
    `;

    const countQuery = `
      SELECT COUNT(DISTINCT e.id) as total
      FROM exchanges e
      LEFT JOIN orders o ON e.order_id = o.id
      LEFT JOIN customers c ON o.customer_id = c.id
      ${whereClause}
    `;

    const [results, countResult] = await Promise.all([
      dbSession.query(
        (session) => session.prepare(query).bind(...queryParams, limit, offset).all(),
        { queryName: 'db-query' }
      ),
      dbSession.query(
        (session) => session.prepare(countQuery).bind(...queryParams).first(),
        { queryName: 'db-query' }
      )
    ]);

    const total = (countResult?.total as number) || 0;

    return c.json({
      success: true,
      data: results.results,
      meta: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    return handleError(c, error, 'Failed to fetch exchanges');
  }
});

// Get single exchange with details
app.get('/api/exchanges/:id', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const exchangeId = c.req.param('id');

    const exchangeData = await dbSession.query(
   (session) => session.prepare(`
      SELECT 
        e.*,
        o.number as order_number,
        o.customer_id,
        o.total_amount as order_total,
        o.currency_code as order_currency,
        COALESCE(c.email, o.email) as customer_email,
        COALESCE(c.first_name, '') as customer_first_name,
        COALESCE(c.last_name, '') as customer_last_name
      FROM exchanges e
      LEFT JOIN orders o ON e.order_id = o.id
      LEFT JOIN customers c ON o.customer_id = c.id
      WHERE e.id = ?
    `).bind(exchangeId).first(),
   { queryName: 'db-query' }
 );

    if (!exchangeData) {
      return c.json({ error: 'Exchange not found' }, 404);
    }

    // Get return items
    const returnItems = await dbSession.query(
      (session) => session.prepare(`
      SELECT 
        eri.*,
        oi.product_title,
        oi.variant_title,
        oi.unit_price,
        oi.total_price as original_total,
        rr.label as reason_label
      FROM exchange_return_items eri
      LEFT JOIN order_items oi ON eri.order_item_id = oi.id
      LEFT JOIN return_reasons rr ON eri.reason_id = rr.id
      WHERE eri.exchange_id = ?
      ORDER BY eri.created_at
    `).bind(exchangeId).all(),
      { queryName: 'db-query' }
    );

    // Get new items
    const newItems = await dbSession.query(
      (session) => session.prepare(`
      SELECT 
        eni.*,
        pt.title as product_title,
        pv.title as variant_title,
        pv.sku
      FROM exchange_new_items eni
      LEFT JOIN product_variants pv ON eni.variant_id = pv.id
      LEFT JOIN products p ON pv.product_id = p.id
      LEFT JOIN product_translations pt ON p.id = pt.product_id AND pt.language_code ='ro'
      WHERE eni.exchange_id = ?
      ORDER BY eni.created_at
    `).bind(exchangeId).all(),
      { queryName: 'db-query' }
    );

    return c.json({
      success: true,
      data: {
        ...exchangeData,
        return_items: returnItems.results,
        new_items: newItems.results
      }
    });
  } catch (error) {
    return handleError(c, error, 'Failed to fetch exchange');
  }
});

// Create new exchange
app.post('/api/exchanges', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const body = await c.req.json();
    const { 
      order_id, 
      return_items, // Array of {order_item_id, reason_id, quantity, note}
      new_items, // Array of {variant_id, quantity}
      customer_note,
      admin_note,
      shipping_address
    } = body;

    if (!order_id || (!return_items?.length && !new_items?.length)) {
      return c.json({ error: 'Order ID and items are required' }, 400);
    }

    // Verify order exists
    const order = await dbSession.query(
      (session) => session.prepare('SELECT * FROM orders WHERE id = ?').bind(order_id).first(),
      { queryName: 'db-query' }
    );
    if (!order) {
      return c.json({ error: 'Order not found' }, 404);
    }

    const exchangeId = generateId('exc');
    const exchangeNumber = `EXC-${Date.now()}`;

    // Calculate difference amount
    let returnValue = 0;
    let newItemsValue = 0;

    // Calculate return value
    if (return_items?.length) {
      for (const item of return_items) {
        const orderItem = await dbSession.query(
      (session) => session.prepare('SELECT * FROM order_items WHERE id = ?').bind(item.order_item_id).first(),
      { queryName: 'db-query' }
    );
        if (orderItem) {
          returnValue += ((orderItem.unit_price as number) * item.quantity);
        }
      }
    }

    // Calculate new items value
    if (new_items?.length) {
      for (const item of new_items) {
        const variant = await dbSession.query(
      (session) => session.prepare(`
          SELECT vp.price 
          FROM variant_prices vp 
          WHERE vp.variant_id = ? AND vp.currency_code = ?
          ORDER BY vp.created_at DESC 
          LIMIT 1
        `).bind(item.variant_id, order.currency_code).first(),
      { queryName: 'db-query' }
    );
        
        if (variant) {
          newItemsValue += ((variant.price as number) * item.quantity);
        }
      }
    }

    const differenceAmount = newItemsValue - returnValue;

    // Create exchange
    await dbSession.query(
      (session) => session.prepare(`
      INSERT INTO exchanges (
        id, order_id, status, exchange_number, customer_note, admin_note,
        difference_amount, currency_code, shipping_address, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `).bind(
      exchangeId, order_id, 'requested', exchangeNumber, customer_note || null, admin_note || null,
      differenceAmount, order.currency_code, shipping_address ? JSON.stringify(shipping_address) : null
    ).run(),
      { usePrimary: true, queryName: 'db-write' }
    );

    // Create return items
    if (return_items?.length) {
      for (const item of return_items) {
        const itemId = generateId('exc_ret_item');
        await dbSession.query(
      (session) => session.prepare(`
          INSERT INTO exchange_return_items (
            id, exchange_id, order_item_id, reason_id, quantity, note, created_at
          ) VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        `).bind(
          itemId, exchangeId, item.order_item_id, item.reason_id || null, item.quantity, item.note || null
        ).run(),
      { usePrimary: true, queryName: 'db-write' }
    );
      }
    }

    // Create new items
    if (new_items?.length) {
      for (const item of new_items) {
        const itemId = generateId('exc_new_item');
        const variant = await dbSession.query(
      (session) => session.prepare(`
          SELECT vp.price 
          FROM variant_prices vp 
          WHERE vp.variant_id = ? AND vp.currency_code = ?
          ORDER BY vp.created_at DESC 
          LIMIT 1
        `).bind(item.variant_id, order.currency_code).first(),
      { queryName: 'db-query' }
    );
        
        const unitPrice = variant ? (variant.price as number) : 0;
        const totalPrice = unitPrice * item.quantity;

        await dbSession.query(
      (session) => session.prepare(`
          INSERT INTO exchange_new_items (
            id, exchange_id, variant_id, quantity, unit_price, total_price, note, created_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        `).bind(
          itemId, exchangeId, item.variant_id, item.quantity, unitPrice, totalPrice, item.note || null
        ).run(),
      { usePrimary: true, queryName: 'db-write' }
    );
      }
    }

    return c.json({ 
      success: true, 
      data: { id: exchangeId, exchange_number: exchangeNumber, difference_amount: differenceAmount },
      message: 'Exchange created successfully' 
    }, 201);
  } catch (error) {
    return handleError(c, error, 'Failed to create exchange');
  }
});

// Update exchange status
app.put('/api/exchanges/:id/status', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const exchangeId = c.req.param('id');
    const { status, admin_note, tracking_number, tracking_url } = await c.req.json();

    const validStatuses = ['requested', 'approved', 'processing', 'shipped', 'completed', 'cancelled'];
    if (!validStatuses.includes(status)) {
      return c.json({ error: 'Invalid status' }, 400);
    }

    let updateData = [status];
    let setClauses = ['status = ?'];

    if (admin_note) {
      setClauses.push('admin_note = ?');
      updateData.push(admin_note);
    }

    if (tracking_number) {
      setClauses.push('tracking_number = ?');
      updateData.push(tracking_number);
    }

    if (tracking_url) {
      setClauses.push('tracking_url = ?');
      updateData.push(tracking_url);
    }

    // Add status-specific timestamp updates
    if (status === 'shipped') {
      setClauses.push('shipped_at = CURRENT_TIMESTAMP');
    } else if (status === 'completed') {
      setClauses.push('completed_at = CURRENT_TIMESTAMP');
    } else if (status === 'cancelled') {
      setClauses.push('cancelled_at = CURRENT_TIMESTAMP');
    }

    setClauses.push('updated_at = CURRENT_TIMESTAMP');
    updateData.push(exchangeId);

    await dbSession.query(


      (session) => session.prepare(`
      UPDATE exchanges 
      SET ${setClauses.join(', ')}
      WHERE id = ?
    `).bind(...updateData).run(),


      { usePrimary: true, queryName: 'db-write' }


    );

    return c.json({ success: true, message: 'Exchange status updated successfully' });
  } catch (error) {
    return handleError(c, error, 'Failed to update exchange status');
  }
});

// ===========================================
// SETTINGS ROUTES
// ===========================================

// Get all settings
app.get('/api/settings', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const category = c.req.query('category');
    
    let query = 'SELECT * FROM settings WHERE 1=1';
    const params: any[] = [];
    
    if (category) {
      query += ' AND category = ?';
      params.push(category);
    }
    
    query += ' ORDER BY category, key';
    
    const settings = await dbSession.query(
      (session) => session.prepare(query).bind(...params).all(),
      { queryName: 'db-query' }
    );
    
    // Group settings by category
    const grouped: any = {};
    settings.results.forEach((setting: any) => {
      if (!grouped[setting.category]) {
        grouped[setting.category] = {};
      }
      try {
        grouped[setting.category][setting.key] = JSON.parse(String(setting.value));
      } catch {
        grouped[setting.category][setting.key] = setting.value;
      }
    });
    
    return c.json({ settings: category ? grouped[category] || {} : grouped });
    
  } catch (error) {
    return handleError(c, error, 'Failed to fetch settings');
  }
});

// Get specific setting
// app.get('/api/settings/:key', async (c) => {
//   try {
//     const key = c.req.param('key');
    
//     const setting = await dbSession.query(
      (session) => session.prepare((`
//       SELECT * FROM settings WHERE key = ?
//     `).bind(key).first(),
      { queryName: 'db-query' }
    );
    
//     if (!setting) {
//       return c.json({ error: 'Setting not found' }, 404);
//     }
    
//     let value;
//     try {
//       value = JSON.parse(String(setting.value));
//     } catch {
//       value = setting.value;
//     }
    
//     return c.json({ 
//       key: setting.key,
//       value,
//       category: setting.category,
//       is_public: setting.is_public,
//       updated_at: setting.updated_at
//     });
    
//   } catch (error) {
//     return handleError(c, error, 'Failed to fetch setting');
//   }
// });

// Update settings (bulk update)
app.put('/api/settings', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const { settings, category } = await c.req.json();
    
    if (!settings) {
      return c.json({ error: 'Settings object is required' }, 400);
    }
    
    // Begin transaction by preparing all statements
    const statements = [];
    
    for (const [key, value] of Object.entries(settings)) {
      const jsonValue = JSON.stringify(value);
      
      statements.push(
        c.env.DB.prepare(`
          INSERT INTO settings (key, value, category, updated_at)
          VALUES (?, ?, ?, CURRENT_TIMESTAMP)
          ON CONFLICT(key) DO UPDATE SET 
            value = excluded.value,
            category = COALESCE(excluded.category, category),
            updated_at = CURRENT_TIMESTAMP
        `).bind(key, jsonValue, category || 'general')
      );
    }
    
    // Execute all statements
    for (const stmt of statements) {
      await stmt.run();
    }
    
    return c.json({ message: 'Settings updated successfully' });
    
  } catch (error) {
    return handleError(c, error, 'Failed to update settings');
  }
});

// Update single setting
app.put('/api/settings/:key', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const key = c.req.param('key');
    const { value, category, is_public } = await c.req.json();
    
    const jsonValue = JSON.stringify(value);
    
    await dbSession.query(
      (session) => session.prepare(`
      INSERT INTO settings (key, value, category, is_public, updated_at)
      VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)
      ON CONFLICT(key) DO UPDATE SET 
        value = excluded.value,
        category = COALESCE(excluded.category, category),
        is_public = COALESCE(excluded.is_public, is_public),
        updated_at = CURRENT_TIMESTAMP
    `).bind(key, jsonValue, category || 'general', is_public || false).run(),
      { usePrimary: true, queryName: 'db-write' }
    );
    
    return c.json({ message: 'Setting updated successfully' });
    
  } catch (error) {
    return handleError(c, error, 'Failed to update setting');
  }
});

// Delete setting
app.delete('/api/settings/:key', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const key = c.req.param('key');
    
    await dbSession.query(
      (session) => session.prepare(`
      DELETE FROM settings WHERE key = ?
    `).bind(key).run(),
      { usePrimary: true, queryName: 'db-write' }
    );
    
    return c.json({ message: 'Setting deleted successfully' });
    
  } catch (error) {
    return handleError(c, error, 'Failed to delete setting');
  }
});

// Get all currencies
app.get('/api/currencies', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const currenciesResult = await dbSession.query(
      (session) => session.prepare(`
      SELECT * FROM currencies ORDER BY code
    `).all(),
      { queryName: 'db-query' }
    );
    
    return c.json({ currencies: currenciesResult.results });
    
  } catch (error) {
    return handleError(c, error, 'Failed to fetch currencies');
  }
});

// Create/Update currency
app.post('/api/currencies', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const body = await c.req.json();
    const { code, name, symbol, symbol_native, decimal_digits, /* exchange_rate, */ is_active, is_default } = body; // decimal_places from schema, but route uses decimal_digits

    if (!code || !name || !symbol) {
      return c.json({ error: 'Code, name, and symbol are required' }, 400);
    }

    const upperCode = String(code).toUpperCase();
    // const nativeSymbol = symbol_native || symbol; // Schema has symbol_native as NOT NULL, but not in simplified.ts body destructuring
    // const digits = typeof decimal_digits === 'number' && !isNaN(decimal_digits) ? decimal_digits : 2; // Schema uses decimal_places
    const decimalPlaces = typeof decimal_digits === 'number' && !isNaN(decimal_digits) ? decimal_digits : (typeof body.decimal_places === 'number' && !isNaN(body.decimal_places) ? body.decimal_places : 2) ;
    const isActive = is_active === true || is_active === 1 || String(is_active).toLowerCase() === 'true';
    const shouldBeDefault = is_default === true || is_default === 1 || String(is_default).toLowerCase() === 'true';
    const exchangeRate = typeof body.exchange_rate === 'number' && !isNaN(body.exchange_rate) ? body.exchange_rate : 1.0;


    // If this currency is being set as default, unset any other existing default currency
    if (shouldBeDefault) {
      await dbSession.query(
      (session) => session.prepare(`
        UPDATE currencies SET is_default = 0 WHERE is_default = 1 AND code != ?
      `).bind(upperCode).run(),
      { usePrimary: true, queryName: 'db-write' }
    );
    }

    // Try to update existing currency
    // Note: simplified_ecommerce_schema.sql for `currencies` does not have rounding or raw_rounding, but has decimal_places, exchange_rate, is_active
    const updateResult = await dbSession.query(
      (session) => session.prepare(`
      UPDATE currencies 
      SET name = ?, 
          symbol = ?, 
          -- symbol_native = ?,  -- Not in simplified.ts body, but in schema as NOT NULL
          decimal_places = ?, 
          exchange_rate = ?, 
          is_active = ?,
          is_default = ?,
          updated_at = CURRENT_TIMESTAMP
      WHERE code = ?
    `).bind(
      name,
      symbol,
      // nativeSymbol, 
      decimalPlaces,
      exchangeRate,
      isActive ? 1 : 0,
      shouldBeDefault ? 1 : 0,
      upperCode
    ).run(),
      { usePrimary: true, queryName: 'db-write' }
    );

    // If no rows were updated (currency didn't exist), insert a new one
    if (updateResult.meta.changes === 0) {
      await dbSession.query(
      (session) => session.prepare(`
        INSERT INTO currencies (
          code, name, symbol, decimal_places, exchange_rate, is_default, is_active, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
      `).bind(
        upperCode,
        name,
        symbol,
        decimalPlaces,
        exchangeRate,
        shouldBeDefault ? 1 : 0,
        isActive ? 1 : 0
      ).run(),
      { usePrimary: true, queryName: 'db-write' }
    );
    }
    
    if (shouldBeDefault) {
        await dbSession.query(

          (session) => session.prepare('UPDATE currencies SET is_default = 1 WHERE code = ?').bind(upperCode).run(),

          { usePrimary: true, queryName: 'db-write' }

        );
    }

    return c.json({ message: 'Currency saved successfully' });

  } catch (error) {
    return handleError(c, error, 'Failed to save currency');
  }
});

// Get all regions
app.get('/api/regions', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const regions = await dbSession.query(
      (session) => session.prepare(`
      SELECT * FROM region ORDER BY name
    `).all(),
      { queryName: 'db-query' }
    );
    
    return c.json({ regions: regions.results });
    
  } catch (error) {
    return handleError(c, error, 'Failed to fetch regions');
  }
});

// Create/Update region
app.post('/api/regions', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const { id, name, currency_code, automatic_taxes, metadata } = await c.req.json();
    
    if (!name || !currency_code) {
      return c.json({ error: 'Name and currency code are required' }, 400);
    }
    
    const regionId = id || generateId('reg');
    
    await c.env.DB.prepare(`
      INSERT INTO region (
        id, name, currency_code, automatic_taxes, metadata, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      ON CONFLICT(id) DO UPDATE SET 
        name = excluded.name,
        currency_code = excluded.currency_code,
        automatic_taxes = excluded.automatic_taxes,
        metadata = excluded.metadata,
        updated_at = CURRENT_TIMESTAMP
    `).bind(
      regionId,
      name,
      currency_code,
      automatic_taxes || 1,
      JSON.stringify(metadata || {})
    ).run();
    
    return c.json({ id: regionId, message: 'Region saved successfully' });
    
  } catch (error) {
    return handleError(c, error, 'Failed to save region');
  }
});

// Get tax rates
app.get('/api/tax-rates', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const taxRates = await dbSession.query(
      (session) => session.prepare(`
      SELECT 
        tr.*,
        treg.country_code,
        treg.province_code
      FROM tax_rate tr
      LEFT JOIN tax_region treg ON tr.tax_region_id = treg.id
      WHERE tr.deleted_at IS NULL
      ORDER BY tr.name
    `).all(),
      { queryName: 'db-query' }
    );
    
    return c.json({ tax_rates: taxRates.results });
    
  } catch (error) {
    return handleError(c, error, 'Failed to fetch tax rates');
  }
});

// Create tax rate
app.post('/api/tax-rates', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const { name, code, rate, country_code, province_code, is_default, is_combinable } = await c.req.json();
    
    if (!name || !code || rate === undefined) {
      return c.json({ error: 'Name, code, and rate are required' }, 400);
    }
    
    // Create tax region first
    const taxRegionId = generateId('treg');
    await dbSession.query(
      (session) => session.prepare(`
      INSERT INTO tax_region (
        id, country_code, province_code, created_at, updated_at
      ) VALUES (?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `).bind(taxRegionId, country_code, province_code).run(),
      { usePrimary: true, queryName: 'db-write' }
    );
    
    // Create tax rate
    const taxRateId = generateId('tr');
    await dbSession.query(
      (session) => session.prepare(`
      INSERT INTO tax_rate (
        id, rate, code, name, is_default, is_combinable, tax_region_id, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `).bind(
      taxRateId,
      rate,
      code,
      name,
      is_default || 0,
      is_combinable || 0,
      taxRegionId
    ).run(),
      { usePrimary: true, queryName: 'db-write' }
    );
    
    return c.json({ id: taxRateId, message: 'Tax rate created successfully' });
    
  } catch (error) {
    return handleError(c, error, 'Failed to create tax rate');
  }
});

// Get shipping zones
app.get('/api/shipping-zones', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const zones = await dbSession.query(
      (session) => session.prepare(`
      SELECT * FROM service_zone ORDER BY name
    `).all(),
      { queryName: 'db-query' }
    );
    
    return c.json({ shipping_zones: zones.results });
    
  } catch (error) {
    return handleError(c, error, 'Failed to fetch shipping zones');
  }
});

// Create shipping zone
app.post('/api/shipping-zones', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const { name, fulfillment_set_id, metadata } = await c.req.json();
    
    if (!name) {
      return c.json({ error: 'Name is required' }, 400);
    }
    
    const zoneId = generateId('sz');
    
    await c.env.DB.prepare(`
      INSERT INTO service_zone (
        id, name, fulfillment_set_id, metadata, created_at, updated_at
      ) VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `).bind(
      zoneId,
      name,
      fulfillment_set_id || null,
      JSON.stringify(metadata || {})
    ).run();
    
    return c.json({ id: zoneId, message: 'Shipping zone created successfully' });
    
  } catch (error) {
    return handleError(c, error, 'Failed to create shipping zone');
  }
});

// Get payment providers
app.get('/api/payment-providers', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const providers = await dbSession.query(
      (session) => session.prepare(`
      SELECT * FROM payment_provider ORDER BY id
    `).all(),
      { queryName: 'db-query' }
    );
    
    return c.json({ payment_providers: providers.results });
    
  } catch (error) {
    return handleError(c, error, 'Failed to fetch payment providers');
  }
});

// Create payment provider
app.post('/api/payment-providers', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const data = await c.req.json();
    const { id, is_enabled = true, metadata = {} } = data;

    if (!id) {
      return c.json({ success: false, error: 'Provider ID is required' }, 400);
    }

    // Check if provider already exists
    const existingProvider = await dbSession.query(
      (session) => session.prepare(`
      SELECT id FROM payment_provider WHERE id = ?
    `).bind(id).first(),
      { queryName: 'db-query' }
    );

    if (existingProvider) {
      return c.json({ success: false, error: 'Payment provider already exists' }, 400);
    }

    const now = new Date().toISOString();

    try {
      // Try to create payment provider with metadata column
      await dbSession.query(
      (session) => session.prepare(`
        INSERT INTO payment_provider (id, is_enabled, metadata, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?)
      `).bind(
        id,
        is_enabled ? 1 : 0,
        JSON.stringify(metadata),
        now,
        now
      ).run(),
      { usePrimary: true, queryName: 'db-write' }
    );
    } catch (metadataError: any) {
      // If metadata column doesn't exist, try without it
      if (metadataError.message && metadataError.message.includes('no column named metadata')) {
        console.log('metadata column not found, creating provider without metadata');
        
        await dbSession.query(
      (session) => session.prepare(`
          INSERT INTO payment_provider (id, is_enabled, created_at, updated_at)
          VALUES (?, ?, ?, ?)
        `).bind(
          id,
          is_enabled ? 1 : 0,
          now,
          now
        ).run(),
      { usePrimary: true, queryName: 'db-write' }
    );
      } else {
        // Re-throw the error if it's not about the metadata column
        throw metadataError;
      }
    }

    // Get created provider
    const createdProvider = await dbSession.query(
      (session) => session.prepare(`
      SELECT * FROM payment_provider WHERE id = ?
    `).bind(id).first(),
      { queryName: 'db-query' }
    );

    return c.json({
      success: true,
      data: createdProvider,
      message: 'Payment provider created successfully'
    });

  } catch (error) {
    return handleError(c, error, 'Failed to create payment provider');
  }
});

// Update payment provider
app.put('/api/payment-providers/:id', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const providerId = c.req.param('id');
    const data = await c.req.json();

    // Check if provider exists
    const existingProvider = await dbSession.query(
      (session) => session.prepare(`
      SELECT id FROM payment_provider WHERE id = ?
    `).bind(providerId).first(),
      { queryName: 'db-query' }
    );

    if (!existingProvider) {
      return c.json({ success: false, error: 'Payment provider not found' }, 404);
    }

    const updateFields = [];
    const updateValues = [];

    // Always update is_enabled if provided
    if (data.is_enabled !== undefined) {
      updateFields.push('is_enabled = ?');
      updateValues.push(data.is_enabled ? 1 : 0);
    }

    // Update metadata if provided
    if (data.metadata !== undefined) {
      updateFields.push('metadata = ?');
      updateValues.push(JSON.stringify(data.metadata));
    }

    // Always update timestamp
    updateFields.push('updated_at = CURRENT_TIMESTAMP');

    if (updateFields.length === 1) { // Only timestamp field
      return c.json({ success: false, error: 'No fields to update' }, 400);
    }

    // Add the provider ID for WHERE clause
    updateValues.push(providerId);

    await dbSession.query(


      (session) => session.prepare(`
      UPDATE payment_provider
      SET ${updateFields.join(', ')}
      WHERE id = ?
    `).bind(...updateValues).run(),


      { usePrimary: true, queryName: 'db-write' }


    );

    return c.json({ 
      success: true,
      message: 'Payment provider updated successfully' 
    });

  } catch (error) {
    return handleError(c, error, 'Failed to update payment provider');
  }
});

// Delete payment provider
app.delete('/api/payment-providers/:id', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const providerId = c.req.param('id');

    // Check if provider exists
    const provider = await dbSession.query(
      (session) => session.prepare(`
      SELECT id FROM payment_provider WHERE id = ?
    `).bind(providerId).first(),
      { queryName: 'db-query' }
    );

    if (!provider) {
      return c.json({ success: false, error: 'Payment provider not found' }, 404);
    }

    // Check if provider is in use (has active payment sessions)
    const activeSessions = await dbSession.query(
      (session) => session.prepare(`
      SELECT COUNT(*) as count FROM payment_sessions 
      WHERE provider_id = ? AND deleted_at IS NULL
    `).bind(providerId).first(),
      { queryName: 'db-query' }
    );

    if (activeSessions && (activeSessions.count as number) > 0) {
      return c.json({ 
        success: false, 
        error: 'Cannot delete payment provider that has active payment sessions' 
      }, 400);
    }

    // Soft delete the payment provider
    await dbSession.query(
      (session) => session.prepare(`
      UPDATE payment_provider 
      SET deleted_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP 
      WHERE id = ?
    `).bind(providerId).run(),
      { usePrimary: true, queryName: 'db-write' }
    );

    return c.json({
      success: true,
      message: 'Payment provider deleted successfully'
    });

  } catch (error) {
    return handleError(c, error, 'Failed to delete payment provider');
  }
});

// Get webhooks
app.get('/api/webhooks', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const webhooks = await dbSession.query(
      (session) => session.prepare(`
      SELECT id, name, url, events, is_active, last_success_at, last_error, created_at, updated_at
      FROM webhook_deliveries
      ORDER BY created_at DESC
    `).all(),
      { queryName: 'db-query' }
    );
    
    return c.json({ webhooks: webhooks.results || [] });
    
  } catch (error) {
    return handleError(c, error, 'Failed to fetch webhooks');
  }
});

// Create webhook
app.post('/api/webhooks', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const { name, url, events, is_active } = await c.req.json();
    
    if (!name || !url || !events) {
      return c.json({ error: 'Name, URL, and events are required' }, 400);
    }
    
    const webhookId = generateId('wh');
    const secret = generateId('whsec'); // Generate webhook secret
    
    await dbSession.query(
      (session) => session.prepare(`
      INSERT INTO webhooks (
        id, name, url, events, is_active, secret, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `).bind(
      webhookId,
      name,
      url,
      JSON.stringify(events),
      is_active ? 1 : 0,
      secret
    ).run(),
      { usePrimary: true, queryName: 'db-write' }
    );
    
    return c.json({ 
      id: webhookId, 
      secret,
      message: 'Webhook created successfully' 
    });
    
  } catch (error) {
    return handleError(c, error, 'Failed to create webhook');
  }
});

// Test webhook
app.post('/api/webhooks/:id/test', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const id = c.req.param('id');
    
    const webhook = await dbSession.query(
      (session) => session.prepare(`
      SELECT * FROM webhooks WHERE id = ?
    `).bind(id).first(),
      { queryName: 'db-query' }
    );
    
    if (!webhook) {
      return c.json({ error: 'Webhook not found' }, 404);
    }
    
    // Send test payload
    const testPayload = {
      event: 'webhook.test',
      data: {
        message: 'This is a test webhook from your store',
        timestamp: new Date().toISOString()
      }
    };
    
    try {
      const response = await fetch(String(webhook.url), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Webhook-Signature': String(webhook.secret)
        },
        body: JSON.stringify(testPayload)
      });
      
      // Log delivery attempt
      await dbSession.query(
      (session) => session.prepare(`
        INSERT INTO webhook_deliveries (
          id, webhook_id, event_type, payload, response_status, delivered_at, created_at
        ) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      `).bind(
        generateId('whd'),
        id,
        'webhook.test',
        JSON.stringify(testPayload),
        response.status
      ).run(),
      { usePrimary: true, queryName: 'db-write' }
    );
      
      if (response.ok) {
        await dbSession.query(
      (session) => session.prepare(`
          UPDATE webhooks 
          SET last_success_at = CURRENT_TIMESTAMP, last_error = NULL, updated_at = CURRENT_TIMESTAMP
          WHERE id = ?
        `).bind(id).run(),
      { usePrimary: true, queryName: 'db-write' }
    );
        
        return c.json({ message: 'Webhook test successful', status: response.status });
      } else {
        const errorText = await response.text();
        await dbSession.query(
      (session) => session.prepare(`
          UPDATE webhooks 
          SET last_error = ?, updated_at = CURRENT_TIMESTAMP
          WHERE id = ?
        `).bind(`HTTP ${response.status}: ${errorText}`, id).run(),
      { usePrimary: true, queryName: 'db-write' }
    );
        
        return c.json({ 
          error: 'Webhook test failed', 
          status: response.status,
          details: errorText 
        }, 400);
      }
      
    } catch (fetchError: any) {
      await dbSession.query(
      (session) => session.prepare(`
        UPDATE webhooks 
        SET last_error = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `).bind(fetchError.message, id).run(),
      { usePrimary: true, queryName: 'db-write' }
    );
      
      return c.json({ 
        error: 'Failed to send webhook', 
        details: fetchError.message 
      }, 400);
    }
    
  } catch (error) {
    return handleError(c, error, 'Failed to test webhook');
  }
});

// Settings schema/structure for frontend
app.get('/api/settings/schema', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const schema = {
      general: {
        title: 'General Settings',
        description: 'Basic store information and defaults',
        fields: {
          store_name: { type: 'text', label: 'Store Name', required: true },
          store_description: { type: 'textarea', label: 'Store Description' },
          store_email: { type: 'email', label: 'Store Email', required: true },
          store_phone: { type: 'tel', label: 'Store Phone' },
          store_address: { type: 'textarea', label: 'Store Address' },
          default_currency: { type: 'select', label: 'Default Currency', options: 'currencies' },
          default_language: { type: 'select', label: 'Default Language', options: 'languages' },
          timezone: { type: 'select', label: 'Timezone', options: 'timezones' }
        }
      },
      localization: {
        title: 'Localization',
        description: 'Multi-language and multi-currency settings',
        fields: {
          enable_multi_currency: { type: 'boolean', label: 'Enable Multi-Currency' },
          enable_multi_language: { type: 'boolean', label: 'Enable Multi-Language' },
          auto_detect_currency: { type: 'boolean', label: 'Auto-detect Currency by Location' },
          auto_detect_language: { type: 'boolean', label: 'Auto-detect Language by Browser' }
        }
      },
      tax: {
        title: 'Tax Settings',
        description: 'Tax calculation and display preferences',
        fields: {
          tax_inclusive: { type: 'boolean', label: 'Prices Include Tax' },
          tax_calculation: { type: 'select', label: 'Tax Calculation', options: [
            { value: 'automatic', label: 'Automatic' },
            { value: 'manual', label: 'Manual' }
          ]},
          display_tax_breakdown: { type: 'boolean', label: 'Show Tax Breakdown to Customers' },
          require_tax_exempt_certificate: { type: 'boolean', label: 'Require Tax Exempt Certificate' }
        }
      },
      inventory: {
        title: 'Inventory Management',
        description: 'Stock tracking and inventory alerts',
        fields: {
          enable_inventory_tracking: { type: 'boolean', label: 'Enable Inventory Tracking' },
          allow_backorders: { type: 'boolean', label: 'Allow Backorders' },
          low_stock_threshold: { type: 'number', label: 'Low Stock Alert Threshold', min: 0 },
          out_of_stock_behavior: { type: 'select', label: 'Out of Stock Behavior', options: [
            { value: 'hide', label: 'Hide Product' },
            { value: 'show_unavailable', label: 'Show as Unavailable' },
            { value: 'allow_backorder', label: 'Allow Backorder' }
          ]},
          send_low_stock_alerts: { type: 'boolean', label: 'Send Low Stock Email Alerts' }
        }
      },
      shipping: {
        title: 'Shipping Settings',
        description: 'Shipping options and fulfillment',
        fields: {
          require_shipping_address: { type: 'boolean', label: 'Require Shipping Address' },
          enable_local_delivery: { type: 'boolean', label: 'Enable Local Delivery' },
          enable_pickup: { type: 'boolean', label: 'Enable Store Pickup' },
          default_shipping_method: { type: 'select', label: 'Default Shipping Method', options: 'shipping_methods' },
          free_shipping_threshold: { type: 'number', label: 'Free Shipping Threshold', min: 0 }
        }
      },
      payments: {
        title: 'Payment Settings',
        description: 'Payment gateway and processing options',
        fields: {
          enable_guest_checkout: { type: 'boolean', label: 'Enable Guest Checkout' },
          require_payment_confirmation: { type: 'boolean', label: 'Require Payment Confirmation' },
          payment_terms: { type: 'textarea', label: 'Payment Terms and Conditions' },
          accepted_payment_methods: { type: 'multiselect', label: 'Accepted Payment Methods', options: [
            { value: 'credit_card', label: 'Credit Card' },
            { value: 'paypal', label: 'PayPal' },
            { value: 'apple_pay', label: 'Apple Pay' },
            { value: 'google_pay', label: 'Google Pay' },
            { value: 'bank_transfer', label: 'Bank Transfer' }
          ]}
        }
      },
      security: {
        title: 'Security Settings',
        description: 'Security and authentication preferences',
        fields: {
          require_account_activation: { type: 'boolean', label: 'Require Email Activation for New Accounts' },
          enable_two_factor: { type: 'boolean', label: 'Enable Two-Factor Authentication' },
          session_timeout: { type: 'number', label: 'Session Timeout (minutes)', min: 1 },
          password_min_length: { type: 'number', label: 'Minimum Password Length', min: 6 },
          enable_captcha: { type: 'boolean', label: 'Enable CAPTCHA on Forms' }
        }
      },
      notifications: {
        title: 'Notifications',
        description: 'Email and webhook notification settings',
        fields: {
          enable_order_notifications: { type: 'boolean', label: 'Send Order Confirmation Emails' },
          enable_shipping_notifications: { type: 'boolean', label: 'Send Shipping Notification Emails' },
          enable_low_stock_notifications: { type: 'boolean', label: 'Send Low Stock Alerts' },
          notification_email: { type: 'email', label: 'Notification Email Address' },
          enable_customer_notifications: { type: 'boolean', label: 'Allow Customer Marketing Emails' }
        }
      },
      seo: {
        title: 'SEO Settings',
        description: 'Search engine optimization settings',
        fields: {
          meta_title: { type: 'text', label: 'Default Meta Title' },
          meta_description: { type: 'textarea', label: 'Default Meta Description' },
          enable_sitemap: { type: 'boolean', label: 'Generate XML Sitemap' },
          enable_robots_txt: { type: 'boolean', label: 'Generate robots.txt' },
          google_analytics_id: { type: 'text', label: 'Google Analytics ID' },
          facebook_pixel_id: { type: 'text', label: 'Facebook Pixel ID' }
        }
      },
      advanced: {
        title: 'Advanced Settings',
        description: 'Advanced configuration options',
        fields: {
          enable_api: { type: 'boolean', label: 'Enable REST API' },
          enable_webhooks: { type: 'boolean', label: 'Enable Webhooks' },
          custom_css: { type: 'textarea', label: 'Custom CSS', rows: 10 },
          custom_javascript: { type: 'textarea', label: 'Custom JavaScript', rows: 10 },
          maintenance_mode: { type: 'boolean', label: 'Enable Maintenance Mode' },
          maintenance_message: { type: 'textarea', label: 'Maintenance Message' }
        }
      }
    };
    
    return c.json({ schema });
    
  } catch (error) {
    return handleError(c, error, 'Failed to fetch settings schema');
  }
});

// ===========================================
// PRODUCT TYPES ROUTES
// ===========================================

// Get all product types
app.get('/api/product-types', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const productTypes = await dbSession.query(
      (session) => session.prepare(`
      SELECT id, name, metadata, created_at
      FROM product_types
      ORDER BY name ASC
    `).all(),
      { queryName: 'db-query' }
    );

    return c.json({
      success: true,
      product_types: productTypes.results || [] 
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch product types');
  }
});

// Create product type
app.post('/api/product-types', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    
    const { name, metadata } = await c.req.json();

    if (!name?.trim()) {
      return c.json({ success: false, error: 'Product type name is required' }, 400);
    }

    const id = generateId('ptyp');

    await dbSession.query(
      (session) => session.prepare(`
        INSERT INTO product_types (id, name, metadata, created_at)
        VALUES (?, ?, ?, CURRENT_TIMESTAMP)
      `).bind(
        id,
        name.trim(),
        metadata ? JSON.stringify(metadata) : null
      ).run(),
      { usePrimary: true, queryName: 'create-product-type' }
    );

    return c.json({
      success: true,
      data: { id },
      message: 'Product type created successfully'
    });

  } catch (error) {
    return handleError(c, error, 'Failed to create product type');
  }
});

// Get single product type
app.get('/api/product-types/:id', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const id = c.req.param('id');

    const productType = await dbSession.query(
      (session) => session.prepare(`
      SELECT id, name, metadata, created_at
      FROM product_types
      WHERE id = ?
    `).bind(id).first(),
      { queryName: 'db-query' }
    );

    if (!productType) {
      return c.json({ success: false, error: 'Product type not found' }, 404);
    }

    return c.json({
      success: true,
      data: {
        ...productType,
        metadata: (productType.metadata && typeof productType.metadata === 'string') ? JSON.parse(productType.metadata) : null
      }
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch product type');
  }
});

// Update product type
app.put('/api/product-types/:id', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const id = c.req.param('id');
    const { name, metadata } = await c.req.json();

    if (!name?.trim()) {
      return c.json({ success: false, error: 'Product type name is required' }, 400);
    }

    // Check if product type exists
    const existing = await dbSession.query(
      (session) => session.prepare(`
      SELECT id FROM product_types WHERE id = ?
    `).bind(id).first(),
      { queryName: 'db-query' }
    );

    if (!existing) {
      return c.json({ success: false, error: 'Product type not found' }, 404);
    }

    await dbSession.query(
      (session) => session.prepare(`
      UPDATE product_types 
      SET name = ?, metadata = ?
      WHERE id = ?
    `).bind(
      name.trim(),
      metadata ? JSON.stringify(metadata) : null,
      id
    ).run(),
      { usePrimary: true, queryName: 'db-write' }
    );

    return c.json({
      success: true,
      message: 'Product type updated successfully'
    });

  } catch (error) {
    return handleError(c, error, 'Failed to update product type');
  }
});

// Delete product type
app.delete('/api/product-types/:id', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const id = c.req.param('id');

    // Check if any products use this type
    const productsUsingType = await dbSession.query(
      (session) => session.prepare(`
      SELECT COUNT(*) as count FROM products WHERE type_id = ? AND deleted_at IS NULL
    `).bind(id).first(),
      { queryName: 'db-query' }
    );

    if (productsUsingType && (productsUsingType.count as number) > 0) {
      return c.json({
        success: false,
        error: 'Cannot delete product type that is being used by products'
      }, 400);
    }

    await dbSession.query(
      (session) => session.prepare(`
      DELETE FROM product_types WHERE id = ?
    `).bind(id).run(),
      { usePrimary: true, queryName: 'db-write' }
    );

    return c.json({
      success: true,
      message: 'Product type deleted successfully'
    });

  } catch (error) {
    return handleError(c, error, 'Failed to delete product type');
  }
});

// ===========================================
// CATEGORIES ROUTES
// ===========================================

// Get all categories
app.get('/api/categories', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const categories = await dbSession.query(
      (session) => session.prepare(`
      SELECT 
        c.id, c.parent_id, c.handle, c.sort_order, c.is_active, c.metadata, c.created_at,
        ct.name, ct.description, ct.seo_title, ct.seo_description
      FROM categories c
      LEFT JOIN category_translations ct ON c.id = ct.category_id AND ct.language_code ='ro'
      WHERE c.deleted_at IS NULL
      ORDER BY c.sort_order, c.created_at DESC
    `).all(),
      { queryName: 'db-query' }
    );

    return c.json({ 
      success: true,
      categories: categories.results || [] 
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch categories');
  }
});

// ===========================================
// CUSTOMER GROUPS ROUTES
// ===========================================

// Get all customer groups
app.get('/api/customer-groups', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const search = c.req.query('search');
    
    let whereClause = 'deleted_at IS NULL';
    let params = [];

    if (search) {
      whereClause += ' AND (name LIKE ? OR description LIKE ?)';
      const searchParam = `%${search}%`;
      params.push(searchParam, searchParam);
    }

    const groups = await dbSession.query(
      (session) => session.prepare(`
      SELECT 
        cg.*,
        COUNT(c.id) as customer_count
      FROM customer_groups cg
      LEFT JOIN customers c ON cg.id = c.group_id AND c.deleted_at IS NULL
      WHERE ${whereClause}
      GROUP BY cg.id
      ORDER BY cg.created_at DESC
    `).bind(...params).all(),
      { queryName: 'db-query' }
    );

    // Add additional stats for each group
    const groupsWithStats = await Promise.all(
      groups.results.map(async (group) => {
        const stats = await dbSession.query(
      (session) => session.prepare(`
          SELECT 
            COUNT(DISTINCT o.id) as total_orders,
            COALESCE(SUM(o.total_amount), 0) as total_spent
          FROM orders o
          JOIN customers c ON o.customer_id = c.id
          WHERE c.group_id = ? AND c.deleted_at IS NULL AND o.deleted_at IS NULL
        `).bind(group.id).first(),
      { queryName: 'db-query' }
    );

        return {
          ...group,
          total_orders: stats?.total_orders || 0,
          total_spent: stats?.total_spent || 0,
        };
      })
    );

    return c.json({ groups: groupsWithStats });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch customer groups');
  }
});

// Create customer group
app.post('/api/customer-groups', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const { name, description, discount_percentage, metadata } = await c.req.json();

    if (!name?.trim()) {
      return c.json({ error: 'Group name is required' }, 400);
    }

    const id = crypto.randomUUID();

    await dbSession.query(
      (session) => session.prepare(`
      INSERT INTO customer_groups (
        id, name, description, discount_percentage, metadata, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `).bind(
      id,
      name.trim(),
      description?.trim() || null,
      discount_percentage || 0,
      metadata ? JSON.stringify(metadata) : null
    ).run(),
      { usePrimary: true, queryName: 'db-write' }
    );

    return c.json({ 
      message: 'Customer group created successfully',
      group_id: id 
    });

  } catch (error) {
    return handleError(c, error, 'Failed to create customer group');
  }
});

// Update customer group
app.put('/api/customer-groups/:id', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const id = c.req.param('id');
    const { name, description, discount_percentage, metadata } = await c.req.json();

    if (!name?.trim()) {
      return c.json({ error: 'Group name is required' }, 400);
    }

    await dbSession.query(
      (session) => session.prepare(`
      UPDATE customer_groups 
      SET 
        name = ?, 
        description = ?, 
        discount_percentage = ?, 
        metadata = ?, 
        updated_at = CURRENT_TIMESTAMP 
      WHERE id = ? AND deleted_at IS NULL
    `).bind(
      name.trim(),
      description?.trim() || null,
      discount_percentage || 0,
      metadata ? JSON.stringify(metadata) : null,
      id
    ).run(),
      { usePrimary: true, queryName: 'db-write' }
    );

    return c.json({ message: 'Customer group updated successfully' });

  } catch (error) {
    return handleError(c, error, 'Failed to update customer group');
  }
});

// Delete customer group
app.delete('/api/customer-groups/:id', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const id = c.req.param('id');

    // Soft delete the group
    await dbSession.query(
      (session) => session.prepare(`
      UPDATE customer_groups 
      SET deleted_at = CURRENT_TIMESTAMP 
      WHERE id = ?
    `).bind(id).run(),
      { usePrimary: true, queryName: 'db-write' }
    );

    // Remove group assignment from customers
    await dbSession.query(
      (session) => session.prepare(`
      UPDATE customers 
      SET group_id = NULL, updated_at = CURRENT_TIMESTAMP
      WHERE group_id = ?
    `).bind(id).run(),
      { usePrimary: true, queryName: 'db-write' }
    );

    return c.json({ message: 'Customer group deleted successfully' });

  } catch (error) {
    return handleError(c, error, 'Failed to delete customer group');
  }
});

// Bulk delete customer groups
app.post('/api/customer-groups/bulk-delete', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const { groupIds } = await c.req.json();

    if (!groupIds || !Array.isArray(groupIds) || groupIds.length === 0) {
      return c.json({ error: 'Group IDs are required' }, 400);
    }

    const placeholders = groupIds.map(() => '?').join(',');
    
    // Soft delete the groups
    await dbSession.query(
      (session) => session.prepare(`
      UPDATE customer_groups 
      SET deleted_at = CURRENT_TIMESTAMP 
      WHERE id IN (${placeholders})
    `).bind(...groupIds).run(),
      { usePrimary: true, queryName: 'db-write' }
    );

    // Remove group assignments from customers
    await dbSession.query(
      (session) => session.prepare(`
      UPDATE customers 
      SET group_id = NULL, updated_at = CURRENT_TIMESTAMP
      WHERE group_id IN (${placeholders})
    `).bind(...groupIds).run(),
      { usePrimary: true, queryName: 'db-write' }
    );

    return c.json({ 
      message: `${groupIds.length} customer groups deleted successfully` 
    });

  } catch (error) {
    return handleError(c, error, 'Failed to delete customer groups');
  }
});

// ===========================================
// TAGS ROUTES
// ===========================================

// Get all tags from products (extract from products.tags JSON)
app.get('/api/tags', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const products = await dbSession.query(
      (session) => session.prepare(`
      SELECT tags FROM products 
      WHERE deleted_at IS NULL AND tags IS NOT NULL AND tags != '[]'
    `).all(),
      { queryName: 'db-query' }
    );

    const tagsMap = new Map();
    
    for (const product of products.results) {
      if (product.tags) {
        try {
          const productTags = JSON.parse(String(product.tags));
          if (Array.isArray(productTags)) {
            productTags.forEach(tag => {
              if (tag && typeof tag === 'string') {
                const count = tagsMap.get(tag) || 0;
                tagsMap.set(tag, count + 1);
              }
            });
          }
        } catch (e) {
          // Skip invalid JSON
        }
      }
    }

    const tags = Array.from(tagsMap.entries()).map(([name, usage_count]) => ({
      id: name.toLowerCase().replace(/\s+/g, '-'),
      name,
      usage_count,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    })).sort((a, b) => b.usage_count - a.usage_count);

    return c.json({ tags });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch tags');
  }
});

// Delete a tag (remove from all products)
app.delete('/api/tags/:tagName', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const tagName = decodeURIComponent(c.req.param('tagName'));

    // Get all products that have this tag
    const products = await dbSession.query(
      (session) => session.prepare(`
      SELECT id, tags FROM products 
      WHERE deleted_at IS NULL AND tags IS NOT NULL AND tags != '[]'
    `).all(),
      { queryName: 'db-query' }
    );

    const updates = [];
    
    for (const product of products.results) {
      if (product.tags) {
        try {
          const productTags = JSON.parse(String(product.tags));
          if (Array.isArray(productTags) && productTags.includes(tagName)) {
            const newTags = productTags.filter(tag => tag !== tagName);
            updates.push({
              id: product.id,
              tags: JSON.stringify(newTags)
            });
          }
        } catch (e) {
          // Skip invalid JSON
        }
      }
    }

    // Update all products
    for (const update of updates) {
      await dbSession.query(
      (session) => session.prepare(`
        UPDATE products 
        SET tags = ?, updated_at = CURRENT_TIMESTAMP 
        WHERE id = ?
      `).bind(update.tags, update.id).run(),
      { usePrimary: true, queryName: 'db-write' }
    );
    }

    return c.json({ 
      message: 'Tag deleted successfully',
      updated_products: updates.length 
    });

  } catch (error) {
    return handleError(c, error, 'Failed to delete tag');
  }
});

// ===========================================
// INVENTORY MANAGEMENT ROUTES
// ===========================================

// INVENTORY ITEMS ROUTES

// Get all inventory items
app.get('/api/inventory/items', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '20');
    const search = c.req.query('search') || '';
    const lowStock = c.req.query('low_stock') === 'true';
    const requiresShipping = c.req.query('requires_shipping');
    const offset = (page - 1) * limit;

    let query = `
      SELECT 
        ii.id, ii.sku, ii.title, ii.description, ii.thumbnail, ii.origin_country,
        ii.hs_code, ii.mid_code, ii.material, ii.weight, ii.length, ii.height, ii.width,
        ii.requires_shipping, ii.metadata, ii.created_at, ii.updated_at,
        COALESCE(SUM(il.stocked_quantity), 0) as total_quantity,
        COALESCE(SUM(il.stocked_quantity - il.reserved_quantity), 0) as available_quantity,
        COALESCE(SUM(il.reserved_quantity), 0) as reserved_quantity,
        COUNT(DISTINCT il.location_id) as location_count
      FROM inventory_item ii
      LEFT JOIN inventory_level il ON ii.id = il.inventory_item_id
      WHERE ii.deleted_at IS NULL
    `;

    const params = [];
    
    if (search) {
      query += ` AND (ii.sku LIKE ? OR ii.title LIKE ? OR ii.description LIKE ?)`;
      params.push(`%${search}%`, `%${search}%`, `%${search}%`);
    }
    
    if (requiresShipping && requiresShipping !== 'all') {
      query += ` AND ii.requires_shipping = ?`;
      params.push(requiresShipping === 'true' ? 1 : 0);
    }

    query += ` GROUP BY ii.id`;
    
    if (lowStock) {
      query += ` HAVING available_quantity < 10`;
    }

    query += ` ORDER BY ii.created_at DESC LIMIT ? OFFSET ?`;
    params.push(limit, offset);

    const items = await dbSession.query(
      (session) => session.prepare(query).bind(...params).all(),
      { queryName: 'db-query' }
    );

    // Get total count
    let countQuery = `SELECT COUNT(DISTINCT ii.id) as total FROM inventory_item ii`;
    let countParams: any[] = [];
    
    if (search || requiresShipping) {
      countQuery += ` WHERE ii.deleted_at IS NULL`;
      
      if (search) {
        countQuery += ` AND (ii.sku LIKE ? OR ii.title LIKE ? OR ii.description LIKE ?)`;
        countParams.push(`%${search}%`, `%${search}%`, `%${search}%`);
      }
      
      if (requiresShipping && requiresShipping !== 'all') {
        countQuery += ` AND ii.requires_shipping = ?`;
        countParams.push(requiresShipping === 'true' ? 1 : 0);
      }
    } else {
      countQuery += ` WHERE ii.deleted_at IS NULL`;
    }

    const totalResult = await dbSession.query(
      (session) => session.prepare(countQuery).bind(...countParams).first(),
      { queryName: 'db-query' }
    );

    return c.json({
      items: items.results,
      total: totalResult?.total || 0,
      page,
      limit,
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch inventory items');
  }
});

// Create inventory item
app.post('/api/inventory/items', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const data = await c.req.json();
    const id = generateId('iitem');

    await c.env.DB.prepare(`
      INSERT INTO inventory_item (
        id, sku, title, description, thumbnail, origin_country, hs_code, mid_code,
        material, weight, length, height, width, requires_shipping, metadata,
        created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `).bind(
      id, data.sku, data.title, data.description || null, data.thumbnail || null,
      data.origin_country || null, data.hs_code || null, data.mid_code || null,
      data.material || null, data.weight || null, data.length || null,
      data.height || null, data.width || null, data.requires_shipping ? 1 : 0,
      JSON.stringify(data.metadata || {})
    ).run();

    return c.json({ message: 'Inventory item created successfully', id });

  } catch (error) {
    return handleError(c, error, 'Failed to create inventory item');
  }
});

// Update inventory item
app.put('/api/inventory/items/:id', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const id = c.req.param('id');
    const data = await c.req.json();
    
    await c.env.DB.prepare(`
      UPDATE inventory_item SET 
        sku = ?, title = ?, description = ?, thumbnail = ?, origin_country = ?,
        hs_code = ?, mid_code = ?, material = ?, weight = ?, length = ?, height = ?,
        width = ?, requires_shipping = ?, metadata = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ? AND deleted_at IS NULL
    `).bind(
      data.sku, data.title, data.description || null, data.thumbnail || null,
      data.origin_country || null, data.hs_code || null, data.mid_code || null,
      data.material || null, data.weight || null, data.length || null,
      data.height || null, data.width || null, data.requires_shipping ? 1 : 0,
      JSON.stringify(data.metadata || {}), id
    ).run();

    return c.json({ message: 'Inventory item updated successfully' });

  } catch (error) {
    return handleError(c, error, 'Failed to update inventory item');
  }
});

// Delete inventory item
app.delete('/api/inventory/items/:id', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const id = c.req.param('id');
    
    await dbSession.query(
      (session) => session.prepare(`
      UPDATE inventory_item 
      SET deleted_at = CURRENT_TIMESTAMP 
      WHERE id = ?
    `).bind(id).run(),
      { usePrimary: true, queryName: 'db-write' }
    );

    return c.json({ message: 'Inventory item deleted successfully' });

  } catch (error) {
    return handleError(c, error, 'Failed to delete inventory item');
  }
});

// STOCK LOCATIONS ROUTES

// Get all stock locations
app.get('/api/inventory/locations', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '20');
    const search = c.req.query('search') || '';
    const offset = (page - 1) * limit;

    let query = `
      SELECT 
        sl.id, sl.name, sl.metadata, sl.created_at, sl.updated_at,
        sla.id as address_id, sla.address_1, sla.address_2, sla.company, sla.city,
        sla.country_code, sla.phone, sla.province, sla.postal_code,
        COUNT(DISTINCT il.inventory_item_id) as item_count,
        COALESCE(SUM(il.stocked_quantity), 0) as total_inventory,
        COUNT(CASE WHEN il.stocked_quantity - il.reserved_quantity < 10 THEN 1 END) as low_stock_items
      FROM stock_location sl
      LEFT JOIN stock_location_address sla ON sl.address_id = sla.id
      LEFT JOIN inventory_level il ON sl.id = il.location_id
      WHERE sl.deleted_at IS NULL
    `;

    const params = [];
    
    if (search) {
      query += ` AND (sl.name LIKE ? OR sla.city LIKE ? OR sla.country_code LIKE ?)`;
      params.push(`%${search}%`, `%${search}%`, `%${search}%`);
    }

    query += ` GROUP BY sl.id ORDER BY sl.created_at DESC LIMIT ? OFFSET ?`;
    params.push(limit, offset);

    const locations = await dbSession.query(
      (session) => session.prepare(query).bind(...params).all(),
      { queryName: 'db-query' }
    );

    // Format results with nested address
    const formattedLocations = locations.results.map(loc => ({
      id: loc.id,
      name: loc.name,
      metadata: loc.metadata ? JSON.parse(String(loc.metadata)) : null,
      created_at: loc.created_at,
      updated_at: loc.updated_at,
      item_count: loc.item_count,
      total_inventory: loc.total_inventory,
      low_stock_items: loc.low_stock_items,
      address: loc.address_id ? {
        id: loc.address_id,
        address_1: loc.address_1,
        address_2: loc.address_2,
        company: loc.company,
        city: loc.city,
        country_code: loc.country_code,
        phone: loc.phone,
        province: loc.province,
        postal_code: loc.postal_code,
      } : null,
    }));

    // Get total count
    let countQuery = `SELECT COUNT(*) as total FROM stock_location sl WHERE sl.deleted_at IS NULL`;
    let countParams = [];
    
    if (search) {
      countQuery += ` AND EXISTS (
        SELECT 1 FROM stock_location_address sla 
        WHERE sl.address_id = sla.id 
        AND (sl.name LIKE ? OR sla.city LIKE ? OR sla.country_code LIKE ?)
      )`;
      countParams.push(`%${search}%`, `%${search}%`, `%${search}%`);
    }

    const totalResult = await dbSession.query(
      (session) => session.prepare(countQuery).bind(...countParams).first(),
      { queryName: 'db-query' }
    );

    return c.json({
      locations: formattedLocations,
      total: totalResult?.total || 0,
      page,
      limit,
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch stock locations');
  }
});

// Create stock location
app.post('/api/inventory/locations', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const data = await c.req.json();
    const locationId = generateId('sloc');
    let addressId = null;
    
    // Create address first if provided
    if (data.address && data.address.address_1) {
      addressId = generateId('sladdr');
      await c.env.DB.prepare(`
        INSERT INTO stock_location_address (
          id, address_1, address_2, company, city,
          country_code, phone, province, postal_code, metadata,
          created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      `).bind(
        addressId, data.address.address_1, data.address.address_2 || null,
        data.address.company || null, data.address.city, data.address.country_code,
        data.address.phone || null, data.address.province || null,
        data.address.postal_code, JSON.stringify({})
      ).run();
    }
    
    // Create location with address_id reference
    await c.env.DB.prepare(`
      INSERT INTO stock_location (id, name, address_id, metadata, created_at, updated_at)
      VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `).bind(locationId, data.name, addressId, JSON.stringify(data.metadata || {})).run();

    return c.json({ message: 'Stock location created successfully', id: locationId });

  } catch (error) {
    return handleError(c, error, 'Failed to create stock location');
  }
});

// Update stock location
app.put('/api/inventory/locations/:id', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const id = c.req.param('id');
    const data = await c.req.json();
    
    // Get current location to check existing address_id
    const currentLocation = await dbSession.query(
      (session) => session.prepare(`
      SELECT address_id FROM stock_location WHERE id = ? AND deleted_at IS NULL
    `).bind(id).first(),
      { queryName: 'db-query' }
    );
    
    let addressId = currentLocation?.address_id || null;
    
    // Handle address update/creation
    if (data.address && data.address.address_1) {
      if (addressId) {
        // Update existing address
        await dbSession.query(
      (session) => session.prepare(`
          UPDATE stock_location_address SET 
            address_1 = ?, address_2 = ?, company = ?, city = ?, country_code = ?,
            phone = ?, province = ?, postal_code = ?, updated_at = CURRENT_TIMESTAMP
          WHERE id = ?
        `).bind(
          data.address.address_1, data.address.address_2 || null,
          data.address.company || null, data.address.city, data.address.country_code,
          data.address.phone || null, data.address.province || null,
          data.address.postal_code, addressId
        ).run(),
      { usePrimary: true, queryName: 'db-write' }
    );
      } else {
        // Create new address
        addressId = generateId('sladdr');
        await c.env.DB.prepare(`
          INSERT INTO stock_location_address (
            id, address_1, address_2, company, city,
            country_code, phone, province, postal_code, metadata,
            created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        `).bind(
          addressId, data.address.address_1, data.address.address_2 || null,
          data.address.company || null, data.address.city, data.address.country_code,
          data.address.phone || null, data.address.province || null,
          data.address.postal_code, JSON.stringify({})
        ).run();
      }
    }
    
    // Update location
    await c.env.DB.prepare(`
      UPDATE stock_location 
      SET name = ?, address_id = ?, metadata = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ? AND deleted_at IS NULL
    `).bind(data.name, addressId, JSON.stringify(data.metadata || {}), id).run();

    return c.json({ message: 'Stock location updated successfully' });

  } catch (error) {
    return handleError(c, error, 'Failed to update stock location');
  }
});

// Delete stock location
app.delete('/api/inventory/locations/:id', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const id = c.req.param('id');
    
    await dbSession.query(
      (session) => session.prepare(`
      UPDATE stock_location 
      SET deleted_at = CURRENT_TIMESTAMP 
      WHERE id = ?
    `).bind(id).run(),
      { usePrimary: true, queryName: 'db-write' }
    );

    return c.json({ message: 'Stock location deleted successfully' });

  } catch (error) {
    return handleError(c, error, 'Failed to delete stock location');
  }
});

// INVENTORY LEVELS ROUTES

// Get all inventory levels
app.get('/api/inventory/levels', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '20');
    const search = c.req.query('search') || '';
    const locationId = c.req.query('location_id');
    const lowStock = c.req.query('low_stock') === 'true';
    const offset = (page - 1) * limit;

    let query = `
      SELECT 
        il.id, il.inventory_item_id, il.location_id, il.stocked_quantity,
        il.reserved_quantity, il.incoming_quantity, 
        (il.stocked_quantity - il.reserved_quantity) as available_quantity,
        il.metadata, il.created_at, il.updated_at,
        ii.sku, ii.title, ii.thumbnail,
        sl.name as location_name
      FROM inventory_level il
      JOIN inventory_item ii ON il.inventory_item_id = ii.id
      JOIN stock_location sl ON il.location_id = sl.id
      WHERE ii.deleted_at IS NULL AND sl.deleted_at IS NULL
    `;

    const params = [];
    
    if (search) {
      query += ` AND (ii.sku LIKE ? OR ii.title LIKE ?)`;
      params.push(`%${search}%`, `%${search}%`);
    }
    
    if (locationId) {
      query += ` AND il.location_id = ?`;
      params.push(locationId);
    }
    
    if (lowStock) {
      query += ` AND (il.stocked_quantity - il.reserved_quantity) < 10`;
    }

    query += ` ORDER BY il.updated_at DESC LIMIT ? OFFSET ?`;
    params.push(limit, offset);

    const levels = await dbSession.query(
      (session) => session.prepare(query).bind(...params).all(),
      { queryName: 'db-query' }
    );

    // Format results
    const formattedLevels = levels.results.map(level => ({
      id: level.id,
      inventory_item_id: level.inventory_item_id,
      location_id: level.location_id,
      stocked_quantity: level.stocked_quantity,
      reserved_quantity: level.reserved_quantity,
      incoming_quantity: level.incoming_quantity,
      available_quantity: level.available_quantity,
      metadata: level.metadata ? JSON.parse(String(level.metadata)) : null,
      created_at: level.created_at,
      updated_at: level.updated_at,
      item: {
        id: level.inventory_item_id,
        sku: level.sku,
        title: level.title,
        thumbnail: level.thumbnail,
      },
      location: {
        id: level.location_id,
        name: level.location_name,
      },
    }));

    // Get total count
    let countQuery = `
      SELECT COUNT(*) as total 
      FROM inventory_level il
      JOIN inventory_item ii ON il.inventory_item_id = ii.id
      JOIN stock_location sl ON il.location_id = sl.id
      WHERE ii.deleted_at IS NULL AND sl.deleted_at IS NULL
    `;
    let countParams = [];
    
    if (search) {
      countQuery += ` AND (ii.sku LIKE ? OR ii.title LIKE ?)`;
      countParams.push(`%${search}%`, `%${search}%`);
    }
    
    if (locationId) {
      countQuery += ` AND il.location_id = ?`;
      countParams.push(locationId);
    }

    const totalResult = await dbSession.query(
      (session) => session.prepare(countQuery).bind(...countParams).first(),
      { queryName: 'db-query' }
    );

    return c.json({
      levels: formattedLevels,
      total: totalResult?.total || 0,
      page,
      limit,
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch inventory levels');
  }
});

// INVENTORY ADJUSTMENTS ROUTES

// Create inventory adjustment
app.post('/api/inventory/adjustments', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const data = await c.req.json();
    const { inventory_level_id, adjustment_type, quantity, reason, reference } = data;
    
    // Get current level
    const level = await dbSession.query(
      (session) => session.prepare(`
      SELECT stocked_quantity FROM inventory_level WHERE id = ?
    `).bind(inventory_level_id).first(),
      { queryName: 'db-query' }
    );

    if (!level) {
      return c.json({ error: 'Inventory level not found' }, 404);
    }

    let newQuantity;
    switch (adjustment_type) {
      case 'increase':
        newQuantity = Number(level.stocked_quantity) + quantity;
        break;
      case 'decrease':
        newQuantity = Math.max(0, Number(level.stocked_quantity) - quantity);
        break;
      case 'set':
        newQuantity = quantity;
        break;
      default:
        return c.json({ error: 'Invalid adjustment type' }, 400);
    }

    // Update inventory level
    await dbSession.query(
      (session) => session.prepare(`
      UPDATE inventory_level 
      SET stocked_quantity = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `).bind(newQuantity, inventory_level_id).run(),
      { usePrimary: true, queryName: 'db-write' }
    );

    // Log the adjustment (you could create an adjustments table for this)
    
    return c.json({ 
      message: 'Stock adjustment applied successfully',
      old_quantity: level.stocked_quantity,
      new_quantity: newQuantity
    });

  } catch (error) {
    return handleError(c, error, 'Failed to create stock adjustment');
  }
});

// INVENTORY RESERVATIONS ROUTES

// Get all inventory reservations
app.get('/api/inventory/reservations', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '20');
    const search = c.req.query('search') || '';
    const locationId = c.req.query('location_id');
    const status = c.req.query('status');
    const offset = (page - 1) * limit;

    let query = `
      SELECT 
        ri.id, ri.line_item_id, ri.inventory_item_id, ri.location_id,
        ri.quantity, ri.description, ri.created_by, ri.metadata,
        ri.created_at, ri.updated_at,
        ii.sku, ii.title, ii.thumbnail,
        sl.name as location_name,
        oi.quantity as line_item_quantity,
        o.id as order_id, o.display_id as order_display_id
      FROM reservation_item ri
      JOIN inventory_item ii ON ri.inventory_item_id = ii.id
      JOIN stock_location sl ON ri.location_id = sl.id
      LEFT JOIN order_item oi ON ri.line_item_id = oi.item_id
      LEFT JOIN "order" o ON oi.order_id = o.id
      WHERE ii.deleted_at IS NULL AND sl.deleted_at IS NULL
    `;

    const params = [];
    
    if (search) {
      query += ` AND (ii.sku LIKE ? OR ii.title LIKE ? OR o.display_id LIKE ?)`;
      params.push(`%${search}%`, `%${search}%`, `%${search}%`);
    }
    
    if (locationId) {
      query += ` AND ri.location_id = ?`;
      params.push(locationId);
    }

    query += ` ORDER BY ri.created_at DESC LIMIT ? OFFSET ?`;
    params.push(limit, offset);

    const reservations = await dbSession.query(
      (session) => session.prepare(query).bind(...params).all(),
      { queryName: 'db-query' }
    );

    // Format results
    const formattedReservations = reservations.results.map(res => ({
      id: res.id,
      line_item_id: res.line_item_id,
      inventory_item_id: res.inventory_item_id,
      location_id: res.location_id,
      quantity: res.quantity,
      description: res.description,
      created_by: res.created_by,
      metadata: res.metadata ? JSON.parse(String(res.metadata)) : null,
      created_at: res.created_at,
      updated_at: res.updated_at,
      item: {
        id: res.inventory_item_id,
        sku: res.sku,
        title: res.title,
        thumbnail: res.thumbnail,
      },
      location: {
        id: res.location_id,
        name: res.location_name,
      },
      line_item: res.line_item_id ? {
        id: res.line_item_id,
        order_id: res.order_id,
        order_display_id: res.order_display_id,
        quantity: res.line_item_quantity,
      } : null,
    }));

    // Get total count
    let countQuery = `
      SELECT COUNT(*) as total 
      FROM reservation_item ri
      JOIN inventory_item ii ON ri.inventory_item_id = ii.id
      JOIN stock_location sl ON ri.location_id = sl.id
      LEFT JOIN order_item oi ON ri.line_item_id = oi.item_id
      LEFT JOIN "order" o ON oi.order_id = o.id
      WHERE ii.deleted_at IS NULL AND sl.deleted_at IS NULL
    `;
    let countParams = [];
    
    if (search) {
      countQuery += ` AND (ii.sku LIKE ? OR ii.title LIKE ? OR o.display_id LIKE ?)`;
      countParams.push(`%${search}%`, `%${search}%`, `%${search}%`);
    }
    
    if (locationId) {
      countQuery += ` AND ri.location_id = ?`;
      countParams.push(locationId);
    }

    const totalResult = await dbSession.query(
      (session) => session.prepare(countQuery).bind(...countParams).first(),
      { queryName: 'db-query' }
    );

    return c.json({
      reservations: formattedReservations,
      total: totalResult?.total || 0,
      page,
      limit,
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch inventory reservations');
  }
});

// Cancel inventory reservation
app.delete('/api/inventory/reservations/:id', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const id = c.req.param('id');
    
    // Get reservation details for updating inventory level
    const reservation = await dbSession.query(
      (session) => session.prepare(`
      SELECT inventory_item_id, location_id, quantity 
      FROM reservation_item 
      WHERE id = ?
    `).bind(id).first(),
      { queryName: 'db-query' }
    );

    if (!reservation) {
      return c.json({ error: 'Reservation not found' }, 404);
    }

    // Delete reservation
    await dbSession.query(
      (session) => session.prepare(`
      DELETE FROM reservation_item WHERE id = ?
    `).bind(id).run(),
      { usePrimary: true, queryName: 'db-write' }
    );

    // Update inventory level (reduce reserved quantity)
    await dbSession.query(
      (session) => session.prepare(`
      UPDATE inventory_level 
      SET reserved_quantity = reserved_quantity - ?, updated_at = CURRENT_TIMESTAMP
      WHERE inventory_item_id = ? AND location_id = ?
    `).bind(reservation.quantity, reservation.inventory_item_id, reservation.location_id).run(),
      { usePrimary: true, queryName: 'db-write' }
    );

    return c.json({ message: 'Reservation cancelled successfully' });

  } catch (error) {
    return handleError(c, error, 'Failed to cancel reservation');
  }
});

// ===========================================
// ANALYTICS AND DASHBOARD ROUTES
// ===========================================

// Get dashboard analytics
app.get('/api/analytics/dashboard', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    // Get basic counts and totals
    const [
      orderStats,
      customerStats,
      productStats,
      revenueStats
    ] = await Promise.all([
      // Order statistics - orders table doesn't have deleted_at, only cancelled_at
      c.env.DB.prepare(`
        SELECT 
          COUNT(*) as total_orders,
          COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_orders,
          COUNT(CASE WHEN created_at >= date('now', '-30 days') THEN 1 END) as orders_last_30_days
        FROM orders 
        WHERE status != 'cancelled'
      `).first(),
      
      // Customer statistics - customers table has deleted_at
      c.env.DB.prepare(`
        SELECT 
          COUNT(*) as total_customers,
          COUNT(CASE WHEN created_at >= date('now', '-30 days') THEN 1 END) as new_customers_last_30_days
        FROM customers 
        WHERE deleted_at IS NULL
      `).first(),
      
      // Product statistics
      c.env.DB.prepare(`
        SELECT 
          COUNT(*) as total_products,
          COUNT(CASE WHEN status = 'active' THEN 1 END) as active_products,
          COUNT(CASE WHEN inventory_quantity <= 10 THEN 1 END) as low_stock_products
        FROM products p
        LEFT JOIN product_variants pv ON p.id = pv.product_id
        WHERE p.deleted_at IS NULL
      `).first(),
      
      // Revenue statistics - orders table doesn't have deleted_at
      c.env.DB.prepare(`
        SELECT 
          COALESCE(SUM(total_amount), 0) as total_revenue,
          COALESCE(SUM(CASE WHEN created_at >= date('now', '-30 days') THEN total_amount ELSE 0 END), 0) as revenue_last_30_days,
          COALESCE(SUM(CASE WHEN created_at >= date('now', '-7 days') THEN total_amount ELSE 0 END), 0) as revenue_last_7_days
        FROM orders 
        WHERE status NOT IN ('cancelled', 'refunded')
      `).first()
    ]);

    // Get recent orders - orders table doesn't have deleted_at
    const recentOrders = await dbSession.query(
      (session) => session.prepare(`
      SELECT 
        o.id, o.number, o.total_amount, o.currency_code, o.status, o.created_at,
        c.first_name, c.last_name, c.email
      FROM orders o
      LEFT JOIN customers c ON o.customer_id = c.id
      WHERE o.status != 'cancelled'
      ORDER BY o.created_at DESC
      LIMIT 10
    `).all(),
      { queryName: 'db-query' }
    );

    return c.json({
      success: true,
      data: {
        orders: {
          total: orderStats?.total_orders || 0,
          pending: orderStats?.pending_orders || 0,
          last_30_days: orderStats?.orders_last_30_days || 0,
        },
        customers: {
          total: customerStats?.total_customers || 0,
          new_last_30_days: customerStats?.new_customers_last_30_days || 0,
        },
        products: {
          total: productStats?.total_products || 0,
          active: productStats?.active_products || 0,
          low_stock: productStats?.low_stock_products || 0,
        },
        revenue: {
          total: revenueStats?.total_revenue || 0,
          last_30_days: revenueStats?.revenue_last_30_days || 0,
          last_7_days: revenueStats?.revenue_last_7_days || 0,
        },
        recent_orders: (recentOrders.results || []).map((order: any) => ({
          ...order,
          total_amount: parseFloat(order.total_amount) || 0
        })),
      },
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch dashboard data');
  }
});

// Populate analytics from existing order data
app.post('/api/analytics/populate', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    console.log('Starting analytics population...');
    
    // Get all order items with order data - Updated to use actual statuses in the database
    const orderItemsQuery = `
      SELECT 
        oi.variant_id,
        oi.quantity,
        oi.total_price,
        oi.created_at,
        o.customer_id,
        o.status,
        o.financial_status,
        pv.product_id
      FROM order_items oi
      JOIN orders o ON oi.order_id = o.id
      JOIN product_variants pv ON oi.variant_id = pv.id
      WHERE o.status IN ('completed', 'delivered', 'shipped', 'processing', 'confirmed')
        AND o.financial_status IN ('pending', 'paid', 'partially_paid')
      ORDER BY oi.created_at
    `;

    const orderItems = await dbSession.query(
      (session) => session.prepare(orderItemsQuery).all(),
      { queryName: 'db-query' }
    );
    console.log(`Found ${orderItems.results?.length || 0} order items to process`);

    if (!orderItems.results?.length) {
      return c.json({
        success: true,
        message: 'No order items found to process'
      });
    }

    let processedCount = 0;

    // Process each order item to update analytics
    for (const item of orderItems.results) {
      try {
        const productId = item.product_id as string;
        const variantId = item.variant_id as string;
        const quantity = Number(item.quantity);
        const revenue = parseFloat(String(item.total_price));
        const orderDate = new Date(String(item.created_at).replace(/"/g, ''));

        console.log(`Processing item: product=${productId}, variant=${variantId}, quantity=${quantity}, revenue=${revenue}, date=${orderDate.toISOString()}`);

        // Update order statistics
        const periodStart = new Date(orderDate.getFullYear(), orderDate.getMonth(), 1);
        const periodEnd = new Date(orderDate.getFullYear(), orderDate.getMonth() + 1, 0);
        const periodStartStr = periodStart.toISOString().split('T')[0];
        const periodEndStr = periodEnd.toISOString().split('T')[0];
        const orderDateStr = orderDate.toISOString();

        // Calculate popularity score
        const daysSinceOrder = Math.floor((Date.now() - orderDate.getTime()) / (1000 * 60 * 60 * 24));
        const recencyScore = Math.max(0, 100 - daysSinceOrder * 2);
        const basePopularityScore = quantity * 0.4 + (revenue / 100) * 0.3 + recencyScore * 0.3;

        const statisticsId = `${productId}-${variantId}-${periodStartStr}`;

        console.log(`Checking for existing order statistics: ${statisticsId}`);

        // Check if record exists
        const existingRecord = await dbSession.query(
      (session) => session.prepare(`
          SELECT id, total_orders, total_quantity, total_revenue, first_order_date, popularity_score
          FROM order_statistics
          WHERE product_id = ? AND variant_id = ? AND period_start = ?
        `).bind(productId, variantId, periodStartStr + ' 00:00:00').first(),
      { queryName: 'db-query' }
    );

        if (existingRecord) {
          console.log(`Updating existing order statistics record: ${existingRecord.id}`);
          // Update existing record
          const newTotalOrders = Number(existingRecord.total_orders) + 1;
          const newTotalQuantity = Number(existingRecord.total_quantity) + quantity;
          const newTotalRevenue = Number(existingRecord.total_revenue) + revenue;
          const newAvgOrderValue = newTotalRevenue / newTotalOrders;
          const newPopularityScore = Number(existingRecord.popularity_score) + basePopularityScore * 0.1;

          await dbSession.query(
      (session) => session.prepare(`
            UPDATE order_statistics
            SET 
              total_orders = ?,
              total_quantity = ?,
              total_revenue = ?,
              avg_order_value = ?,
              last_order_date = ?,
              popularity_score = ?,
              updated_at = ?
            WHERE id = ?
          `).bind(
            newTotalOrders,
            newTotalQuantity,
            newTotalRevenue,
            newAvgOrderValue,
            orderDateStr,
            newPopularityScore,
            new Date().toISOString(),
            existingRecord.id
          ).run(),
      { usePrimary: true, queryName: 'db-write' }
    );
        } else {
          console.log(`Creating new order statistics record: ${statisticsId}`);
          // Insert new record
          await dbSession.query(
      (session) => session.prepare(`
            INSERT INTO order_statistics (
              id, product_id, variant_id, total_orders, total_quantity, total_revenue,
              avg_order_value, first_order_date, last_order_date, popularity_score,
              period_start, period_end, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
          `).bind(
            statisticsId,
            productId,
            variantId,
            1,
            quantity,
            revenue,
            revenue,
            orderDateStr,
            orderDateStr,
            basePopularityScore,
            periodStartStr + ' 00:00:00',
            periodEndStr + ' 23:59:59',
            new Date().toISOString(),
            new Date().toISOString()
          ).run(),
      { usePrimary: true, queryName: 'db-write' }
    );
        }

        // Update product analytics
        const analyticsId = `${productId}-${variantId || 'main'}-${periodStartStr}-monthly`;

        console.log(`Checking for existing product analytics: ${analyticsId}`);

        const existingAnalytics = await dbSession.query(
      (session) => session.prepare(`
          SELECT id, purchases, revenue
          FROM product_analytics
          WHERE product_id = ? AND variant_id = ? AND period_start = ? AND period_type = 'monthly'
        `).bind(productId, variantId, periodStartStr).first(),
      { queryName: 'db-query' }
    );

        if (existingAnalytics) {
          console.log(`Updating existing product analytics record: ${existingAnalytics.id}`);
          await dbSession.query(
      (session) => session.prepare(`
            UPDATE product_analytics
            SET 
              purchases = purchases + ?,
              revenue = revenue + ?,
              last_purchased_at = ?,
              updated_at = ?
            WHERE id = ?
          `).bind(
            quantity,
            revenue,
            orderDateStr,
            new Date().toISOString(),
            existingAnalytics.id
          ).run(),
      { usePrimary: true, queryName: 'db-write' }
    );
        } else {
          console.log(`Creating new product analytics record: ${analyticsId}`);
          await dbSession.query(
      (session) => session.prepare(`
            INSERT INTO product_analytics (
              id, product_id, variant_id, views, purchases, revenue, last_purchased_at,
              period_start, period_end, period_type, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
          `).bind(
            analyticsId,
            productId,
            variantId,
            0,
            quantity,
            revenue,
            orderDateStr,
            periodStartStr,
            periodEndStr,
            'monthly',
            new Date().toISOString(),
            new Date().toISOString()
          ).run(),
      { usePrimary: true, queryName: 'db-write' }
    );
        }

        processedCount++;
        console.log(`Successfully processed item ${processedCount}/${orderItems.results.length}`);

      } catch (itemError) {
        console.error(`Error processing order item ${item.variant_id}:`, itemError);
        throw itemError; // Re-throw to fail the entire operation
      }
    }

    // Clear cache to force fresh data
    if (c.env.CACHE) {
      await c.env.CACHE.put('popular_products_month', '', { expirationTtl: 60 });
      await c.env.CACHE.put('trending_products', '', { expirationTtl: 60 });
    }

    console.log(`Analytics population completed successfully. Processed ${processedCount} items.`);

    return c.json({
      success: true,
      message: `Analytics populated successfully from ${processedCount} order items`
    });
  } catch (error) {
    console.error('Failed to populate analytics:', error);
    return c.json({
      success: false,
      error: 'Failed to populate analytics',
      details: error instanceof Error ? error.message : String(error)
    }, 500);
  }
});

// Quick populate analytics endpoint (useful for manual testing)
app.post('/api/analytics/quick-populate', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    console.log('Starting quick analytics population...');
    
    // Only get the most recent 50 orders to populate quickly
    const orderItemsQuery = `
      SELECT 
        oi.variant_id,
        oi.quantity,
        oi.total_price,
        oi.created_at,
        o.customer_id,
        o.status,
        o.financial_status,
        pv.product_id
      FROM order_items oi
      JOIN orders o ON oi.order_id = o.id
      JOIN product_variants pv ON oi.variant_id = pv.id
      WHERE o.status IN ('completed', 'delivered', 'shipped', 'processing', 'confirmed', 'pending')
        AND o.financial_status IN ('pending', 'paid', 'partially_paid')
      ORDER BY oi.created_at DESC
      LIMIT 50
    `;

    const orderItems = await dbSession.query(
      (session) => session.prepare(orderItemsQuery).all(),
      { queryName: 'db-query' }
    );
    console.log(`Found ${orderItems.results?.length || 0} recent order items to process`);

    if (!orderItems.results?.length) {
      return c.json({
        success: true,
        message: 'No recent order items found to process'
      });
    }

    let processedCount = 0;

    // Process each order item to update analytics
    for (const item of orderItems.results) {
      try {
        const productId = item.product_id as string;
        const variantId = item.variant_id as string;
        const quantity = Number(item.quantity);
        const revenue = parseFloat(String(item.total_price));
        const orderDate = new Date(String(item.created_at).replace(/"/g, ''));

        // Update order statistics
        const periodStart = new Date(orderDate.getFullYear(), orderDate.getMonth(), 1);
        const periodStartStr = periodStart.toISOString().split('T')[0];
        const periodEndStr = new Date(orderDate.getFullYear(), orderDate.getMonth() + 1, 0).toISOString().split('T')[0];
        const orderDateStr = orderDate.toISOString();

        // Calculate popularity score based on recency and revenue
        const daysSinceOrder = Math.floor((Date.now() - orderDate.getTime()) / (1000 * 60 * 60 * 24));
        const recencyScore = Math.max(0, 100 - daysSinceOrder * 1);
        const basePopularityScore = quantity * 0.4 + (revenue / 100) * 0.3 + recencyScore * 0.3;

        const statisticsId = `${productId}-${variantId}-${periodStartStr}`;

        // Use UPSERT logic (INSERT OR REPLACE)
        await dbSession.query(
          (session) => session.prepare(`
            INSERT OR REPLACE INTO order_statistics (
              id, product_id, variant_id, total_orders, total_quantity, total_revenue,
              avg_order_value, first_order_date, last_order_date, popularity_score,
              period_start, period_end, created_at, updated_at
            ) VALUES (?, ?, ?, 
              COALESCE((SELECT total_orders FROM order_statistics WHERE id = ?), 0) + 1,
              COALESCE((SELECT total_quantity FROM order_statistics WHERE id = ?), 0) + ?,
              COALESCE((SELECT total_revenue FROM order_statistics WHERE id = ?), 0) + ?,
              ?, ?, ?, 
              COALESCE((SELECT popularity_score FROM order_statistics WHERE id = ?), 0) + ?,
              ?, ?, ?, ?
            )
          `).bind(
            statisticsId, productId, variantId,
            statisticsId, statisticsId, quantity,
            statisticsId, revenue,
            revenue, orderDateStr, orderDateStr,
            statisticsId, basePopularityScore * 0.1,
            periodStartStr + ' 00:00:00', periodEndStr + ' 23:59:59',
            new Date().toISOString(), new Date().toISOString()
          ).run(),
          { usePrimary: true, queryName: 'db-write' }
        );

        processedCount++;

      } catch (itemError) {
        console.error(`Error processing order item ${item.variant_id}:`, itemError);
        // Continue processing other items
      }
    }

    // Clear cache to force refresh
    if (c.env.CACHE) {
      await c.env.CACHE.put('popular_products_month', '', { expirationTtl: 1 });
      await c.env.CACHE.put('trending_products', '', { expirationTtl: 1 });
    }

    return c.json({
      success: true,
      message: `Quick analytics population completed successfully. Processed ${processedCount} items.`,
      processed_count: processedCount
    });

  } catch (error) {
    console.error('Failed to populate analytics quickly:', error);
    return c.json({
      success: false,
      error: 'Failed to populate analytics quickly'
    }, 500);
  }
});

// Public analytics population endpoint (no auth required for testing)
app.post('/analytics/public-populate', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    console.log('Starting public analytics population...');
    
    // Only get the most recent 50 orders to populate quickly
    const orderItemsQuery = `
      SELECT 
        oi.variant_id,
        oi.quantity,
        oi.total_price,
        oi.created_at,
        o.customer_id,
        o.status,
        o.financial_status,
        pv.product_id
      FROM order_items oi
      JOIN orders o ON oi.order_id = o.id
      JOIN product_variants pv ON oi.variant_id = pv.id
      WHERE 1=1
        -- Include all orders for now to see what data we have
      ORDER BY oi.created_at DESC
      LIMIT 50
    `;
    
    const orderItems = await dbSession.query(
      (session) => session.prepare(orderItemsQuery).all(),
      { queryName: 'analytics-populate-order-items' }
    );

    console.log(`Found ${orderItems.results?.length || 0} order items to process`);
    
    let processed = 0;
    let errors = 0;

    if (orderItems.results?.length) {
      for (const item of orderItems.results) {
        try {
          // Check if statistics already exist for this product
          const existingStats = await dbSession.query(
            (session) => session.prepare(`
              SELECT id FROM order_statistics 
              WHERE product_id = ? AND variant_id = ?
              AND period_start = DATE('now', '-30 days')
            `).bind(item.product_id, item.variant_id).first(),
            { queryName: 'check-existing-stats' }
          );

          if (!existingStats) {
            // Insert new statistics record
            await dbSession.query(
              (session) => session.prepare(`
                INSERT INTO order_statistics (
                  id, product_id, variant_id, total_orders, total_quantity, 
                  total_revenue, avg_order_value, last_order_date, first_order_date,
                  popularity_score, period_start, period_end
                ) VALUES (?, ?, ?, 1, ?, ?, ?, ?, ?, 1.0, DATE('now', '-30 days'), DATE('now'))
              `).bind(
                `stats_${item.product_id}_${item.variant_id}_${Date.now()}`,
                item.product_id,
                item.variant_id,
                item.quantity,
                item.total_price,
                item.total_price,
                item.created_at,
                item.created_at
              ).run(),
              { usePrimary: true, queryName: 'insert-order-stats' }
            );
          } else {
            // Update existing statistics
            await dbSession.query(
              (session) => session.prepare(`
                UPDATE order_statistics SET
                  total_orders = total_orders + 1,
                  total_quantity = total_quantity + ?,
                  total_revenue = total_revenue + ?,
                  avg_order_value = total_revenue / total_orders,
                  last_order_date = ?,
                  popularity_score = popularity_score + 0.1,
                  updated_at = CURRENT_TIMESTAMP
                WHERE product_id = ? AND variant_id = ?
                AND period_start = DATE('now', '-30 days')
              `).bind(
                item.quantity,
                item.total_price,
                item.created_at,
                item.product_id,
                item.variant_id
              ).run(),
              { usePrimary: true, queryName: 'update-order-stats' }
            );
          }
          
          processed++;
        } catch (itemError) {
          console.error('Error processing item:', item, itemError);
          errors++;
        }
      }
    }

    // Clear cache to force fresh data
    try {
      await c.env.CACHE.put('popular_products_month', '', { expirationTtl: 1 });
      await c.env.CACHE.put('trending_products', '', { expirationTtl: 1 });
    } catch (cacheError) {
      console.warn('Failed to clear cache:', cacheError);
    }

    console.log(`Analytics population completed. Processed: ${processed}, Errors: ${errors}`);

    return c.json({
      success: true,
      message: 'Public analytics population completed',
      stats: {
        total_items_found: orderItems.results?.length || 0,
        processed,
        errors
      }
    });

  } catch (error) {
    console.error('Public analytics population failed:', error);
    return c.json({
      success: false,
      error: 'Public analytics population failed',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, 500);
  }
});

// Public debug endpoint (no auth required for testing)
app.get('/analytics/debug-orders', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    // Check what order statuses actually exist
    const orderStatuses = await dbSession.query(
      (session) => session.prepare(`
      SELECT DISTINCT status, COUNT(*) as count
      FROM orders 
      GROUP BY status
      ORDER BY count DESC
    `).all(),
      { queryName: 'db-query' }
    );

    // Check what financial statuses exist
    const financialStatuses = await dbSession.query(
      (session) => session.prepare(`
      SELECT DISTINCT financial_status, COUNT(*) as count
      FROM orders 
      GROUP BY financial_status
      ORDER BY count DESC
    `).all(),
      { queryName: 'db-query' }
    );

    // Get sample order items with order info
    const sampleOrderItems = await dbSession.query(
      (session) => session.prepare(`
      SELECT 
        oi.variant_id,
        oi.quantity,
        oi.total_price,
        oi.created_at,
        o.status,
        o.financial_status,
        pv.product_id
      FROM order_items oi
      JOIN orders o ON oi.order_id = o.id
      JOIN product_variants pv ON oi.variant_id = pv.id
      ORDER BY oi.created_at DESC
      LIMIT 10
    `).all(),
      { queryName: 'db-query' }
    );

    // Count total orders and order items
    const totalOrders = await dbSession.query(
      (session) => session.prepare('SELECT COUNT(*) as count FROM orders').first(),
      { queryName: 'db-query' }
    );
    const totalOrderItems = await dbSession.query(
      (session) => session.prepare('SELECT COUNT(*) as count FROM order_items').first(),
      { queryName: 'db-query' }
    );

    // Check variant table exists and has data
    const totalVariants = await dbSession.query(
      (session) => session.prepare('SELECT COUNT(*) as count FROM product_variants').first(),
      { queryName: 'db-query' }
    );

    // Check join results with explicit debugging
    const joinTest = await dbSession.query(
      (session) => session.prepare(`
      SELECT oi.id as item_id, oi.variant_id, pv.id as variant_exists, pv.product_id
      FROM order_items oi
      LEFT JOIN product_variants pv ON oi.variant_id = pv.id
      LIMIT 5
    `).all(),
      { queryName: 'db-query' }
    );

    return c.json({
      success: true,
      data: {
        total_orders: totalOrders?.count || 0,
        total_order_items: totalOrderItems?.count || 0,
        total_variants: totalVariants?.count || 0,
        order_statuses: orderStatuses.results || [],
        financial_statuses: financialStatuses.results || [],
        sample_order_items: sampleOrderItems.results || [],
        join_test: joinTest.results || []
      }
    });
  } catch (error) {
    console.error('Failed to debug orders:', error);
    return c.json({
      success: false,
      error: 'Failed to debug orders'
    }, 500);
  }
});

// Get analytics status and statistics
app.get('/api/analytics/status', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    // Get counts from analytics tables
    const orderStatsCount = await dbSession.query(
      (session) => session.prepare('SELECT COUNT(*) as count FROM order_statistics').first(),
      { queryName: 'db-query' }
    );
    const productAnalyticsCount = await dbSession.query(
      (session) => session.prepare('SELECT COUNT(*) as count FROM product_analytics').first(),
      { queryName: 'db-query' }
    );
    
    // Get sample data
    const sampleOrderStats = await dbSession.query(
      (session) => session.prepare(`
      SELECT product_id, total_orders, total_revenue, popularity_score
      FROM order_statistics
      ORDER BY popularity_score DESC
      LIMIT 5
    `).all(),
      { queryName: 'db-query' }
    );

    const sampleProductAnalytics = await dbSession.query(
      (session) => session.prepare(`
      SELECT product_id, purchases, revenue
      FROM product_analytics
      ORDER BY revenue DESC
      LIMIT 5
    `).all(),
      { queryName: 'db-query' }
    );

    return c.json({
      success: true,
      data: {
        order_statistics_count: orderStatsCount?.count || 0,
        product_analytics_count: productAnalyticsCount?.count || 0,
        sample_order_stats: sampleOrderStats.results || [],
        sample_product_analytics: sampleProductAnalytics.results || []
      }
    });
  } catch (error) {
    console.error('Failed to get analytics status:', error);
    return c.json({
      success: false,
      error: 'Failed to get analytics status'
    }, 500);
  }
});

// Clear all analytics data
app.delete('/api/analytics/clear', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    console.log('Clearing analytics data...');
    
    await dbSession.query(
      (session) => session.prepare('DELETE FROM order_statistics').run(),
      { usePrimary: true, queryName: 'db-write' }
    );
    await dbSession.query(
      (session) => session.prepare('DELETE FROM product_analytics').run(),
      { usePrimary: true, queryName: 'db-write' }
    );

    // Clear cache
    if (c.env.CACHE) {
      await c.env.CACHE.put('popular_products_month', '', { expirationTtl: 60 });
      await c.env.CACHE.put('trending_products', '', { expirationTtl: 60 });
    }

    return c.json({
      success: true,
      message: 'Analytics data cleared successfully'
    });
  } catch (error) {
    console.error('Failed to clear analytics:', error);
    return c.json({
      success: false,
      error: 'Failed to clear analytics'
    }, 500);
  }
});

// Debug endpoint to check actual order data
app.get('/api/analytics/debug-orders', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    // Check what order statuses actually exist
    const orderStatuses = await dbSession.query(
      (session) => session.prepare(`
      SELECT DISTINCT status, COUNT(*) as count
      FROM orders 
      GROUP BY status
      ORDER BY count DESC
    `).all(),
      { queryName: 'db-query' }
    );

    // Check what financial statuses exist
    const financialStatuses = await dbSession.query(
      (session) => session.prepare(`
      SELECT DISTINCT financial_status, COUNT(*) as count
      FROM orders 
      GROUP BY financial_status
      ORDER BY count DESC
    `).all(),
      { queryName: 'db-query' }
    );

    // Get sample order items with order info
    const sampleOrderItems = await dbSession.query(
      (session) => session.prepare(`
      SELECT 
        oi.variant_id,
        oi.quantity,
        oi.total_price,
        oi.created_at,
        o.status,
        o.financial_status,
        pv.product_id
      FROM order_items oi
      JOIN orders o ON oi.order_id = o.id
      JOIN product_variants pv ON oi.variant_id = pv.id
      ORDER BY oi.created_at DESC
      LIMIT 10
    `).all(),
      { queryName: 'db-query' }
    );

    // Count total orders and order items
    const totalOrders = await dbSession.query(
      (session) => session.prepare('SELECT COUNT(*) as count FROM orders').first(),
      { queryName: 'db-query' }
    );
    const totalOrderItems = await dbSession.query(
      (session) => session.prepare('SELECT COUNT(*) as count FROM order_items').first(),
      { queryName: 'db-query' }
    );

    return c.json({
      success: true,
      data: {
        total_orders: totalOrders?.count || 0,
        total_order_items: totalOrderItems?.count || 0,
        order_statuses: orderStatuses.results || [],
        financial_statuses: financialStatuses.results || [],
        sample_order_items: sampleOrderItems.results || []
      }
    });
  } catch (error) {
    console.error('Failed to debug orders:', error);
    return c.json({
      success: false,
      error: 'Failed to debug orders'
    }, 500);
  }
});

// ===========================================
// PROMOTIONS ROUTES
// ===========================================

// Get all promotions
app.get('/api/promotions', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '20');
    const search = c.req.query('search') || '';
    const status = c.req.query('status') || '';
    const type = c.req.query('type') || '';
    const offset = (page - 1) * limit;

    let query = `
      SELECT 
        p.id, p.code, p.campaign_id, p.is_automatic, p.type, p.status,
        p.created_at, p.updated_at,
        pc.name as campaign_name, pc.description as campaign_description,
        pc.starts_at as campaign_starts_at, pc.ends_at as campaign_ends_at,
        pam.type as application_type, pam.value as discount_value,
        pam.currency_code, pam.target_type, pam.allocation,
        COUNT(DISTINCT pr.id) as rules_count
      FROM promotion p
      LEFT JOIN promotion_campaign pc ON p.campaign_id = pc.id
      LEFT JOIN promotion_application_method pam ON p.id = pam.promotion_id
      LEFT JOIN promotion_promotion_rule ppr ON p.id = ppr.promotion_id
      LEFT JOIN promotion_rule pr ON ppr.promotion_rule_id = pr.id
      WHERE p.deleted_at IS NULL
    `;

    const params: any[] = [];
    
    if (search) {
      query += ` AND (p.code LIKE ? OR pc.name LIKE ?)`;
      params.push(`%${search}%`, `%${search}%`);
    }
    
    if (status) {
      query += ` AND p.status = ?`;
      params.push(status);
    }
    
    if (type) {
      query += ` AND p.type = ?`;
      params.push(type);
    }

    query += ` GROUP BY p.id ORDER BY p.created_at DESC LIMIT ? OFFSET ?`;
    params.push(limit, offset);

    const promotions = await dbSession.query(
      (session) => session.prepare(query).bind(...params).all(),
      { queryName: 'db-query' }
    );

    // Get total count
    let countQuery = `SELECT COUNT(DISTINCT p.id) as total FROM promotion p`;
    let countParams: any[] = [];
    
    if (search || status || type) {
      countQuery += ` LEFT JOIN promotion_campaign pc ON p.campaign_id = pc.id WHERE p.deleted_at IS NULL`;
      
      if (search) {
        countQuery += ` AND (p.code LIKE ? OR pc.name LIKE ?)`;
        countParams.push(`%${search}%`, `%${search}%`);
      }
      
      if (status) {
        countQuery += ` AND p.status = ?`;
        countParams.push(status);
      }
      
      if (type) {
        countQuery += ` AND p.type = ?`;
        countParams.push(type);
      }
    } else {
      countQuery += ` WHERE p.deleted_at IS NULL`;
    }

    const totalResult = await dbSession.query(
      (session) => session.prepare(countQuery).bind(...countParams).first(),
      { queryName: 'db-query' }
    );

    return c.json({
      data: promotions.results,
      pagination: {
        page,
        limit,
        total: totalResult?.total || 0,
        pages: Math.ceil(Number(totalResult?.total || 0) / limit)
      }
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch promotions');
  }
});

// Get single promotion with details
app.get('/api/promotions/:id', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const id = c.req.param('id');

    const promotion = await dbSession.query(
      (session) => session.prepare(`
      SELECT 
        p.*,
        pc.name as campaign_name, pc.description as campaign_description,
        pc.starts_at as campaign_starts_at, pc.ends_at as campaign_ends_at,
        pam.type as application_type, pam.value as discount_value,
        pam.currency_code, pam.target_type, pam.allocation, pam.max_quantity,
        pam.apply_to_quantity, pam.buy_rules_min_quantity
      FROM promotion p
      LEFT JOIN promotion_campaign pc ON p.campaign_id = pc.id
      LEFT JOIN promotion_application_method pam ON p.id = pam.promotion_id
      WHERE p.id = ? AND p.deleted_at IS NULL
    `).bind(id).first(),
      { queryName: 'db-query' }
    );

    if (!promotion) {
      return c.json({ error: 'Promotion not found' }, 404);
    }

    // Get promotion rules
    const rules = await dbSession.query(
      (session) => session.prepare(`
      SELECT pr.*, prv.value
      FROM promotion_rule pr
      JOIN promotion_promotion_rule ppr ON pr.id = ppr.promotion_rule_id
      LEFT JOIN promotion_rule_value prv ON pr.id = prv.promotion_rule_id
      WHERE ppr.promotion_id = ? AND pr.deleted_at IS NULL
      ORDER BY pr.created_at
    `).bind(id).all(),
      { queryName: 'db-query' }
    );

    return c.json({
      promotion: {
        ...promotion,
        rules: rules.results
      }
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch promotion');
  }
});

// Create promotion
app.post('/api/promotions', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const data = await c.req.json();
    const promotionId = generateId('promo');

    // Create promotion
    await dbSession.query(
      (session) => session.prepare(`
      INSERT INTO promotion (
        id, code, campaign_id, is_automatic, type, status,
        created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `).bind(
      promotionId,
      data.code,
      data.campaign_id || null,
      data.is_automatic ? 1 : 0,
      data.type,
      data.status || 'inactive'
    ).run(),
      { usePrimary: true, queryName: 'db-write' }
    );

    // Create application method
    if (data.application_method) {
      const applicationMethodId = generateId('pam');
      await dbSession.query(
      (session) => session.prepare(`
        INSERT INTO promotion_application_method (
          id, promotion_id, type, target_type, value, currency_code,
          allocation, max_quantity, apply_to_quantity, buy_rules_min_quantity,
          created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      `).bind(
        applicationMethodId, promotionId,
        data.application_method.type,
        data.application_method.target_type,
        data.application_method.value || null,
        data.application_method.currency_code || null,
        data.application_method.allocation || null,
        data.application_method.max_quantity || null,
        data.application_method.apply_to_quantity || null,
        data.application_method.buy_rules_min_quantity || null
      ).run(),
      { usePrimary: true, queryName: 'db-write' }
    );
    }

    // Create promotion rules
    if (data.rules && Array.isArray(data.rules)) {
      for (const rule of data.rules) {
        const ruleId = generateId('prule');
        
        await dbSession.query(
      (session) => session.prepare(`
          INSERT INTO promotion_rule (
            id, attribute, operator, description,
            created_at, updated_at
          ) VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        `).bind(ruleId, rule.attribute, rule.operator, rule.description || null).run(),
      { usePrimary: true, queryName: 'db-write' }
    );

        // Link rule to promotion
        await dbSession.query(
      (session) => session.prepare(`
          INSERT INTO promotion_promotion_rule (promotion_id, promotion_rule_id)
          VALUES (?, ?)
        `).bind(promotionId, ruleId).run(),
      { usePrimary: true, queryName: 'db-write' }
    );

        // Add rule values
        if (rule.values && Array.isArray(rule.values)) {
          for (const value of rule.values) {
            const valueId = generateId('prval');
            await dbSession.query(
      (session) => session.prepare(`
              INSERT INTO promotion_rule_value (
                id, promotion_rule_id, value,
                created_at, updated_at
              ) VALUES (?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            `).bind(valueId, ruleId, value).run(),
      { usePrimary: true, queryName: 'db-write' }
    );
          }
        }
      }
    }

    return c.json({ id: promotionId, message: 'Promotion created successfully' }, 201);

  } catch (error) {
    return handleError(c, error, 'Failed to create promotion');
  }
});

// Update promotion
app.put('/api/promotions/:id', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const id = c.req.param('id');
    const data = await c.req.json();

    // Update promotion
    await dbSession.query(
      (session) => session.prepare(`
      UPDATE promotion SET
        code = ?, campaign_id = ?, is_automatic = ?, type = ?, status = ?,
        updated_at = CURRENT_TIMESTAMP
      WHERE id = ? AND deleted_at IS NULL
    `).bind(
      data.code,
      data.campaign_id || null,
      data.is_automatic ? 1 : 0,
      data.type,
      data.status,
      id
    ).run(),
      { usePrimary: true, queryName: 'db-write' }
    );

    // Update application method if provided
    if (data.application_method) {
      const existingMethod = await dbSession.query(
      (session) => session.prepare(`
        SELECT id FROM promotion_application_method WHERE promotion_id = ?
      `).bind(id).first(),
      { queryName: 'db-query' }
    );

      if (existingMethod) {
        await dbSession.query(
      (session) => session.prepare(`
          UPDATE promotion_application_method SET
            type = ?, target_type = ?, value = ?, currency_code = ?,
            allocation = ?, max_quantity = ?, apply_to_quantity = ?,
            buy_rules_min_quantity = ?, updated_at = CURRENT_TIMESTAMP
          WHERE promotion_id = ?
        `).bind(
          data.application_method.type,
          data.application_method.target_type,
          data.application_method.value || null,
          data.application_method.currency_code || null,
          data.application_method.allocation || null,
          data.application_method.max_quantity || null,
          data.application_method.apply_to_quantity || null,
          data.application_method.buy_rules_min_quantity || null,
          id
        ).run(),
      { usePrimary: true, queryName: 'db-write' }
    );
      }
    }

    return c.json({ message: 'Promotion updated successfully' });

  } catch (error) {
    return handleError(c, error, 'Failed to update promotion');
  }
});

// Delete promotion
app.delete('/api/promotions/:id', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const id = c.req.param('id');

    await dbSession.query(
      (session) => session.prepare(`
      UPDATE promotion 
      SET deleted_at = CURRENT_TIMESTAMP 
      WHERE id = ?
    `).bind(id).run(),
      { usePrimary: true, queryName: 'db-write' }
    );

    return c.json({ message: 'Promotion deleted successfully' });

  } catch (error) {
    return handleError(c, error, 'Failed to delete promotion');
  }
});

// Bulk delete promotions
app.post('/api/promotions/bulk-delete', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const { promotionIds } = await c.req.json();

    if (!promotionIds || !Array.isArray(promotionIds) || promotionIds.length === 0) {
      return c.json({ error: 'Promotion IDs are required' }, 400);
    }

    const placeholders = promotionIds.map(() => '?').join(',');
    
    await dbSession.query(
      (session) => session.prepare(`
      UPDATE promotion 
      SET deleted_at = CURRENT_TIMESTAMP 
      WHERE id IN (${placeholders})
    `).bind(...promotionIds).run(),
      { usePrimary: true, queryName: 'db-write' }
    );

    return c.json({ message: `${promotionIds.length} promotions deleted successfully` });

  } catch (error) {
    return handleError(c, error, 'Failed to bulk delete promotions');
  }
});

// ===========================================
// JOURNAL ROUTES
// ===========================================

// Get all journal entries
app.get('/api/journal', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '20');
    const search = c.req.query('search') || '';
    const published = c.req.query('published');
    const author = c.req.query('author') || '';
    const offset = (page - 1) * limit;

    let query = `
      SELECT 
        id, title, content, excerpt, slug, author, image_url,
        published, published_at, tags, metadata,
        created_at, updated_at
      FROM journal_entry
      WHERE deleted_at IS NULL
    `;

    const params = [];
    
    if (search) {
      query += ` AND (title LIKE ? OR content LIKE ? OR excerpt LIKE ?)`;
      params.push(`%${search}%`, `%${search}%`, `%${search}%`);
    }
    
    if (published === 'true') {
      query += ` AND published = 1`;
    } else if (published === 'false') {
      query += ` AND published = 0`;
    }
    
    if (author) {
      query += ` AND author LIKE ?`;
      params.push(`%${author}%`);
    }

    query += ` ORDER BY created_at DESC LIMIT ? OFFSET ?`;
    params.push(limit, offset);

    const entries = await dbSession.query(
      (session) => session.prepare(query).bind(...params).all(),
      { queryName: 'db-query' }
    );

    // Get total count
    let countQuery = `SELECT COUNT(*) as total FROM journal_entry WHERE deleted_at IS NULL`;
    let countParams = [];
    
    if (search || published || author) {
      if (search) {
        countQuery += ` AND (title LIKE ? OR content LIKE ? OR excerpt LIKE ?)`;
        countParams.push(`%${search}%`, `%${search}%`, `%${search}%`);
      }
      
      if (published === 'true') {
        countQuery += ` AND published = 1`;
      } else if (published === 'false') {
        countQuery += ` AND published = 0`;
      }
      
      if (author) {
        countQuery += ` AND author LIKE ?`;
        countParams.push(`%${author}%`);
      }
    }

    const totalResult = await dbSession.query(
      (session) => session.prepare(countQuery).bind(...countParams).first(),
      { queryName: 'db-query' }
    );

    // Parse JSON fields and add calculated fields
    const formattedEntries = entries.results.map(entry => ({
      ...entry,
      metadata: entry.metadata ? JSON.parse(String(entry.metadata)) : null,
      tags: entry.tags ? String(entry.tags).split(',').map((tag: string) => tag.trim()) : [],
      word_count: entry.content ? String(entry.content).split(/\s+/).length : 0,
      reading_time: entry.content ? Math.ceil(String(entry.content).split(/\s+/).length / 200) : 0,
    }));

    return c.json({
      data: formattedEntries,
      pagination: {
        page,
        limit,
        total: totalResult?.total || 0,
        pages: Math.ceil(Number(totalResult?.total || 0) / limit)
      }
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch journal entries');
  }
});

// Get single journal entry
app.get('/api/journal/:id', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const id = c.req.param('id');

    const entry = await dbSession.query(
      (session) => session.prepare(`
      SELECT *
      FROM journal_entry
      WHERE id = ? AND deleted_at IS NULL
    `).bind(id).first(),
      { queryName: 'db-query' }
    );

    if (!entry) {
      return c.json({ error: 'Journal entry not found' }, 404);
    }

    return c.json({
      entry: {
        ...entry,
        metadata: entry.metadata ? JSON.parse(String(entry.metadata)) : null,
        tags: entry.tags ? String(entry.tags).split(',').map((tag: string) => tag.trim()) : [],
        word_count: entry.content ? String(entry.content).split(/\s+/).length : 0,
        reading_time: entry.content ? Math.ceil(String(entry.content).split(/\s+/).length / 200) : 0,
      }
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch journal entry');
  }
});

// Create journal entry
app.post('/api/journal', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const data = await c.req.json();
    const entryId = generateId('journal');

    // Generate slug if not provided
    let slug = data.slug;
    if (!slug) {
      slug = data.title
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/^-+|-+$/g, '');
    }

    // Ensure slug is unique
    const existingSlug = await dbSession.query(
      (session) => session.prepare(`
      SELECT id FROM journal_entry WHERE slug = ? AND deleted_at IS NULL
    `).bind(slug).first(),
      { queryName: 'db-query' }
    );

    if (existingSlug) {
      slug = `${slug}-${Date.now()}`;
    }

    await dbSession.query(
      (session) => session.prepare(`
      INSERT INTO journal_entry (
        id, title, content, excerpt, slug, author, image_url,
        published, published_at, tags, metadata,
        created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `).bind(
      entryId,
      data.title,
      data.content || '',
      data.excerpt || null,
      slug,
      data.author || null,
      data.image_url || null,
      data.published ? 1 : 0,
      data.published && data.published_at ? data.published_at : (data.published ? new Date().toISOString() : null),
      Array.isArray(data.tags) ? data.tags.join(', ') : (data.tags || null),
      data.metadata ? JSON.stringify(data.metadata) : null
    ).run(),
      { usePrimary: true, queryName: 'db-write' }
    );

    return c.json({ id: entryId, slug, message: 'Journal entry created successfully' }, 201);

  } catch (error) {
    return handleError(c, error, 'Failed to create journal entry');
  }
});

// Update journal entry
app.put('/api/journal/:id', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const id = c.req.param('id');
    const data = await c.req.json();

    // Update slug if title changed
    let slug = data.slug;
    if (!slug && data.title) {
      slug = data.title
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/^-+|-+$/g, '');
      
      // Check if slug already exists (excluding current entry)
      const existingSlug = await dbSession.query(
      (session) => session.prepare(`
        SELECT id FROM journal_entry WHERE slug = ? AND id != ? AND deleted_at IS NULL
      `).bind(slug, id).first(),
      { queryName: 'db-query' }
    );

      if (existingSlug) {
        slug = `${slug}-${Date.now()}`;
      }
    }

    await dbSession.query(
      (session) => session.prepare(`
      UPDATE journal_entry SET
        title = ?, content = ?, excerpt = ?, slug = ?, author = ?, image_url = ?,
        published = ?, published_at = ?, tags = ?, metadata = ?,
        updated_at = CURRENT_TIMESTAMP
      WHERE id = ? AND deleted_at IS NULL
    `).bind(
      data.title,
      data.content || '',
      data.excerpt || null,
      slug,
      data.author || null,
      data.image_url || null,
      data.published ? 1 : 0,
      data.published && data.published_at ? data.published_at : (data.published ? new Date().toISOString() : null),
      Array.isArray(data.tags) ? data.tags.join(', ') : (data.tags || null),
      data.metadata ? JSON.stringify(data.metadata) : null,
      id
    ).run(),
      { usePrimary: true, queryName: 'db-write' }
    );

    return c.json({ message: 'Journal entry updated successfully' });

  } catch (error) {
    return handleError(c, error, 'Failed to update journal entry');
  }
});

// Delete journal entry
app.delete('/api/journal/:id', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const id = c.req.param('id');

    await dbSession.query(
      (session) => session.prepare(`
      UPDATE journal_entry 
      SET deleted_at = CURRENT_TIMESTAMP 
      WHERE id = ?
    `).bind(id).run(),
      { usePrimary: true, queryName: 'db-write' }
    );

    return c.json({ message: 'Journal entry deleted successfully' });

  } catch (error) {
    return handleError(c, error, 'Failed to delete journal entry');
  }
});

// Publish/unpublish journal entry
app.post('/api/journal/:id/publish', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const id = c.req.param('id');

    const entry = await dbSession.query(
      (session) => session.prepare(`
      SELECT published FROM journal_entry WHERE id = ? AND deleted_at IS NULL
    `).bind(id).first(),
      { queryName: 'db-query' }
    );

    if (!entry) {
      return c.json({ error: 'Journal entry not found' }, 404);
    }

    const newPublishedState = !entry.published;
    const publishedAt = newPublishedState ? new Date().toISOString() : null;

    await dbSession.query(
      (session) => session.prepare(`
      UPDATE journal_entry 
      SET published = ?, published_at = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `).bind(newPublishedState ? 1 : 0, publishedAt, id).run(),
      { usePrimary: true, queryName: 'db-write' }
    );

    return c.json({ 
      message: `Journal entry ${newPublishedState ? 'published' : 'unpublished'} successfully`,
      published: newPublishedState
    });

  } catch (error) {
    return handleError(c, error, 'Failed to update journal entry status');
  }
});

// Bulk delete journal entries
app.post('/api/journal/bulk-delete', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const { entryIds } = await c.req.json();

    if (!entryIds || !Array.isArray(entryIds) || entryIds.length === 0) {
      return c.json({ error: 'Entry IDs are required' }, 400);
    }

    const placeholders = entryIds.map(() => '?').join(',');
    
    await dbSession.query(
      (session) => session.prepare(`
      UPDATE journal_entry 
      SET deleted_at = CURRENT_TIMESTAMP 
      WHERE id IN (${placeholders})
    `).bind(...entryIds).run(),
      { usePrimary: true, queryName: 'db-write' }
    );

    return c.json({ message: `${entryIds.length} journal entries deleted successfully` });

  } catch (error) {
    return handleError(c, error, 'Failed to bulk delete journal entries');
  }
});

// Get journal analytics
app.get('/api/journal/analytics', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const [totalEntries, publishedEntries, draftEntries, monthlyStats] = await Promise.all([
      c.env.DB.prepare(`
        SELECT COUNT(*) as total FROM journal_entry WHERE deleted_at IS NULL
      `).first(),
      
      c.env.DB.prepare(`
        SELECT COUNT(*) as published FROM journal_entry 
        WHERE published = 1 AND deleted_at IS NULL
      `).first(),
      
      c.env.DB.prepare(`
        SELECT COUNT(*) as drafts FROM journal_entry 
        WHERE published = 0 AND deleted_at IS NULL
      `).first(),
      
      c.env.DB.prepare(`
        SELECT 
          COUNT(*) as entries,
          strftime('%Y-%m', created_at) as month
        FROM journal_entry 
        WHERE deleted_at IS NULL 
        GROUP BY strftime('%Y-%m', created_at)
        ORDER BY month DESC
        LIMIT 12
      `).all()
    ]);

    return c.json({
      analytics: {
        total_entries: totalEntries?.total || 0,
        published_entries: publishedEntries?.published || 0,
        draft_entries: draftEntries?.drafts || 0,
        monthly_stats: monthlyStats.results
      }
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch journal analytics');
  }
});

// ===========================================
// GIFT CARDS ROUTES
// ===========================================

// Get all gift cards (products with is_giftcard = true)
app.get('/api/gift-cards', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '20');
    const search = c.req.query('search') || '';
    const status = c.req.query('status') || '';
    const offset = (page - 1) * limit;

    let query = `
      SELECT 
        p.id, p.handle, p.status, p.thumbnail, p.metadata, p.created_at, p.updated_at,
        pt.title, pt.subtitle, pt.description,
        COUNT(pv.id) as variant_count,
        MIN(pr.amount) as min_price,
        MAX(pr.amount) as max_price
      FROM product p
      LEFT JOIN product_translations pt ON p.id = pt.product_id AND pt.language_code ='ro'
      LEFT JOIN product_variant pv ON p.id = pv.product_id AND pv.deleted_at IS NULL
      LEFT JOIN product_variant_price_set pvps ON pv.id = pvps.variant_id
      LEFT JOIN price pr ON pvps.price_set_id = pr.price_set_id
      WHERE p.deleted_at IS NULL AND p.is_giftcard = 1
    `;

    const params: any[] = [];
    
    if (search) {
      query += ` AND (pt.title LIKE ? OR p.handle LIKE ?)`;
      params.push(`%${search}%`, `%${search}%`);
    }
    
    if (status) {
      query += ` AND p.status = ?`;
      params.push(status);
    }

    query += ` GROUP BY p.id ORDER BY p.created_at DESC LIMIT ? OFFSET ?`;
    params.push(limit, offset);

    const giftCards = await dbSession.query(
      (session) => session.prepare(query).bind(...params).all(),
      { queryName: 'db-query' }
    );

    // Get total count
    let countQuery = `SELECT COUNT(*) as total FROM product p WHERE p.deleted_at IS NULL AND p.is_giftcard = 1`;
    let countParams: any[] = [];
    
    if (search || status) {
      countQuery += ` LEFT JOIN product_translations pt ON p.id = pt.product_id AND pt.language_code ='ro'`;
      
      if (search) {
        countQuery += ` AND (pt.title LIKE ? OR p.handle LIKE ?)`;
        countParams.push(`%${search}%`, `%${search}%`);
      }
      
      if (status) {
        countQuery += ` AND p.status = ?`;
        countParams.push(status);
      }
    }

    const totalResult = await dbSession.query(
      (session) => session.prepare(countQuery).bind(...countParams).first(),
      { queryName: 'db-query' }
    );

    return c.json({
      gift_cards: giftCards.results,
      pagination: {
        page,
        limit,
        total: totalResult?.total || 0,
        totalPages: Math.ceil(Number(totalResult?.total || 0) / limit)
      }
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch gift cards');
  }
});

// Get single gift card
app.get('/api/gift-cards/:id', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const id = c.req.param('id');

    const giftCard = await dbSession.query(
      (session) => session.prepare(`
      SELECT 
        p.id, p.handle, p.status, p.thumbnail, p.metadata, p.created_at, p.updated_at,
        pt.title, pt.subtitle, pt.description
      FROM product p
      LEFT JOIN product_translations pt ON p.id = pt.product_id AND pt.language_code ='ro'
      WHERE p.id = ? AND p.deleted_at IS NULL AND p.is_giftcard = 1
    `).bind(id).first(),
      { queryName: 'db-query' }
    );

    if (!giftCard) {
      return c.json({ error: 'Gift card not found' }, 404);
    }

    // Get variants with prices
    const variants = await dbSession.query(
      (session) => session.prepare(`
      SELECT 
        pv.id, pv.title, pv.sku, pv.metadata,
        pr.amount, pr.currency_code
      FROM product_variant pv
      LEFT JOIN product_variant_price_set pvps ON pv.id = pvps.variant_id
      LEFT JOIN price pr ON pvps.price_set_id = pr.price_set_id
      WHERE pv.product_id = ? AND pv.deleted_at IS NULL
      ORDER BY pr.amount
    `).bind(id).all(),
      { queryName: 'db-query' }
    );

    return c.json({
      gift_card: {
        ...giftCard,
        variants: variants.results,
      }
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch gift card');
  }
});

// Create gift card
app.post('/api/gift-cards', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const data = await c.req.json();
    const giftCardId = generateId('gcard');

    // Create gift card product
    await c.env.DB.prepare(`
      INSERT INTO product (
        id, handle, status, thumbnail, is_giftcard, discountable,
        metadata, created_at, updated_at
      ) VALUES (?, ?, ?, ?, 1, 0, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `).bind(
      giftCardId,
      data.handle,
      data.status || 'draft',
      data.thumbnail || null,
      JSON.stringify(data.metadata || {})
    ).run();

    // Create product translation
    if (data.title) {
      await dbSession.query(

        (session) => session.prepare(`
        INSERT INTO product_translations (
          product_id, language_code, title, subtitle, description
        ) VALUES (?, 'ro', ?, ?, ?)
      `).bind(
        giftCardId,
        data.title,
        data.subtitle || null,
        data.description || null
      ).run(),

        { usePrimary: true, queryName: 'db-write' }

      );
    }

    return c.json({ id: giftCardId, message: 'Gift card created successfully' }, 201);

  } catch (error) {
    return handleError(c, error, 'Failed to create gift card');
  }
});

// Update gift card
app.put('/api/gift-cards/:id', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const id = c.req.param('id');
    const data = await c.req.json();

    // Update gift card
    await c.env.DB.prepare(`
      UPDATE product SET
        handle = ?, status = ?, thumbnail = ?, metadata = ?, 
        updated_at = CURRENT_TIMESTAMP
      WHERE id = ? AND is_giftcard = 1
    `).bind(
      data.handle,
      data.status,
      data.thumbnail || null,
      JSON.stringify(data.metadata || {}),
      id
    ).run();

    // Update translation
    const existingTranslation = await dbSession.query(
      (session) => session.prepare(`
      SELECT product_id FROM product_translations 
      WHERE product_id = ? AND language_code ='ro'
    `).bind(id).first(),
      { queryName: 'db-query' }
    );

    if (existingTranslation) {
      await dbSession.query(
      (session) => session.prepare(`
        UPDATE product_translations SET
          title = ?, subtitle = ?, description = ?
        WHERE product_id = ? AND language_code ='ro'
      `).bind(
        data.title,
        data.subtitle || null,
        data.description || null,
        id
      ).run(),
      { usePrimary: true, queryName: 'db-write' }
    );
    } else if (data.title) {
      await dbSession.query(

        (session) => session.prepare(`
        INSERT INTO product_translations (
          product_id, language_code, title, subtitle, description
        ) VALUES (?, 'ro', ?, ?, ?)
      `).bind(
        id,
        data.title,
        data.subtitle || null,
        data.description || null
      ).run(),

        { usePrimary: true, queryName: 'db-write' }

      );
    }

    return c.json({ message: 'Gift card updated successfully' });

  } catch (error) {
    return handleError(c, error, 'Failed to update gift card');
  }
});

// Delete gift card
app.delete('/api/gift-cards/:id', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const id = c.req.param('id');

    await dbSession.query(
      (session) => session.prepare(`
      UPDATE product 
      SET deleted_at = CURRENT_TIMESTAMP 
      WHERE id = ? AND is_giftcard = 1
    `).bind(id).run(),
      { usePrimary: true, queryName: 'db-write' }
    );

    return c.json({ message: 'Gift card deleted successfully' });

  } catch (error) {
    return handleError(c, error, 'Failed to delete gift card');
  }
});

// Bulk delete gift cards
app.post('/api/gift-cards/bulk-delete', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const { giftCardIds } = await c.req.json();

    if (!giftCardIds || !Array.isArray(giftCardIds) || giftCardIds.length === 0) {
      return c.json({ error: 'Gift card IDs are required' }, 400);
    }

    const placeholders = giftCardIds.map(() => '?').join(',');
    
    await dbSession.query(
      (session) => session.prepare(`
      UPDATE product 
      SET deleted_at = CURRENT_TIMESTAMP 
      WHERE id IN (${placeholders}) AND is_giftcard = 1
    `).bind(...giftCardIds).run(),
      { usePrimary: true, queryName: 'db-write' }
    );

    return c.json({ message: `${giftCardIds.length} gift cards deleted successfully` });

  } catch (error) {
    return handleError(c, error, 'Failed to bulk delete gift cards');
  }
});

// ===========================================
// CAMPAIGNS ROUTES
// ===========================================

// Get all promotion campaigns
app.get('/api/campaigns', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '20');
    const search = c.req.query('search') || '';
    const status = c.req.query('status') || '';
    const offset = (page - 1) * limit;

    let query = `
      SELECT 
        pc.id, pc.name, pc.description, pc.campaign_identifier,
        pc.starts_at, pc.ends_at, pc.created_at, pc.updated_at,
        COUNT(DISTINCT p.id) as promotion_count,
        CASE 
          WHEN pc.ends_at < datetime('now') THEN 'expired'
          WHEN pc.starts_at > datetime('now') THEN 'scheduled'
          WHEN pc.starts_at <= datetime('now') AND (pc.ends_at IS NULL OR pc.ends_at >= datetime('now')) THEN 'active'
          ELSE 'inactive'
        END as status
      FROM promotion_campaign pc
      LEFT JOIN promotion p ON pc.id = p.campaign_id AND p.deleted_at IS NULL
      WHERE pc.deleted_at IS NULL
    `;

    const params: any[] = [];
    
    if (search) {
      query += ` AND (pc.name LIKE ? OR pc.description LIKE ? OR pc.campaign_identifier LIKE ?)`;
      params.push(`%${search}%`, `%${search}%`, `%${search}%`);
    }

    query += ` GROUP BY pc.id`;
    
    if (status) {
      query += ` HAVING status = ?`;
      params.push(status);
    }

    query += ` ORDER BY pc.created_at DESC LIMIT ? OFFSET ?`;
    params.push(limit, offset);

    const campaigns = await dbSession.query(
      (session) => session.prepare(query).bind(...params).all(),
      { queryName: 'db-query' }
    );

    // Get total count
    let countQuery = `
      SELECT COUNT(DISTINCT pc.id) as total 
      FROM promotion_campaign pc
      WHERE pc.deleted_at IS NULL
    `;
    let countParams: any[] = [];
    
    if (search) {
      countQuery += ` AND (pc.name LIKE ? OR pc.description LIKE ? OR pc.campaign_identifier LIKE ?)`;
      countParams.push(`%${search}%`, `%${search}%`, `%${search}%`);
    }

    const totalResult = await dbSession.query(
      (session) => session.prepare(countQuery).bind(...countParams).first(),
      { queryName: 'db-query' }
    );

    return c.json({
      campaigns: campaigns.results,
      pagination: {
        page,
        limit,
        total: totalResult?.total || 0,
        totalPages: Math.ceil(Number(totalResult?.total || 0) / limit)
      }
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch campaigns');
  }
});

// Get single campaign with promotions
app.get('/api/campaigns/:id', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const id = c.req.param('id');

    const campaign = await dbSession.query(
      (session) => session.prepare(`
      SELECT *
      FROM promotion_campaign
      WHERE id = ? AND deleted_at IS NULL
    `).bind(id).first(),
      { queryName: 'db-query' }
    );

    if (!campaign) {
      return c.json({ error: 'Campaign not found' }, 404);
    }

    // Get associated promotions
    const promotions = await dbSession.query(
      (session) => session.prepare(`
      SELECT 
        p.id, p.code, p.type, p.status, p.is_automatic,
        pam.type as application_type, pam.value as discount_value,
        pam.currency_code, pam.target_type
      FROM promotion p
      LEFT JOIN promotion_application_method pam ON p.id = pam.promotion_id
      WHERE p.campaign_id = ? AND p.deleted_at IS NULL
      ORDER BY p.created_at DESC
    `).bind(id).all(),
      { queryName: 'db-query' }
    );

    return c.json({
      campaign: {
        ...campaign,
        promotions: promotions.results
      }
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch campaign');
  }
});

// Create campaign
app.post('/api/campaigns', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const data = await c.req.json();
    const campaignId = generateId('camp');

    await dbSession.query(
      (session) => session.prepare(`
      INSERT INTO promotion_campaign (
        id, name, description, campaign_identifier, starts_at, ends_at,
        created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `).bind(
      campaignId,
      data.name,
      data.description || null,
      data.campaign_identifier,
      data.starts_at || null,
      data.ends_at || null
    ).run(),
      { usePrimary: true, queryName: 'db-write' }
    );

    return c.json({ id: campaignId, message: 'Campaign created successfully' }, 201);

  } catch (error) {
    return handleError(c, error, 'Failed to create campaign');
  }
});

// Update campaign
app.put('/api/campaigns/:id', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const id = c.req.param('id');
    const data = await c.req.json();

    await dbSession.query(
      (session) => session.prepare(`
      UPDATE promotion_campaign SET
        name = ?, description = ?, campaign_identifier = ?,
        starts_at = ?, ends_at = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ? AND deleted_at IS NULL
    `).bind(
      data.name,
      data.description || null,
      data.campaign_identifier,
      data.starts_at || null,
      data.ends_at || null,
      id
    ).run(),
      { usePrimary: true, queryName: 'db-write' }
    );

    return c.json({ message: 'Campaign updated successfully' });

  } catch (error) {
    return handleError(c, error, 'Failed to update campaign');
  }
});

// Delete campaign
app.delete('/api/campaigns/:id', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const id = c.req.param('id');

    await dbSession.query(
      (session) => session.prepare(`
      UPDATE promotion_campaign 
      SET deleted_at = CURRENT_TIMESTAMP 
      WHERE id = ?
    `).bind(id).run(),
      { usePrimary: true, queryName: 'db-write' }
    );

    return c.json({ message: 'Campaign deleted successfully' });

  } catch (error) {
    return handleError(c, error, 'Failed to delete campaign');
  }
});

// Bulk delete campaigns
app.post('/api/campaigns/bulk-delete', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const { campaignIds } = await c.req.json();

    if (!campaignIds || !Array.isArray(campaignIds) || campaignIds.length === 0) {
      return c.json({ error: 'Campaign IDs are required' }, 400);
    }

    const placeholders = campaignIds.map(() => '?').join(',');
    
    await dbSession.query(
      (session) => session.prepare(`
      UPDATE promotion_campaign 
      SET deleted_at = CURRENT_TIMESTAMP 
      WHERE id IN (${placeholders})
    `).bind(...campaignIds).run(),
      { usePrimary: true, queryName: 'db-write' }
    );

    return c.json({ message: `${campaignIds.length} campaigns deleted successfully` });

  } catch (error) {
    return handleError(c, error, 'Failed to bulk delete campaigns');
  }
});

// ===========================================
// FILE MANAGEMENT ROUTES
// ===========================================

// Get all folders
app.get('/api/files/folders', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const search = c.req.query('search') || '';
    const parentId = c.req.query('parent_id');

    let query = `
      SELECT 
        id, name, parent_id, path, metadata, created_at, updated_at,
        (SELECT COUNT(*) FROM file_folder cf WHERE cf.parent_id = f.id) as subfolder_count,
        (SELECT COUNT(*) FROM file_upload fu WHERE fu.folder_id = f.id) as file_count
      FROM file_folder f
      WHERE deleted_at IS NULL
    `;

    const params: any[] = [];

    if (search) {
      query += ` AND name LIKE ?`;
      params.push(`%${search}%`);
    }

    if (parentId) {
      query += ` AND parent_id = ?`;
      params.push(parentId);
    } else {
      query += ` AND parent_id IS NULL`;
    }

    query += ` ORDER BY name`;

    const folders = await dbSession.query(
      (session) => session.prepare(query).bind(...params).all(),
      { queryName: 'db-query' }
    );

    return c.json({
      folders: folders.results.map((folder: any) => ({
        ...folder,
        metadata: folder.metadata ? JSON.parse(String(folder.metadata)) : null,
      }))
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch folders');
  }
});

// Create folder
app.post('/api/files/folders', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const { name, parent_id, description } = await c.req.json();

    if (!name?.trim()) {
      return c.json({ error: 'Folder name is required' }, 400);
    }

    const folderId = generateId('folder');
    
    // Build path
    let path = name;
    if (parent_id) {
      const parent = await dbSession.query(
      (session) => session.prepare(`
        SELECT path FROM file_folder WHERE id = ? AND deleted_at IS NULL
      `).bind(parent_id).first(),
      { queryName: 'db-query' }
    );
      
      if (parent) {
        path = `${parent.path}/${name}`;
      }
    }

    await c.env.DB.prepare(`
      INSERT INTO file_folder (
        id, name, parent_id, path, metadata, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `).bind(
      folderId,
      name.trim(),
      parent_id || null,
      path,
      JSON.stringify({ description: description || null })
    ).run();

    return c.json({ id: folderId, message: 'Folder created successfully' }, 201);

  } catch (error) {
    return handleError(c, error, 'Failed to create folder');
  }
});

// Update folder
app.put('/api/files/folders/:id', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const id = c.req.param('id');
    const { name, description } = await c.req.json();

    if (!name?.trim()) {
      return c.json({ error: 'Folder name is required' }, 400);
    }

    // Get current folder info
    const folder = await dbSession.query(
      (session) => session.prepare(`
      SELECT parent_id, path FROM file_folder WHERE id = ? AND deleted_at IS NULL
    `).bind(id).first(),
      { queryName: 'db-query' }
    );

    if (!folder) {
      return c.json({ error: 'Folder not found' }, 404);
    }

    // Build new path
    let newPath = name.trim();
    if (folder.parent_id) {
      const parent = await dbSession.query(
      (session) => session.prepare(`
        SELECT path FROM file_folder WHERE id = ? AND deleted_at IS NULL
      `).bind(folder.parent_id).first(),
      { queryName: 'db-query' }
    );
      
      if (parent) {
        newPath = `${parent.path}/${name.trim()}`;
      }
    }

    await c.env.DB.prepare(`
      UPDATE file_folder 
      SET name = ?, path = ?, metadata = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ? AND deleted_at IS NULL
    `).bind(
      name.trim(),
      newPath,
      JSON.stringify({ description: description || null }),
      id
    ).run();

    return c.json({ message: 'Folder updated successfully' });

  } catch (error) {
    return handleError(c, error, 'Failed to update folder');
  }
});

// Delete folder
app.delete('/api/files/folders/:id', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const id = c.req.param('id');

    // Check if folder has files or subfolders
    const [files, subfolders] = await Promise.all([
      dbSession.query(
        (session) => session.prepare(`SELECT COUNT(*) as count FROM file_upload WHERE folder_id = ?`).bind(id).first(),
        { queryName: 'db-query' }
      ),
      dbSession.query(
        (session) => session.prepare(`SELECT COUNT(*) as count FROM file_folder WHERE parent_id = ? AND deleted_at IS NULL`).bind(id).first(),
        { queryName: 'db-query' }
      )
    ]);

    if ((Number(files?.count) || 0) > 0 || (Number(subfolders?.count) || 0) > 0) {
      return c.json({ error: 'Cannot delete folder that contains files or subfolders' }, 400);
    }

    await dbSession.query(
      (session) => session.prepare(`
      UPDATE file_folder 
      SET deleted_at = CURRENT_TIMESTAMP 
      WHERE id = ?
    `).bind(id).run(),
      { usePrimary: true, queryName: 'db-write' }
    );

    return c.json({ message: 'Folder deleted successfully' });

  } catch (error) {
    return handleError(c, error, 'Failed to delete folder');
  }
});

// Get all files
app.get('/api/files', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '20');
    const search = c.req.query('search') || '';
    const folderId = c.req.query('folder_id');
    const mimeType = c.req.query('mime_type') || '';
    const offset = (page - 1) * limit;

    let query = `
      SELECT 
        fu.id, fu.name, fu.original_name, fu.mime_type, fu.size,
        fu.url, fu.thumbnail_url, fu.r2_key, fu.metadata,
        fu.created_at, fu.updated_at,
        ff.name as folder_name, ff.path as folder_path
      FROM file_upload fu
      LEFT JOIN file_folder ff ON fu.folder_id = ff.id
      WHERE fu.deleted_at IS NULL
    `;

    const params: any[] = [];

    if (search) {
      query += ` AND (fu.name LIKE ? OR fu.original_name LIKE ?)`;
      params.push(`%${search}%`, `%${search}%`);
    }

    if (folderId) {
      query += ` AND fu.folder_id = ?`;
      params.push(folderId);
    } else if (folderId === '') {
      query += ` AND fu.folder_id IS NULL`;
    }

    if (mimeType) {
      if (mimeType === 'image') {
        query += ` AND fu.mime_type LIKE 'image/%'`;
      } else if (mimeType === 'video') {
        query += ` AND fu.mime_type LIKE 'video/%'`;
      } else if (mimeType === 'document') {
        query += ` AND fu.mime_type IN ('application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain')`;
      } else {
        query += ` AND fu.mime_type = ?`;
        params.push(mimeType);
      }
    }

    query += ` ORDER BY fu.created_at DESC LIMIT ? OFFSET ?`;
    params.push(limit, offset);

    const files = await dbSession.query(
      (session) => session.prepare(query).bind(...params).all(),
      { queryName: 'db-query' }
    );

    // Get total count
    let countQuery = `SELECT COUNT(*) as total FROM file_upload fu WHERE fu.deleted_at IS NULL`;
    let countParams: any[] = [];

    if (search) {
      countQuery += ` AND (fu.name LIKE ? OR fu.original_name LIKE ?)`;
      countParams.push(`%${search}%`, `%${search}%`);
    }

    if (folderId) {
      countQuery += ` AND fu.folder_id = ?`;
      countParams.push(folderId);
    } else if (folderId === '') {
      countQuery += ` AND fu.folder_id IS NULL`;
    }

    if (mimeType) {
      if (mimeType === 'image') {
        countQuery += ` AND fu.mime_type LIKE 'image/%'`;
      } else if (mimeType === 'video') {
        countQuery += ` AND fu.mime_type LIKE 'video/%'`;
      } else if (mimeType === 'document') {
        countQuery += ` AND fu.mime_type IN ('application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain')`;
      } else {
        countQuery += ` AND fu.mime_type = ?`;
        countParams.push(mimeType);
      }
    }

    const totalResult = await dbSession.query(
      (session) => session.prepare(countQuery).bind(...countParams).first(),
      { queryName: 'db-query' }
    );

    return c.json({
      files: files.results.map((file: any) => ({
        ...file,
        metadata: file.metadata ? JSON.parse(String(file.metadata)) : null,
      })),
      pagination: {
        page,
        limit,
        total: totalResult?.total || 0,
        totalPages: Math.ceil(Number(totalResult?.total || 0) / limit)
      }
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch files');
  }
});

// Upload file (single file)
app.post('/api/files/upload', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const formData = await c.req.formData();
    const file = formData.get('file') as File;
    const folderId = formData.get('folder_id') as string | null;
    const name = formData.get('name') as string || file.name;

    if (!file) {
      return c.json({ error: 'No file provided' }, 400);
    }

    // Validate file size (10MB limit)
    if (file.size > 10 * 1024 * 1024) {
      return c.json({ error: 'File size must be less than 10MB' }, 400);
    }

    // Generate unique filename
    const fileExtension = file.name.split('.').pop();
    const uniqueName = `${Date.now()}_${Math.random().toString(36).substring(2)}.${fileExtension}`;
    const r2Key = `uploads/${uniqueName}`;

    // For this example, we'll store the file info but you would typically upload to R2
    // await c.env.R2_BUCKET.put(r2Key, file.stream());

    const fileId = generateId('file');
    const url = `/files/${r2Key}`;
    let thumbnailUrl = null;

    // Generate thumbnail URL for images
    if (file.type.startsWith('image/')) {
      thumbnailUrl = `/files/thumbnails/${r2Key}`;
    }

    await c.env.DB.prepare(`
      INSERT INTO file_upload (
        id, name, original_name, mime_type, size, folder_id,
        url, thumbnail_url, r2_key, metadata,
        created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `).bind(
      fileId,
      name,
      file.name,
      file.type,
      file.size,
      folderId || null,
      url,
      thumbnailUrl,
      r2Key,
      JSON.stringify({})
    ).run();

    return c.json({
      id: fileId,
      name,
      original_name: file.name,
      mime_type: file.type,
      size: file.size,
      url,
      thumbnail_url: thumbnailUrl,
      message: 'File uploaded successfully'
    }, 201);

  } catch (error) {
    return handleError(c, error, 'Failed to upload file');
  }
});

// Update file
app.put('/api/files/:id', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const id = c.req.param('id');
    const { name, folder_id } = await c.req.json();

    if (!name?.trim()) {
      return c.json({ error: 'File name is required' }, 400);
    }

    await dbSession.query(
      (session) => session.prepare(`
      UPDATE file_upload 
      SET name = ?, folder_id = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ? AND deleted_at IS NULL
    `).bind(name.trim(), folder_id || null, id).run(),
      { usePrimary: true, queryName: 'db-write' }
    );

    return c.json({ message: 'File updated successfully' });

  } catch (error) {
    return handleError(c, error, 'Failed to update file');
  }
});

// Delete file
app.delete('/api/files/:id', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const id = c.req.param('id');

    // Get file info
    const file = await dbSession.query(
      (session) => session.prepare(`
      SELECT r2_key FROM file_upload WHERE id = ? AND deleted_at IS NULL
    `).bind(id).first(),
      { queryName: 'db-query' }
    );

    if (!file) {
      return c.json({ error: 'File not found' }, 404);
    }

    // Delete from R2 (in a real implementation)
    // await c.env.R2_BUCKET.delete(file.r2_key);

    // Soft delete from database
    await dbSession.query(
      (session) => session.prepare(`
      UPDATE file_upload 
      SET deleted_at = CURRENT_TIMESTAMP 
      WHERE id = ?
    `).bind(id).run(),
      { usePrimary: true, queryName: 'db-write' }
    );

    return c.json({ message: 'File deleted successfully' });

  } catch (error) {
    return handleError(c, error, 'Failed to delete file');
  }
});

// Bulk delete files
app.post('/api/files/bulk-delete', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const { fileIds } = await c.req.json();

    if (!fileIds || !Array.isArray(fileIds) || fileIds.length === 0) {
      return c.json({ error: 'File IDs are required' }, 400);
    }

    // Get file info for R2 deletion
    const placeholders = fileIds.map(() => '?').join(',');
    const files = await dbSession.query(
      (session) => session.prepare(`
      SELECT r2_key FROM file_upload 
      WHERE id IN (${placeholders}) AND deleted_at IS NULL
    `).bind(...fileIds).all(),
      { queryName: 'db-query' }
    );

    // Delete from R2 (in a real implementation)
    // for (const file of files.results) {
    //   await c.env.R2_BUCKET.delete(file.r2_key);
    // }

    // Soft delete from database
    await dbSession.query(
      (session) => session.prepare(`
      UPDATE file_upload 
      SET deleted_at = CURRENT_TIMESTAMP 
      WHERE id IN (${placeholders})
    `).bind(...fileIds).run(),
      { usePrimary: true, queryName: 'db-write' }
    );

    return c.json({ message: `${fileIds.length} files deleted successfully` });

  } catch (error) {
    return handleError(c, error, 'Failed to bulk delete files');
  }
});

// Move files to folder
app.post('/api/files/move', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const { fileIds, folder_id } = await c.req.json();

    if (!fileIds || !Array.isArray(fileIds) || fileIds.length === 0) {
      return c.json({ error: 'File IDs are required' }, 400);
    }

    const placeholders = fileIds.map(() => '?').join(',');
    
    await dbSession.query(
      (session) => session.prepare(`
      UPDATE file_upload 
      SET folder_id = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id IN (${placeholders}) AND deleted_at IS NULL
    `).bind(folder_id || null, ...fileIds).run(),
      { usePrimary: true, queryName: 'db-write' }
    );

    return c.json({ message: `${fileIds.length} files moved successfully` });

  } catch (error) {
    return handleError(c, error, 'Failed to move files');
  }
});

// Get file analytics
app.get('/api/files/analytics', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const [totalFiles, totalSize, fileTypes, recentUploads] = await Promise.all([
      c.env.DB.prepare(`
        SELECT COUNT(*) as total FROM file_upload WHERE deleted_at IS NULL
      `).first(),
      
      c.env.DB.prepare(`
        SELECT COALESCE(SUM(size), 0) as total_size FROM file_upload WHERE deleted_at IS NULL
      `).first(),
      
      c.env.DB.prepare(`
        SELECT 
          CASE 
            WHEN mime_type LIKE 'image/%' THEN 'Images'
            WHEN mime_type LIKE 'video/%' THEN 'Videos'
            WHEN mime_type LIKE 'audio/%' THEN 'Audio'
            WHEN mime_type IN ('application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain') THEN 'Documents'
            ELSE 'Other'
          END as type,
          COUNT(*) as count,
          COALESCE(SUM(size), 0) as total_size
        FROM file_upload 
        WHERE deleted_at IS NULL
        GROUP BY type
        ORDER BY count DESC
      `).all(),
      
      c.env.DB.prepare(`
        SELECT 
          COUNT(*) as uploads,
          DATE(created_at) as date
        FROM file_upload 
        WHERE deleted_at IS NULL AND created_at >= date('now', '-30 days')
        GROUP BY DATE(created_at)
        ORDER BY date DESC
        LIMIT 30
      `).all()
    ]);

    return c.json({
      analytics: {
        total_files: totalFiles?.total || 0,
        total_size: totalSize?.total_size || 0,
        file_types: fileTypes?.results || [],
        recent_uploads: recentUploads?.results || []
      }
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch file analytics');
  }
});

// ===========================================
// ADDITIONAL API ENDPOINTS
// ===========================================

// Get sales channels
app.get('/api/sales-channels', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const channels = await dbSession.query(
      (session) => session.prepare(`
      SELECT * FROM sales_channel ORDER BY name
    `).all(),
      { queryName: 'db-query' }
    );
    
    return c.json({ data: channels.results || [] });
    
  } catch (error) {
    return handleError(c, error, 'Failed to fetch sales channels');
  }
});

// Create sales channel
app.post('/api/sales-channels', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const { name, description, is_disabled } = await c.req.json();
    
    if (!name) {
      return c.json({ error: 'Name is required' }, 400);
    }
    
    const channelId = generateId('sc');
    
    await dbSession.query(
      (session) => session.prepare(`
      INSERT INTO sales_channel (
        id, name, description, is_disabled, created_at, updated_at
      ) VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `).bind(
      channelId,
      name,
      description || null,
      is_disabled ? 1 : 0
    ).run(),
      { usePrimary: true, queryName: 'db-write' }
    );
    
    return c.json({ 
      id: channelId, 
      message: 'Sales channel created successfully' 
    });
    
  } catch (error) {
    return handleError(c, error, 'Failed to create sales channel');
  }
});

// Get stock locations
app.get('/api/stock-locations', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const locations = await dbSession.query(
      (session) => session.prepare(`
      SELECT sl.*, sla.address_1, sla.city, sla.country_code
      FROM stock_location sl
      LEFT JOIN stock_location_address sla ON sl.address_id = sla.id
      ORDER BY sl.name
    `).all(),
      { queryName: 'db-query' }
    );
    
    return c.json({ data: locations.results || [] });
    
  } catch (error) {
    return handleError(c, error, 'Failed to fetch stock locations');
  }
});

// Create stock location
app.post('/api/stock-locations', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const { name, address } = await c.req.json();
    
    if (!name) {
      return c.json({ error: 'Name is required' }, 400);
    }
    
    const locationId = generateId('sloc');
    let addressId = null;
    
    // Create address if provided
    if (address) {
      addressId = generateId('addr');
      await dbSession.query(
      (session) => session.prepare(`
        INSERT INTO stock_location_address (
          id, address_1, address_2, company, city, country_code, 
          phone, province, postal_code, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      `).bind(
        addressId,
        address.address_1 || null,
        address.address_2 || null,
        address.company || null,
        address.city || null,
        address.country_code || 'US',
        address.phone || null,
        address.province || null,
        address.postal_code || null
      ).run(),
      { usePrimary: true, queryName: 'db-write' }
    );
    }
    
    await c.env.DB.prepare(`
      INSERT INTO stock_location (
        id, name, address_id, metadata, created_at, updated_at
      ) VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `).bind(
      locationId,
      name,
      addressId,
      JSON.stringify({})
    ).run();
    
    return c.json({ 
      id: locationId, 
      message: 'Stock location created successfully' 
    });
    
  } catch (error) {
    return handleError(c, error, 'Failed to create stock location');
  }
});

// Get store settings
app.get('/api/settings/store', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const store = await dbSession.query(
      (session) => session.prepare(`
      SELECT * FROM store LIMIT 1
    `).first(),
      { queryName: 'db-query' }
    );
    
    return c.json({ data: store || {} });
    
  } catch (error) {
    return handleError(c, error, 'Failed to fetch store settings');
  }
});

// Update store settings
app.put('/api/settings/store', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const storeData = await c.req.json();
    
    // Check if store exists
    const existingStore = await dbSession.query(
      (session) => session.prepare(`
      SELECT id FROM store LIMIT 1
    `).first(),
      { queryName: 'db-query' }
    );
    
    if (existingStore) {
      // Update existing store
      await c.env.DB.prepare(`
        UPDATE store SET 
          name = ?, 
          default_sales_channel_id = ?, 
          default_region_id = ?, 
          default_location_id = ?, 
          metadata = ?, 
          updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `).bind(
        storeData.name || null,
        storeData.default_sales_channel_id || null,
        storeData.default_region_id || null,
        storeData.default_location_id || null,
        JSON.stringify(storeData.metadata || {}),
        existingStore.id
      ).run();
    } else {
      // Create new store
      const storeId = generateId('store');
      await c.env.DB.prepare(`
        INSERT INTO store (
          id, name, default_sales_channel_id, default_region_id, 
          default_location_id, metadata, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      `).bind(
        storeId,
        storeData.name || 'My Store',
        storeData.default_sales_channel_id || null,
        storeData.default_region_id || null,
        storeData.default_location_id || null,
        JSON.stringify(storeData.metadata || {})
      ).run();
    }
    
    return c.json({ message: 'Store settings updated successfully' });
    
  } catch (error) {
    return handleError(c, error, 'Failed to update store settings');
  }
});

// Get tax rates
app.get('/api/tax-rates', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const taxRates = await dbSession.query(
      (session) => session.prepare(`
      SELECT tr.*, treg.country_code, treg.province_code
      FROM tax_rate tr
      LEFT JOIN tax_region treg ON tr.tax_region_id = treg.id
      ORDER BY tr.name
    `).all(),
      { queryName: 'db-query' }
    );
    
    const taxRegions = await dbSession.query(
      (session) => session.prepare(`
      SELECT * FROM tax_region ORDER BY country_code, province_code
    `).all(),
      { queryName: 'db-query' }
    );
    
    return c.json({ 
      data: {
        taxRates: taxRates.results || [],
        taxRegions: taxRegions.results || []
      }
    });
    
  } catch (error) {
    return handleError(c, error, 'Failed to fetch tax rates');
  }
});

// Create tax rate
app.post('/api/tax-rates', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const { name, code, rate, is_default, is_combinable, tax_region_id, metadata } = await c.req.json();
    
    if (!name || !code || rate === undefined) {
      return c.json({ error: 'Name, code, and rate are required' }, 400);
    }
    
    const taxRateId = generateId('tr');
    
    await c.env.DB.prepare(`
      INSERT INTO tax_rate (
        id, rate, code, name, is_default, is_combinable, 
        tax_region_id, metadata, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `).bind(
      taxRateId,
      rate,
      code,
      name,
      is_default ? 1 : 0,
      is_combinable ? 1 : 0,
      tax_region_id || null,
      JSON.stringify(metadata || {})
    ).run();
    
    return c.json({ 
      id: taxRateId, 
      message: 'Tax rate created successfully' 
    });
    
  } catch (error) {
    return handleError(c, error, 'Failed to create tax rate');
  }
});

// Update tax rate
app.put('/api/tax-rates/:id', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const id = c.req.param('id');
    const { name, code, rate, is_default, is_combinable, metadata } = await c.req.json();
    
    await c.env.DB.prepare(`
      UPDATE tax_rate SET 
        rate = ?, code = ?, name = ?, is_default = ?, 
        is_combinable = ?, metadata = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `).bind(
      rate,
      code,
      name,
      is_default ? 1 : 0,
      is_combinable ? 1 : 0,
      JSON.stringify(metadata || {}),
      id
    ).run();
    
    return c.json({ message: 'Tax rate updated successfully' });
    
  } catch (error) {
    return handleError(c, error, 'Failed to update tax rate');
  }
});

// Delete tax rate
app.delete('/api/tax-rates/:id', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const id = c.req.param('id');
    
    await dbSession.query(
      (session) => session.prepare(`
      UPDATE tax_rate SET 
        deleted_at = CURRENT_TIMESTAMP 
      WHERE id = ?
    `).bind(id).run(),
      { usePrimary: true, queryName: 'db-write' }
    );
    
    return c.json({ message: 'Tax rate deleted successfully' });
    
  } catch (error) {
    return handleError(c, error, 'Failed to delete tax rate');
  }
});

// ===========================================
// USER MANAGEMENT ROUTES
// ===========================================

// Get all users
app.get('/api/settings/users', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '20');
    const search = c.req.query('search') || '';
    const role = c.req.query('role') || '';
    const status = c.req.query('status') || '';
    const offset = (page - 1) * limit;

    let query = `
      SELECT 
        id, email, first_name, last_name, role, is_active,
        last_login_at, created_at, updated_at
      FROM users
      WHERE deleted_at IS NULL
    `;

    const params: any[] = [];
    
    if (search) {
      query += ` AND (email LIKE ? OR first_name LIKE ? OR last_name LIKE ?)`;
      params.push(`%${search}%`, `%${search}%`, `%${search}%`);
    }
    
    if (role) {
      query += ` AND role = ?`;
      params.push(role);
    }
    
    if (status === 'active') {
      query += ` AND is_active = 1`;
    } else if (status === 'inactive') {
      query += ` AND is_active = 0`;
    }

    query += ` ORDER BY created_at DESC LIMIT ? OFFSET ?`;
    params.push(limit, offset);

    const users = await dbSession.query(
      (session) => session.prepare(query).bind(...params).all(),
      { queryName: 'db-query' }
    );

    // Get total count
    let countQuery = `SELECT COUNT(*) as total FROM users WHERE deleted_at IS NULL`;
    let countParams: any[] = [];
    
    if (search || role || status) {
      if (search) {
        countQuery += ` AND (email LIKE ? OR first_name LIKE ? OR last_name LIKE ?)`;
        countParams.push(`%${search}%`, `%${search}%`, `%${search}%`);
      }
      
      if (role) {
        countQuery += ` AND role = ?`;
        countParams.push(role);
      }
      
      if (status === 'active') {
        countQuery += ` AND is_active = 1`;
      } else if (status === 'inactive') {
        countQuery += ` AND is_active = 0`;
      }
    }

    const totalResult = await dbSession.query(
      (session) => session.prepare(countQuery).bind(...countParams).first(),
      { queryName: 'db-query' }
    );
      
      return c.json({ 
      data: users.results,
      pagination: {
        page,
        limit,
        total: totalResult?.total || 0,
        totalPages: Math.ceil((Number(totalResult?.total) || 0) / limit)
      }
    });
    
  } catch (error) {
    return handleError(c, error, 'Failed to fetch users');
  }
});

// Get single user
app.get('/api/settings/users/:id', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const id = c.req.param('id');

    const user = await dbSession.query(
      (session) => session.prepare(`
      SELECT 
        id, email, first_name, last_name, role, is_active,
        last_login_at, created_at, updated_at
      FROM users
      WHERE id = ? AND deleted_at IS NULL
    `).bind(id).first(),
      { queryName: 'db-query' }
    );

    if (!user) {
      return c.json({ error: 'User not found' }, 404);
    }

    return c.json({ user });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch user');
  }
});

// Create user
app.post('/api/settings/users', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const { email, first_name, last_name, role, is_active, password } = await c.req.json();

    if (!email || !first_name || !last_name) {
      return c.json({ error: 'Email, first name, and last name are required' }, 400);
    }

    // Check if email already exists
    const existingUser = await dbSession.query(
      (session) => session.prepare(`
      SELECT id FROM users WHERE email = ? AND deleted_at IS NULL
    `).bind(email).first(),
      { queryName: 'db-query' }
    );

    if (existingUser) {
      return c.json({ error: 'Email already exists' }, 400);
    }

    const userId = generateId('user');

    // Note: In production, you should properly hash passwords
    const passwordHash = password || 'admin123'; // Default password

    await dbSession.query(
      (session) => session.prepare(`
      INSERT INTO users (
        id, email, first_name, last_name, role, is_active, password_hash,
        created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `).bind(
      userId,
      email,
      first_name,
      last_name,
      role || 'admin',
      is_active !== false ? 1 : 0,
      passwordHash
    ).run(),
      { usePrimary: true, queryName: 'db-write' }
    );

    return c.json({ id: userId, message: 'User created successfully' }, 201);

  } catch (error) {
    return handleError(c, error, 'Failed to create user');
  }
});

// Update user
app.put('/api/settings/users/:id', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const id = c.req.param('id');
    const { email, first_name, last_name, role, is_active, password } = await c.req.json();

    if (!email || !first_name || !last_name) {
      return c.json({ error: 'Email, first name, and last name are required' }, 400);
    }

    // Check if email already exists for other users
    const existingUser = await dbSession.query(
      (session) => session.prepare(`
      SELECT id FROM users WHERE email = ? AND id != ? AND deleted_at IS NULL
    `).bind(email, id).first(),
      { queryName: 'db-query' }
    );

    if (existingUser) {
      return c.json({ error: 'Email already exists' }, 400);
    }

    let updateQuery = `
      UPDATE users SET
        email = ?, first_name = ?, last_name = ?, role = ?, is_active = ?,
        updated_at = CURRENT_TIMESTAMP 
      WHERE id = ? AND deleted_at IS NULL
    `;
    
    let params = [
      email,
      first_name,
      last_name,
      role || 'admin',
      is_active !== false ? 1 : 0,
      id
    ];

    // Update password if provided
    if (password && password.trim()) {
      updateQuery = `
        UPDATE users SET
          email = ?, first_name = ?, last_name = ?, role = ?, is_active = ?,
          password_hash = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ? AND deleted_at IS NULL
      `;
      params = [
        email,
        first_name,
        last_name,
        role || 'admin',
        is_active !== false ? 1 : 0,
        password.trim(),
        id
      ];
    }

    await dbSession.query(
      (session) => session.prepare(updateQuery).bind(...params).run(),
      { usePrimary: true, queryName: 'db-write' }
    );

    return c.json({ message: 'User updated successfully' });

  } catch (error) {
    return handleError(c, error, 'Failed to update user');
  }
});

// Delete user
app.delete('/api/settings/users/:id', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const id = c.req.param('id');

    // Get current user ID from JWT to prevent self-deletion
    const payload = c.get('jwtPayload');
    if (payload && payload.id === id) {
      return c.json({ error: 'Cannot delete your own account' }, 400);
    }

    await dbSession.query(
      (session) => session.prepare(`
      UPDATE users 
      SET deleted_at = CURRENT_TIMESTAMP 
      WHERE id = ?
    `).bind(id).run(),
      { usePrimary: true, queryName: 'db-write' }
    );

    return c.json({ message: 'User deleted successfully' });

  } catch (error) {
    return handleError(c, error, 'Failed to delete user');
  }
});

// Bulk delete users
app.post('/api/settings/users/bulk-delete', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const { userIds } = await c.req.json();

    if (!userIds || !Array.isArray(userIds) || userIds.length === 0) {
      return c.json({ error: 'User IDs are required' }, 400);
    }

    // Get current user ID from JWT to prevent self-deletion
    const payload = c.get('jwtPayload');
    if (payload && userIds.includes(payload.id)) {
      return c.json({ error: 'Cannot delete your own account' }, 400);
    }

    const placeholders = userIds.map(() => '?').join(',');
    
    await dbSession.query(
      (session) => session.prepare(`
      UPDATE users 
      SET deleted_at = CURRENT_TIMESTAMP 
      WHERE id IN (${placeholders})
    `).bind(...userIds).run(),
      { usePrimary: true, queryName: 'db-write' }
    );

    return c.json({ message: `${userIds.length} users deleted successfully` });

  } catch (error) {
    return handleError(c, error, 'Failed to bulk delete users');
  }
});

// Update user status (activate/deactivate)
app.put('/api/settings/users/:id/status', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const id = c.req.param('id');
    const { is_active } = await c.req.json();

    // Get current user ID from JWT to prevent self-deactivation
    const payload = c.get('jwtPayload');
    if (payload && payload.id === id && !is_active) {
      return c.json({ error: 'Cannot deactivate your own account' }, 400);
    }

    await dbSession.query(
      (session) => session.prepare(`
      UPDATE users 
      SET is_active = ?, updated_at = CURRENT_TIMESTAMP 
      WHERE id = ? AND deleted_at IS NULL
    `).bind(is_active ? 1 : 0, id).run(),
      { usePrimary: true, queryName: 'db-write' }
    );

    return c.json({ message: `User ${is_active ? 'activated' : 'deactivated'} successfully` });

  } catch (error) {
    return handleError(c, error, 'Failed to update user status');
  }
});

// Reset user password
app.post('/api/settings/users/:id/reset-password', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const id = c.req.param('id');
    const { password } = await c.req.json();

    if (!password || password.trim().length < 6) {
      return c.json({ error: 'Password must be at least 6 characters long' }, 400);
    }

    // Note: In production, you should properly hash passwords
    await dbSession.query(
      (session) => session.prepare(`
      UPDATE users 
      SET password_hash = ?, updated_at = CURRENT_TIMESTAMP 
      WHERE id = ? AND deleted_at IS NULL
    `).bind(password.trim(), id).run(),
      { usePrimary: true, queryName: 'db-write' }
    );

    return c.json({ message: 'Password reset successfully' });

  } catch (error) {
    return handleError(c, error, 'Failed to reset password');
  }
});

// Get user roles (for dropdown/select options)
app.get('/api/settings/user-roles', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const roles = [
      { value: 'admin', label: 'Administrator', description: 'Full access to all features' },
      { value: 'manager', label: 'Manager', description: 'Can manage products, orders, and customers' },
      { value: 'editor', label: 'Editor', description: 'Can edit content and view reports' },
      { value: 'viewer', label: 'Viewer', description: 'Read-only access to most features' }
    ];

    return c.json({ roles });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch user roles');
  }
});

// ===========================================
// API KEYS MANAGEMENT ROUTES
// ===========================================

// Get all API keys
app.get('/api/settings/api-keys', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '20');
    const search = c.req.query('search') || '';
    const status = c.req.query('status') || '';
    const offset = (page - 1) * limit;

    let query = `
      SELECT 
        id, title, type, redacted_token, prefix, last_used_at, 
        expires_at, created_at, updated_at
      FROM api_key
      WHERE deleted_at IS NULL
    `;

    const params: any[] = [];
    
    if (search) {
      query += ` AND (title LIKE ? OR prefix LIKE ?)`;
      params.push(`%${search}%`, `%${search}%`);
    }
    
    if (status === 'active') {
      query += ` AND (expires_at IS NULL OR expires_at > datetime('now'))`;
    } else if (status === 'expired') {
      query += ` AND expires_at IS NOT NULL AND expires_at <= datetime('now')`;
    }

    query += ` ORDER BY created_at DESC LIMIT ? OFFSET ?`;
    params.push(limit, offset);

    const apiKeys = await dbSession.query(
      (session) => session.prepare(query).bind(...params).all(),
      { queryName: 'db-query' }
    );

    // Get total count
    let countQuery = `SELECT COUNT(*) as total FROM api_key WHERE deleted_at IS NULL`;
    let countParams: any[] = [];
    
    if (search || status) {
      if (search) {
        countQuery += ` AND (title LIKE ? OR prefix LIKE ?)`;
        countParams.push(`%${search}%`, `%${search}%`);
      }
      
      if (status === 'active') {
        countQuery += ` AND (expires_at IS NULL OR expires_at > datetime('now'))`;
      } else if (status === 'expired') {
        countQuery += ` AND expires_at IS NOT NULL AND expires_at <= datetime('now')`;
      }
    }

    const totalResult = await dbSession.query(
      (session) => session.prepare(countQuery).bind(...countParams).first(),
      { queryName: 'db-query' }
    );

    // Add computed status to each API key
    const apiKeysWithStatus = apiKeys.results.map((key: any) => ({
      ...key,
      status: (!key.expires_at || new Date(String(key.expires_at)) > new Date()) ? 'active' : 'expired',
      is_expired: key.expires_at && new Date(String(key.expires_at)) <= new Date()
    }));

    return c.json({
      data: apiKeysWithStatus,
      pagination: {
      page,
      limit,
        total: totalResult?.total || 0,
        totalPages: Math.ceil((Number(totalResult?.total) || 0) / limit)
      }
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch API keys');
  }
});

// Get single API key
app.get('/api/settings/api-keys/:id', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const id = c.req.param('id');

    const apiKey = await dbSession.query(
      (session) => session.prepare(`
      SELECT 
        id, title, type, redacted_token, prefix, last_used_at,
        expires_at, created_at, updated_at
      FROM api_key
      WHERE id = ? AND deleted_at IS NULL
    `).bind(id).first(),
      { queryName: 'db-query' }
    );

    if (!apiKey) {
      return c.json({ error: 'API key not found' }, 404);
    }

    return c.json({ 
      data: {
        ...apiKey,
        status: (!apiKey.expires_at || new Date(String(apiKey.expires_at)) > new Date()) ? 'active' : 'expired',
        is_expired: apiKey.expires_at && new Date(String(apiKey.expires_at)) <= new Date()
      }
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch API key');
  }
});

// Create API key
app.post('/api/settings/api-keys', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const { title, type, expires_at } = await c.req.json();

    if (!title || !type) {
      return c.json({ error: 'Title and type are required' }, 400);
    }

    const apiKeyId = generateId('ak');
    
    // Generate a secure token
    const token = generateId('sk') + '_' + Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
    const prefix = token.substring(0, 12);
    const redactedToken = prefix + '...' + token.substring(token.length - 4);

      await dbSession.query(
      (session) => session.prepare(`
      INSERT INTO api_key (
        id, title, type, token, redacted_token, prefix, expires_at,
          created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      `).bind(
      apiKeyId,
      title,
      type,
      token,
      redactedToken,
      prefix,
      expires_at || null
      ).run(),
      { usePrimary: true, queryName: 'db-write' }
    );

    return c.json({ 
      id: apiKeyId, 
      token, // Return full token only on creation
      redacted_token: redactedToken,
      message: 'API key created successfully. Please save the token as it will not be shown again.' 
    }, 201);

  } catch (error) {
    return handleError(c, error, 'Failed to create API key');
  }
});

// Update API key
app.put('/api/settings/api-keys/:id', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const id = c.req.param('id');
    const { title, expires_at } = await c.req.json();

    if (!title) {
      return c.json({ error: 'Title is required' }, 400);
    }

    await dbSession.query(
      (session) => session.prepare(`
      UPDATE api_key SET
        title = ?, expires_at = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ? AND deleted_at IS NULL
    `).bind(title, expires_at || null, id).run(),
      { usePrimary: true, queryName: 'db-write' }
    );

    return c.json({ message: 'API key updated successfully' });

  } catch (error) {
    return handleError(c, error, 'Failed to update API key');
  }
});

// Delete API key
app.delete('/api/settings/api-keys/:id', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const id = c.req.param('id');
    
    await dbSession.query(
      (session) => session.prepare(`
      UPDATE api_key 
      SET deleted_at = CURRENT_TIMESTAMP 
      WHERE id = ?
    `).bind(id).run(),
      { usePrimary: true, queryName: 'db-write' }
    );

    return c.json({ message: 'API key deleted successfully' });

  } catch (error) {
    return handleError(c, error, 'Failed to delete API key');
  }
});

// Bulk delete API keys
app.post('/api/settings/api-keys/bulk-delete', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const { apiKeyIds } = await c.req.json();

    if (!apiKeyIds || !Array.isArray(apiKeyIds) || apiKeyIds.length === 0) {
      return c.json({ error: 'API key IDs are required' }, 400);
    }

    const placeholders = apiKeyIds.map(() => '?').join(',');
    
    await dbSession.query(
      (session) => session.prepare(`
      UPDATE api_key 
      SET deleted_at = CURRENT_TIMESTAMP 
      WHERE id IN (${placeholders})
    `).bind(...apiKeyIds).run(),
      { usePrimary: true, queryName: 'db-write' }
    );

    return c.json({ message: `${apiKeyIds.length} API keys deleted successfully` });

  } catch (error) {
    return handleError(c, error, 'Failed to bulk delete API keys');
  }
});

// Regenerate API key token
app.post('/api/settings/api-keys/:id/regenerate', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const id = c.req.param('id');

    // Generate a new secure token
    const token = generateId('sk') + '_' + Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
    const prefix = token.substring(0, 12);
    const redactedToken = prefix + '...' + token.substring(token.length - 4);

    await dbSession.query(
      (session) => session.prepare(`
      UPDATE api_key 
      SET token = ?, redacted_token = ?, prefix = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ? AND deleted_at IS NULL
    `).bind(token, redactedToken, prefix, id).run(),
      { usePrimary: true, queryName: 'db-write' }
    );
    
    return c.json({ 
      token, // Return full token only on regeneration
      redacted_token: redactedToken,
      message: 'API key regenerated successfully. Please save the new token as it will not be shown again.' 
    });

  } catch (error) {
    return handleError(c, error, 'Failed to regenerate API key');
  }
});

// Get API key usage statistics
app.get('/api/settings/api-keys/stats', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const [totalKeys, activeKeys, expiredKeys, recentUsage] = await Promise.all([
      c.env.DB.prepare(`
        SELECT COUNT(*) as total FROM api_key WHERE deleted_at IS NULL
      `).first(),
      
      c.env.DB.prepare(`
        SELECT COUNT(*) as active FROM api_key 
        WHERE deleted_at IS NULL AND (expires_at IS NULL OR expires_at > datetime('now'))
      `).first(),
      
      c.env.DB.prepare(`
        SELECT COUNT(*) as expired FROM api_key 
        WHERE deleted_at IS NULL AND expires_at IS NOT NULL AND expires_at <= datetime('now')
      `).first(),
      
      c.env.DB.prepare(`
        SELECT 
          COUNT(*) as usage_count,
          DATE(last_used_at) as date
        FROM api_key 
        WHERE deleted_at IS NULL AND last_used_at >= date('now', '-30 days')
        GROUP BY DATE(last_used_at)
        ORDER BY date DESC
        LIMIT 30
      `).all()
    ]);

    return c.json({
      stats: {
        total_keys: totalKeys?.total || 0,
        active_keys: activeKeys?.active || 0,
        expired_keys: expiredKeys?.expired || 0,
        recent_usage: recentUsage?.results || []
      }
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch API key statistics');
  }
});

// ===========================================
// PRODUCT IMAGES ROUTES
// ===========================================

// Get product images
app.get('/api/products/:id/images', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const id = c.req.param('id');

    const images = await dbSession.query(
      (session) => session.prepare(`
      SELECT pi.id, pi.url, pi.alt_text, pi.sort_order, pi.metadata, pi.created_at
      FROM product_images pi
      WHERE pi.product_id = ?
      ORDER BY pi.sort_order, pi.created_at
    `).bind(id).all(),
      { queryName: 'db-query' }
    );

    return c.json({
      success: true,
      data: images.results || []
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch product images');
  }
});

// Create product image
app.post('/api/products/:id/images', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const productId = c.req.param('id');
    const data = await c.req.json();
    const imageId = generateId('img');

    await c.env.DB.prepare(`
      INSERT INTO product_images (id, product_id, url, alt_text, sort_order, metadata)
      VALUES (?, ?, ?, ?, ?, ?)
    `).bind(
      imageId,
      productId,
      data.url,
      data.alt_text || null,
      data.sort_order || data.rank || 0,
      JSON.stringify(data.metadata || {})
    ).run();

    return c.json({
      success: true,
      data: { id: imageId },
      message: 'Image created successfully'
    }, 201);

  } catch (error) {
    return handleError(c, error, 'Failed to create image');
  }
});

// Update product image
app.put('/api/products/:productId/images/:imageId', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const productId = c.req.param('productId');
    const imageId = c.req.param('imageId');
    const data = await c.req.json();

    // Check if image exists
    const image = await dbSession.query(
      (session) => session.prepare(`
      SELECT id FROM product_images
      WHERE id = ? AND product_id = ?
    `).bind(imageId, productId).first(),
      { queryName: 'db-query' }
    );

    if (!image) {
      return c.json({ error: 'Image not found' }, 404);
    }

    // Update image - removed updated_at column since it doesn't exist in the table
    await c.env.DB.prepare(`
      UPDATE product_images SET
        url = ?, alt_text = ?, sort_order = ?, metadata = ?
      WHERE id = ? AND product_id = ?
    `).bind(
      data.url,
      data.alt_text || null,
      data.sort_order || data.rank || 0,
      JSON.stringify(data.metadata || {}),
      imageId,
      productId
    ).run();

    return c.json({
      success: true,
      data: { id: imageId },
      message: 'Image updated successfully'
    });

  } catch (error) {
    return handleError(c, error, 'Failed to update image');
  }
});

// Create product option
app.post('/api/products/:id/options', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const productId = c.req.param('id');
    const data = await c.req.json();
    const optionId = generateId('opt');

    await dbSession.query(
      (session) => session.prepare(`
      INSERT INTO product_options (id, product_id, name, type, sort_order)
      VALUES (?, ?, ?, ?, ?)
    `).bind(
      optionId,
      productId,
      data.name || data.title,
      data.type || 'text',
      data.sort_order || 0
    ).run(),
      { usePrimary: true, queryName: 'db-write' }
    );

    return c.json({
      success: true,
      data: { id: optionId },
      message: 'Option created successfully'
    }, 201);

  } catch (error) {
    return handleError(c, error, 'Failed to create option');
  }
});

// Update product option
app.put('/api/products/:productId/options/:optionId', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const productId = c.req.param('productId');
    const optionId = c.req.param('optionId');
    const data = await c.req.json();

    // Check if option exists
    const option = await dbSession.query(
      (session) => session.prepare(`
      SELECT id FROM product_options
      WHERE id = ? AND product_id = ?
    `).bind(optionId, productId).first(),
      { queryName: 'db-query' }
    );

    if (!option) {
      return c.json({ error: 'Option not found' }, 404);
    }
    
    // Try to update with updated_at column first, fall back to basic update if column doesn't exist
    try {
      await dbSession.query(
      (session) => session.prepare(`
        UPDATE product_options SET
          name = ?, type = ?, sort_order = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ? AND product_id = ?
      `).bind(
        data.name || data.title,
        data.type || 'text',
        data.sort_order || 0,
        optionId,
        productId
      ).run(),
      { usePrimary: true, queryName: 'db-write' }
    );
    } catch (updateError) {
      // If the above fails (likely due to missing updated_at column), try without it
      console.warn('updated_at column not found, updating without it:', updateError);
      await dbSession.query(
      (session) => session.prepare(`
        UPDATE product_options SET
          name = ?, type = ?, sort_order = ?
        WHERE id = ? AND product_id = ?
      `).bind(
        data.name || data.title,
        data.type || 'text',
        data.sort_order || 0,
        optionId,
        productId
      ).run(),
      { usePrimary: true, queryName: 'db-write' }
    );
    }

    return c.json({
      success: true,
      data: { id: optionId },
      message: 'Option updated successfully'
    });

  } catch (error) {
    return handleError(c, error, 'Failed to update option');
  }
});

// ===========================================
// PRODUCT OPTION VALUES ROUTES
// ===========================================

// Get option values for a specific option
app.get('/api/products/:productId/options/:optionId/values', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const optionId = c.req.param('optionId');

    const values = await dbSession.query(
      (session) => session.prepare(`
      SELECT pov.id, pov.value, pov.hex_color, pov.image_url, pov.sort_order, pov.created_at
      FROM product_option_values pov
      WHERE pov.option_id = ?
      ORDER BY pov.sort_order, pov.created_at
    `).bind(optionId).all(),
      { queryName: 'db-query' }
    );

    return c.json({
      success: true,
      data: values.results || []
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch option values');
  }
});

// Create option value
app.post('/api/products/:productId/options/:optionId/values', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const optionId = c.req.param('optionId');
    const data = await c.req.json();
    const valueId = generateId('optval');

    await dbSession.query(
      (session) => session.prepare(`
      INSERT INTO product_option_values (id, option_id, value, hex_color, image_url, sort_order)
      VALUES (?, ?, ?, ?, ?, ?)
    `).bind(
      valueId,
      optionId,
      data.value,
      data.hex_color || null,
      data.image_url || null,
      data.sort_order || 0
    ).run(),
      { usePrimary: true, queryName: 'db-write' }
    );

    return c.json({
      success: true,
      data: { id: valueId },
      message: 'Option value created successfully'
    }, 201);

  } catch (error) {
    return handleError(c, error, 'Failed to create option value');
  }
});

// Update option value
app.put('/api/products/:productId/options/:optionId/values/:valueId', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const valueId = c.req.param('valueId');
    const data = await c.req.json();

    // Check if option value exists
    const value = await dbSession.query(
      (session) => session.prepare(`
      SELECT id FROM product_option_values
      WHERE id = ?
    `).bind(valueId).first(),
      { queryName: 'db-query' }
    );

    if (!value) {
      return c.json({ error: 'Option value not found' }, 404);
    }

    // Try to update with updated_at column first, fall back to basic update if column doesn't exist
    try {
      await dbSession.query(
      (session) => session.prepare(`
        UPDATE product_option_values SET
          value = ?, hex_color = ?, image_url = ?, sort_order = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `).bind(
        data.value,
        data.hex_color || null,
        data.image_url || null,
        data.sort_order || 0,
        valueId
      ).run(),
      { usePrimary: true, queryName: 'db-write' }
    );
    } catch (updateError) {
      // If the above fails (likely due to missing updated_at column), try without it
      console.warn('updated_at column not found in product_option_values, updating without it:', updateError);
      await dbSession.query(
      (session) => session.prepare(`
        UPDATE product_option_values SET
          value = ?, hex_color = ?, image_url = ?, sort_order = ?
        WHERE id = ?
      `).bind(
        data.value,
        data.hex_color || null,
        data.image_url || null,
        data.sort_order || 0,
        valueId
      ).run(),
      { usePrimary: true, queryName: 'db-write' }
    );
    }

    return c.json({
      success: true,
      data: { id: valueId },
      message: 'Option value updated successfully'
    });

  } catch (error) {
    return handleError(c, error, 'Failed to update option value');
  }
});

// ===========================================
// INVOICE MANAGEMENT ROUTES
// ===========================================

// Manual invoice cleanup endpoint
app.delete('/api/invoices/cleanup', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    let deletedCount = 0;
    const currentTime = Date.now();
    
    // List all objects in the UPLOADS bucket
    const list = await c.env.UPLOADS.list();
    
    for (const object of list.objects) {
      // Check if this is an invoice file (based on naming pattern)
      if (object.key.match(/^[A-Z]+\d+_\d+\.pdf$/)) {
        // Get object metadata to check expiration
        const objectDetails = await c.env.UPLOADS.get(object.key);
        
        if (objectDetails?.customMetadata?.expiresAt) {
          const expiresAt = parseInt(objectDetails.customMetadata.expiresAt);
          
          if (currentTime > expiresAt) {
            // File has expired, delete it
            await c.env.UPLOADS.delete(object.key);
            deletedCount++;
            console.log(`Manually deleted expired invoice: ${object.key}`);
          }
        } else {
          // If no expiration metadata, check file age (fallback)
          // Extract timestamp from filename (format: SERIE123_timestamp.pdf)
          const timestampMatch = object.key.match(/_(\d+)\.pdf$/);
          if (timestampMatch) {
            const fileTimestamp = parseInt(timestampMatch[1]);
            const oneHour = 60 * 60 * 1000;
            
            if (currentTime - fileTimestamp > oneHour) {
              // File is older than 1 hour, delete it
              await c.env.UPLOADS.delete(object.key);
              deletedCount++;
              console.log(`Manually deleted old invoice (no metadata): ${object.key}`);
            }
          }
        }
      }
    }
    
    return c.json({
      success: true,
      message: `Manual cleanup completed. Deleted ${deletedCount} expired invoice files.`,
      deletedCount: deletedCount
    });
  } catch (error) {
    return handleError(c, error, 'Failed to cleanup invoice files');
  }
});

// List active invoice files
app.get('/api/invoices/active', async (c) => {
  try {
    // Initialize session service
    const dbSession = getDbSession(c);
    const currentTime = Date.now();
    const activeInvoices: any[] = [];
    
    // List all objects in the UPLOADS bucket
    const list = await c.env.UPLOADS.list();
    
    for (const object of list.objects) {
      // Check if this is an invoice file (based on naming pattern)
      if (object.key.match(/^[A-Z]+\d+_\d+\.pdf$/)) {
        // Get object metadata to check expiration
        const objectDetails = await c.env.UPLOADS.get(object.key);
        
        let isExpired = false;
        let expiresAt = null;
        
        if (objectDetails?.customMetadata?.expiresAt) {
          expiresAt = parseInt(objectDetails.customMetadata.expiresAt);
          isExpired = currentTime > expiresAt;
        } else {
          // If no expiration metadata, check file age (fallback)
          const timestampMatch = object.key.match(/_(\d+)\.pdf$/);
          if (timestampMatch) {
            const fileTimestamp = parseInt(timestampMatch[1]);
            const oneHour = 60 * 60 * 1000;
            expiresAt = fileTimestamp + oneHour;
            isExpired = currentTime - fileTimestamp > oneHour;
          }
        }
        
        if (!isExpired) {
          activeInvoices.push({
            filename: object.key,
            size: object.size,
            uploaded: object.uploaded,
            cif: objectDetails?.customMetadata?.cif,
            serie: objectDetails?.customMetadata?.serie,
            numar: objectDetails?.customMetadata?.numar,
            expiresAt: expiresAt ? new Date(expiresAt).toISOString() : null,
            timeUntilExpiry: expiresAt ? Math.max(0, expiresAt - currentTime) : null
          });
        }
      }
    }
    
    return c.json({
      success: true,
      data: activeInvoices,
      count: activeInvoices.length
    });
  } catch (error) {
    return handleError(c, error, 'Failed to list active invoice files');
  }
});

// Register shipping routes
app.route('/api/shipping', adminShippingRoutes);

// Register file upload routes
app.route('/api/files', fileUploadRoutes);

// Register cache management routes
app.route('/api/cache', cacheManagementRoutes);

export default app;
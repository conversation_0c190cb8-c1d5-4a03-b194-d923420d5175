<template>
    <div class="bg-wheat-50 min-h-screen w-full">
      <div class="w-full max-w-7xl mx-auto px-2 sm:px-4 py-4 sm:py-8">
        <!-- Breadcrumb -->
        <nav class="flex mb-4 sm:mb-8 overflow-x-auto" aria-label="Breadcrumb">
          <ol class="inline-flex items-center space-x-1 md:space-x-3">
            <li class="inline-flex items-center">
              <NuxtLink to="/" class="inline-flex items-center text-sm font-medium text-forest-600 hover:text-terracotta-500">
                <Icon name="heroicons:home" class="w-4 h-4 mr-2" />
                {{ $t('nav.home') }}
              </NuxtLink>
            </li>
            <li>
              <div class="flex items-center">
                <Icon name="heroicons:chevron-right" class="w-4 h-4 text-gray-400" />
                <NuxtLink to="/cart" class="ml-1 text-sm font-medium text-forest-600 hover:text-terracotta-500 md:ml-2">{{ $t('cart.title') }}</NuxtLink>
              </div>
            </li>
            <li aria-current="page">
              <div class="flex items-center">
                <Icon name="heroicons:chevron-right" class="w-4 h-4 text-gray-400" />
                <span class="ml-1 text-sm font-medium text-forest-900 md:ml-2">{{ $t('checkout.title') }}</span>
              </div>
            </li>
          </ol>
        </nav>
  
        <div v-if="loading" class="flex justify-center items-center py-20">
          <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-terracotta-500"></div>
        </div>
  
        <div v-else-if="error" class="bg-white p-4 sm:p-8 rounded-lg shadow-md text-center">
          <Icon name="heroicons:exclamation-circle" class="w-16 h-16 text-romana-red mx-auto mb-4" />
          <h2 class="text-2xl font-bold text-forest-900 mb-4">{{ $t('checkout.failed_to_initialize_checkout') }}</h2>
          <p class="text-forest-700 mb-6">{{ error }}</p>
          <NuxtLink to="/cart" class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-full shadow-sm text-white bg-terracotta-500 hover:bg-terracotta-600">
            {{ $t('checkout.back') }}
          </NuxtLink>
        </div>
  
        <div v-else-if="cartStore.isEmpty" class="bg-white p-4 sm:p-8 rounded-lg shadow-md text-center">
          <Icon name="heroicons:shopping-cart" class="w-16 h-16 text-forest-300 mx-auto mb-4" />
          <h2 class="text-2xl font-bold text-forest-900 mb-4">{{ $t('checkout.empty_cart') }}</h2>
          <p class="text-forest-700 mb-6">{{ $t('checkout.add_items') }}</p>
          <NuxtLink to="/products" class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-full shadow-sm text-white bg-terracotta-500 hover:bg-terracotta-600">
            {{ $t('checkout.browse_products') }}
          </NuxtLink>
        </div>
  
        <div v-else class="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-8">
          <!-- Checkout Steps -->
          <div class="lg:col-span-2">
            <div class="bg-white rounded-lg shadow-md overflow-hidden checkout-steps-container">
              <!-- Checkout Progress -->
              <div class="px-3 sm:px-6 py-4 bg-wheat-100 border-b border-wheat-200">
                <div class="flex justify-between overflow-x-auto">
                  <div v-for="(step, index) in steps" :key="index" 
                       class="flex items-center shrink-0 mr-2 relative" 
                       :class="{'text-forest-900 font-medium': currentStep >= index, 'text-forest-400': currentStep < index}">
                    <div class="flex items-center justify-center w-6 h-6 sm:w-8 sm:h-8 rounded-full mr-1 sm:mr-2 transition-all duration-300"
                         :class="currentStep > index ? 'bg-emerald-500 text-white' : currentStep === index ? 'bg-terracotta-500 text-white' : 'bg-wheat-200 text-forest-500'">
                      <span v-if="currentStep > index">
                        <Icon name="heroicons:check" class="w-4 h-4 sm:w-5 sm:h-5" />
                      </span>
                      <span v-else>
                        {{ index + 1 }}
                      </span>
                      <div v-if="currentStep === index && stepLoading" class="absolute inset-0 rounded-full border-2 border-terracotta-500 border-t-transparent animate-spin"></div>
                    </div>
                    <span class="text-xs sm:text-sm">{{ step }}</span>
                    <div v-if="index < steps.length - 1" class="hidden sm:block w-8 h-0.5 mx-1" :class="currentStep > index ? 'bg-emerald-500' : 'bg-wheat-300'"></div>
                  </div>
                </div>
              </div>
  
              <!-- Step Content -->
              <div class="p-3 sm:p-6 relative">
                <!-- Step Loading Overlay -->
                <div v-if="stepLoading" class="absolute inset-0 bg-white bg-opacity-80 z-10 flex items-center justify-center">
                  <div class="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-terracotta-500"></div>
                  <span class="ml-3 text-forest-700">{{ $t('checkout.loading_step') }}</span>
                </div>
  
                <!-- Shipping Address (First Step Now) -->
                <ShippingAddress 
                  v-if="currentStep === 0" 
                  :initialData="shippingAddress"
                  :customerData="customerData"
                  @update-address="handleUpdateAddress"
                  @update-customer="handleUpdateCustomerData"
                  @next-step="handleShippingAddressSubmit"
                />
  
                <!-- Shipping Method -->
                <ShippingMethod 
                  v-if="currentStep === 1" 
                  :shippingOptions="shippingOptions || []"
                  :selectedOption="selectedShippingOption || ''"
                  @select-shipping="handleSelectShipping"
                  @next-step="handleShippingMethodSubmit"
                  @previous-step="goToPreviousStep"
                  @update-shipping-options="updateShippingOptions"
                />
  
                <!-- Payment Method -->
                <PaymentMethod 
                  v-if="currentStep === 2" 
                  ref="paymentComponentRef"
                  :cartId="cartId"
                  :availableOptions="paymentOptions"
                  :initialOption="selectedPaymentOption as string | undefined"
                  @select-payment="handleSelectPayment"
                  @payment-ready="handlePaymentReady"
                  @payment-complete="handlePaymentComplete"
                  @next-step="handlePaymentMethodSubmit"
                  @previous-step="goToPreviousStep"
                />
  
                <!-- Review Order -->
                <ReviewOrder 
                  v-if="currentStep === 3" 
                  :cart="orderSummary"
                  :customer="customerData"
                  :shippingAddress="shippingAddress"
                  :billingAddress="sameAsBilling ? shippingAddress : billingAddress"
                  :selectedShipping="selectedShippingName"
                  :selectedPayment="selectedPaymentName"
                  @place-order="handlePlaceOrder"
                  @previous-step="goToPreviousStep"
                />
  
                <!-- Order Confirmation -->
                <div v-if="currentStep === 4" class="text-center">
                  <div class="bg-forest-50 p-6 rounded-lg mb-6">
                    <div class="flex justify-center mb-4">
                      <div class="rounded-full bg-emerald-100 p-3">
                        <Icon name="heroicons:check" class="w-8 h-8 text-emerald-600" />
                      </div>
                    </div>
                    <h3 class="text-xl font-bold text-forest-900 mb-2">{{ $t('checkout.order_complete') }}</h3>
                    <p class="text-forest-600 mb-4">{{ $t('checkout.order_number', { id: orderId }) }}</p>
                    <p class="text-forest-600">{{ $t('checkout.order_confirmation_message') }}</p>
                  </div>
                  
                  <!-- Order Items Summary -->
                  <div v-if="lastOrderDetails" class="bg-white p-6 rounded-lg mb-6 text-left">
                    <h4 class="text-lg font-semibold text-forest-900 mb-4 border-b pb-2">{{ $t('checkout.order_items') }}</h4>
                    
                    <div class="space-y-4">
                      <div v-for="item in lastOrderDetails.items" :key="item.id" class="flex justify-between items-center border-b border-wheat-200 pb-4">
                        <div class="flex items-center">
                          <img :src="getItemImage(item)" :alt="getProductTitle(item)" class="w-16 h-16 object-cover rounded mr-4">
                          <div>
                            <div class="checkout-item-details">
                              <div class="checkout-item-title font-semibold text-forest-900">{{ getProductTitle(item) }}</div>
                              <div class="checkout-item-variant text-xs text-forest-500 mt-0.5" v-if="getItemVariant(item)">
                                {{ getItemVariant(item) }}
                              </div>
                              <div class="checkout-item-quantity">
                                {{ $t('checkout.quantity') }}: {{ item.quantity }}
                              </div>
                            </div>
                          </div>
                        </div>
                        <div class="font-medium">
                          {{ formatPrice(item.unit_price * item.quantity) }}
                        </div>
                      </div>
                    </div>
                    
                    <!-- Order Totals -->
                    <div class="mt-4 pt-4 space-y-2">
                      <div class="flex justify-between">
                        <span class="text-forest-600">{{ $t('checkout.subtotal') }}</span>
                        <span class="font-medium">{{ formatPrice(lastOrderDetails.subtotal) }}</span>
                      </div>
                      <div class="flex justify-between">
                        <span class="text-forest-600">{{ $t('checkout.shipping') }}</span>
                        <span class="font-medium">{{ formatPrice(lastOrderDetails.shipping_total) }}</span>
                      </div>
                      <div class="flex justify-between">
                        <span class="text-forest-600">{{ $t('checkout.tax') }}</span>
                        <span class="font-medium">{{ formatPrice(lastOrderDetails.tax_total) }}</span>
                      </div>
                      <div v-if="lastOrderDetails.discount_total" class="flex justify-between">
                        <span class="text-forest-600">{{ $t('checkout.discount') }}</span>
                        <span class="font-medium text-romana-red">-{{ formatPrice(lastOrderDetails.discount_total) }}</span>
                      </div>
                      <div class="flex justify-between pt-2 border-t border-wheat-200 font-bold">
                        <span class="text-forest-900">{{ $t('checkout.total') }}</span>
                        <span class="text-forest-900">{{ formatPrice(lastOrderDetails.total) }}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div class="flex justify-center space-x-4">
                    <NuxtLink to="/" class="px-6 py-3 bg-forest-600 text-white rounded-lg hover:bg-forest-700 transition-colors">
                      {{ $t('checkout.continue_shopping') }}
                    </NuxtLink>
                    <button 
                      v-if="orderId" 
                      @click="viewOrderDetails"
                      class="px-6 py-3 bg-white border border-forest-300 text-forest-700 rounded-lg hover:bg-forest-50 transition-colors">
                      {{ $t('checkout.view_order') }}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
  
          <!-- Order Summary -->
          <div class="lg:col-span-1">
            <div class="bg-white rounded-lg shadow-md overflow-hidden sticky top-24">
              <div class="px-3 sm:px-6 py-4 bg-wheat-100 border-b border-wheat-200">
                <h3 class="text-lg font-medium text-forest-900">{{ $t('checkout.order_summary') }}</h3>
              </div>
              <div class="p-3 sm:p-6">
                <!-- Cart Items -->
                <div class="space-y-4 mb-6">
                  <div class="checkout-item" v-for="item in cartStore.items" :key="item.id">
                    <div class="checkout-item-image">
                      <nuxt-img
                        :src="getItemImage(item)"
                        :alt="item.product_title"
                        width="60"
                        height="60"
                        loading="lazy"
                      />
                    </div>
                    <div class="checkout-item-details">
                      <div class="checkout-item-title font-semibold text-forest-900">{{ item.product_title || getProductTitle(item) }}</div>
                      <div class="checkout-item-variant text-xs text-forest-500 mt-0.5" v-if="getItemVariant(item)">
                        {{ getItemVariant(item) }}
                      </div>
                      <div class="checkout-item-quantity">
                        {{ $t('checkout.quantity') }}: {{ item.quantity }}
                      </div>
                    </div>
                    <div class="checkout-item-price">
                      {{ formatPrice(item.unit_price * item.quantity) }}
                    </div>
                  </div>
                </div>
  
                <!-- Price Breakdown -->
                <div class="space-y-2 py-4 border-t border-wheat-200">
                  <div class="flex justify-between">
                    <span class="text-xs sm:text-sm text-forest-700">{{ $t('checkout.subtotal') }}</span>
                    <span class="text-xs sm:text-sm font-medium text-forest-900">{{ formatPrice(subtotal || 0) }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-xs sm:text-sm text-forest-700">{{ $t('checkout.shipping') }}</span>
                    <span class="text-xs sm:text-sm font-medium text-forest-900">{{ displayShippingCost ? formatPrice(displayShippingCost) : $t('checkout.calculated_next_step') }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-xs sm:text-sm text-forest-700">{{ $t('checkout.tax') }}</span>
                    <span class="text-xs sm:text-sm font-medium text-forest-900">{{ taxAmount ? formatPrice(taxAmount) : $t('checkout.calculated_checkout') }}</span>
                  </div>
                  <div v-if="discountAmount" class="flex justify-between">
                    <span class="text-xs sm:text-sm text-forest-700">{{ $t('checkout.discount') }}</span>
                    <span class="text-xs sm:text-sm font-medium text-romana-red">-{{ formatPrice(discountAmount) }}</span>
                  </div>
                </div>
  
                <!-- Total -->
                <div class="flex justify-between pt-4 border-t border-wheat-200">
                  <span class="text-sm sm:text-base font-medium text-forest-900">{{ $t('checkout.total') }}</span>
                  <span class="text-sm sm:text-base font-bold text-forest-900">{{ formatPrice(totalAmount || 0) }}</span>
                </div>
  
                <!-- Discount Code -->
                <div class="mt-6">
                  <div class="flex space-x-2">
                    <input 
                      v-model="discountCode" 
                      type="text" 
                      :placeholder="$t('checkout.discount_code')" 
                      class="flex-1 min-w-0 block w-full px-3 py-2 rounded-md border border-wheat-300 shadow-sm focus:outline-none focus:ring-terracotta-500 focus:border-terracotta-500 text-xs sm:text-sm"
                    />
                    <button 
                      @click="handleApplyDiscount" 
                      :disabled="applyingDiscount || !discountCode || currentStep > 3"
                      class="inline-flex items-center px-2 sm:px-4 py-2 border border-transparent text-xs sm:text-sm font-medium rounded-md shadow-sm text-white bg-terracotta-500 hover:bg-terracotta-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-terracotta-500 disabled:bg-terracotta-300 disabled:cursor-not-allowed"
                    >
                      <span v-if="applyingDiscount" class="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></span>
                      {{ $t('checkout.apply_discount') }}
                    </button>
                  </div>
                  <p v-if="discountError" class="mt-2 text-xs sm:text-sm text-romana-red">{{ discountError }}</p>
                  <p v-if="discountSuccess" class="mt-2 text-xs sm:text-sm text-emerald-600">{{ discountSuccess }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </template>
  
  <script setup lang="ts">
  import { ref, computed, reactive, onMounted, watch } from 'vue'
  import { useRouter } from 'vue-router'
  import { useCartStore } from '~/stores/cart'
  import { useLocaleStore } from '~/stores/locale'
  import { useMedusaService } from '~/composables/useMedusaService'
  import { useI18n } from 'vue-i18n'
  import type { ShippingOption } from '~/types/shipping'
  import type { MedusaCart } from '~/types/cart'
  import { useToast } from '#imports'
  import { useAuth } from '~/composables/useAuth'
  import { useCustomer } from '~/composables/useCustomer'
  import { useRuntimeConfig } from '#imports'

  // Get the runtime config
  const config = useRuntimeConfig()

  // Define the Address interface directly instead of importing it
  interface Address {
    id?: string
    first_name: string
    last_name: string
    company?: string
    address_1: string
    address_2?: string
    city: string
    province?: string
    postal_code: string
    country_code: string
    phone?: string
    is_default_shipping?: boolean
    is_default_billing?: boolean
    address_type?: 'shipping' | 'billing' | 'both'
    metadata?: {
      address_type?: 'shipping' | 'billing' | 'both'
      is_default_shipping?: boolean
      is_default_billing?: boolean
    }
  }
  
  interface PaymentOption {
    id: string
    name: string
    data: Record<string, any>
  }
  
  interface CustomerData {
    id?: string;
    email: string;
    first_name: string;
    last_name: string;
    phone?: string;
    has_account?: boolean;
    address1?: string;
    address2?: string;
    city?: string;
    province?: string;
    postalCode?: string;
    country?: string;
    createAccount?: boolean;
    password?: string;
  }
  
  // Define shipping address interface to include the extended properties
  interface ShippingAddressData {
    // Standard address fields
    id?: string
    first_name: string
    last_name: string
    company?: string
    address_1: string
    address_2?: string
    city: string
    province?: string
    postal_code: string
    country_code: string
    phone?: string
    
    // Extended properties for enhanced address management
    shipping?: any
    billing?: any
    sameAsBilling?: boolean
    selectedShippingAddressId?: string | null
    selectedBillingAddressId?: string | null
    saveAddress?: boolean
  }
  
  // Add these interfaces at the top of the script section
  interface OrderResponse {
    type: string;
    data?: {
      order: {
        id: string;
        status: string;
        // Add other order properties as needed
      };
    };
    error?: {
      message: string;
    };
  }
  
  // Add this after the OrderResponse interface
  
  // Define PaymentSession interface
  interface PaymentSession {
    id: string;
    provider_id: string;
    is_selected: boolean;
    data: any;
  }
  
  // Add this after the PaymentSession interface

  // Define a more flexible version of Address for string indexing
  interface AddressWithIndex extends Address {
    [key: string]: any;
  }
  
  // Shipping and billing address interfaces with proper metadata
  interface AddressWithMetadata extends Address {
    metadata?: {
      address_type?: 'shipping' | 'billing' | 'both'
      is_default_shipping?: boolean
      is_default_billing?: boolean
      is_easybox?: boolean
      easybox_locker_id?: string
      easybox_locker_name?: string
    }
  }

  // Helper functions for cart items
  const getItemImage = (item: any): string => {
    // First check if we have the product_id and there's a locally stored image for it
    if (item.product_id) {
      const productId = item.product_id;
      const cachedImages = localStorage.getItem(`product_images_${productId}`);
      if (cachedImages) {
        try {
          const images = JSON.parse(cachedImages);
          if (images.length > 0) {
            return images[0].url;
          }
        } catch (e) {
          console.error('Failed to parse cached product images:', e);
        }
      }
    }

    // Check for variant product images array first (highest priority)
    if (item.variant?.product?.images && 
        Array.isArray(item.variant.product.images) && 
        item.variant.product.images.length > 0) {
      return item.variant.product.images[0].url;
    }
    
    // Check if the item has images array (second priority)
    if (item.images && 
        Array.isArray(item.images) && 
        item.images.length > 0) {
      return item.images[0].url;
    }
    
    // Check for variant's product thumbnail (third priority)
    if (item.variant?.product?.thumbnail && 
        item.variant.product.thumbnail !== '/images/placeholder.jpg') {
      return item.variant.product.thumbnail;
    }
    
    // Check if we have an image in the item directly (fourth priority)
    if (item.image && 
        typeof item.image === 'string' && 
        item.image !== '/images/placeholder.jpg') {
      return item.image;
    }
    
    // Check the thumbnail as fallback (fifth priority)
    if (item.thumbnail && 
        item.thumbnail !== '/images/placeholder.jpg') {
      return item.thumbnail;
    }
    
    // If no image is found, use placeholder
    return '/images/placeholder.jpg';
  }

  const getItemVariant = (item: any): string | null => {
    // Check for variant_title directly on the item
    if (item.variant_title) {
      return item.variant_title;
    }
    
    // Check nested variant object
    if (item.variant && item.variant.title) {
      return item.variant.title;
    }
    
    return null;
  }

  const getCurrency = (item: any): string => {
    if (item.currency) {
      return item.currency;
    }
    
    if (cartStore.cart?.region?.currency_code) {
      return cartStore.cart.region.currency_code;
    }
    
    return localeStore.currentCountry?.currencies[0] || 'RON';
  }
  
  // Utilities and services
  const router = useRouter()
  const { formatCurrency } = useUtilities()
  const medusaService = useMedusaService()
  const { t } = useI18n()
  const toast = useToast()
  const { isAuthenticated } = useCustomer()
  
  // Stores
  const cartStore = useCartStore() as import('~/types/cart').CartStore
  const localeStore = useLocaleStore() as import('~/types/locale').LocaleStore
  
  // State
  const loading = ref(false)
  const error = ref<string | undefined>(undefined)
  const currentStep = ref(0)
  const steps = [
    t('checkout.shipping_step'),
    t('checkout.delivery_step'),
    t('checkout.payment_step'), 
    t('checkout.review_step')
  ]
  const processingPayment = ref(false)
  const paymentReady = ref(false)
  const hasAppliedShipping = ref(false)
  const cart = computed(() => cartStore.cart)
  const cartId = ref<string>('')
  const orderId = ref<string | null>(null)
  const lastOrderDetails = ref<any>(null)
  const paymentData = ref<any>(null)
  
  // Customer and shipping information
  const customerData = ref<CustomerData>({
    email: '',
    first_name: '',
    last_name: '',
    phone: '',
    has_account: false
  })
  
  // Update the shippingAddress ref to use the extended type
  const shippingAddress = ref<ShippingAddressData>({
    first_name: '',
    last_name: '',
    company: '',
    address_1: '',
    address_2: '',
    city: '',
    province: '',
    postal_code: '',
    country_code: '',
    phone: '',
    saveAddress: false
  })
  
  // Add billingAddress to the state
  const billingAddress = ref<Address>({
    first_name: '',
    last_name: '',
    address_1: '',
    address_2: '',
    city: '',
    province: '',
    postal_code: '',
    country_code: '',
    phone: ''
  })
  
  // Add a flag to track if billing is same as shipping
  const sameAsBilling = ref(true)
  
  // Shipping and payment options
  const shippingOptions = ref<ShippingOption[]>([])
  const selectedShippingOption = ref<string | null>(null)
  const paymentOptions = ref<PaymentOption[]>([])
  const selectedPaymentOption = ref<string | null>(null)
  const paymentFormData = ref<any>({})
  
  // Discount handling
  const discountCode = ref('')
  const discountError = ref('')
  const discountSuccess = ref('')
  const applyingDiscount = ref(false)
  
  // Order summary values
  const shippingCost = ref(0)
  const taxAmount = ref(0)
  const discountAmount = ref(0)

  // Update the subtotal and totalAmount computeds
  const subtotal = computed(() => cartStore.cart?.subtotal || 0)

  // Computed property to get shipping cost from the correct location in cart data
  const displayShippingCost = computed(() => {
    if (!cartStore.cart) return 0

    // First check if shipping method is stored in metadata (this is where the Cloudflare backend stores it)
    if (cartStore.cart.metadata && cartStore.cart.metadata.shipping_method && cartStore.cart.metadata.shipping_method.price) {
      console.log('[Checkout] Found shipping cost in metadata:', cartStore.cart.metadata.shipping_method.price)
      return cartStore.cart.metadata.shipping_method.price
    }

    // Fallback: check shipping_total from the cart
    if (cartStore.cart.shipping_total && cartStore.cart.shipping_total > 0) {
      console.log('[Checkout] Found shipping cost in shipping_total:', cartStore.cart.shipping_total)
      return cartStore.cart.shipping_total
    }

    // Another fallback: check shipping_methods array if it exists
    if (cartStore.cart && 'shipping_methods' in cartStore.cart &&
        Array.isArray(cartStore.cart.shipping_methods) &&
        cartStore.cart.shipping_methods.length > 0) {
      const total = cartStore.cart.shipping_methods.reduce((total: number, method: any) => {
        return total + (method.price || method.amount || 0)
      }, 0)
      console.log('[Checkout] Found shipping cost in shipping_methods array:', total)
      return total
    }

    // Default to 0 if no shipping method found
    console.log('[Checkout] No shipping method found, defaulting to 0')
    return 0
  })

  const totalAmount = computed(() => {
    // First try to use the cart's total directly as it should include shipping
    if (cartStore.cart?.total && cartStore.cart.total > 0) {
      return cartStore.cart.total
    }
    
    // Fallback: calculate total from individual components using our computed shipping cost
    const subtotal = cartStore.cart?.subtotal || 0
    const shipping = displayShippingCost.value || 0
    const tax = cartStore.cart?.tax_total || 0
    const discount = cartStore.cart?.discount_total || 0
    
    return subtotal + shipping + tax - discount
  })

  // Order summary for review step - ensure we're using the cart's values directly
  const orderSummary = computed<MedusaCart>(() => {
    const cart = cartStore.cart
    if (!cart) {
      // Return a default empty cart to ensure it's never null
      return {
        id: '',
        items: [],
        subtotal: 0,
        shipping_total: 0,
        tax_total: 0,
        discount_total: 0,
        total: 0,
        region_id: '',
        // Add any other required properties for MedusaCart
      }
    }

    // Use displayShippingCost instead of cart.shipping_total to get the correct shipping cost
    const shippingTotal = displayShippingCost.value || 0
    const subtotalAmount = cart.subtotal || 0
    const taxTotal = cart.tax_total || 0
    const discountTotal = cart.discount_total || 0
    
    // Calculate the correct total including the proper shipping cost
    const calculatedTotal = subtotalAmount + shippingTotal + taxTotal - discountTotal

    return {
      ...cart,
      // Ensure we're using the correct totals from the cart
      subtotal: subtotalAmount,
      shipping_total: shippingTotal, // Use the properly calculated shipping cost
      tax_total: taxTotal,
      discount_total: discountTotal,
      total: cart.total && cart.total > 0 ? cart.total : calculatedTotal // Use cart.total if available, otherwise calculate
    }
  })

  // Update the refreshTotals function to extract shipping cost from cart's shipping_methods if available
  const refreshTotals = () => {
    if (cartStore.cart) {
      // First check if shipping method is stored in metadata (this is where it actually is)
      if (cartStore.cart.metadata && cartStore.cart.metadata.shipping_method && cartStore.cart.metadata.shipping_method.price) {
        shippingCost.value = cartStore.cart.metadata.shipping_method.price
        console.log('[Checkout] Found shipping cost in metadata:', shippingCost.value)
      }
      // Fallback: check shipping_total from the cart as calculated by the backend
      else if (cartStore.cart.shipping_total) {
        shippingCost.value = cartStore.cart.shipping_total
        console.log('[Checkout] Found shipping cost in shipping_total:', shippingCost.value)
      }
      // Another fallback: check shipping_methods array if it exists
      else if (cartStore.cart && 'shipping_methods' in cartStore.cart && 
               Array.isArray(cartStore.cart.shipping_methods) && 
               cartStore.cart.shipping_methods.length > 0) {
        // Sum up all shipping method costs
        shippingCost.value = cartStore.cart.shipping_methods.reduce((total: number, method: any) => {
          return total + (method.price || method.amount || 0)
        }, 0)
        console.log('[Checkout] Found shipping cost in shipping_methods array:', shippingCost.value)
      }
      // Default to 0 if no shipping method found
      else {
        shippingCost.value = 0
        console.log('[Checkout] No shipping method found, defaulting to 0')
      }
      
      taxAmount.value = cartStore.cart.tax_total || 0
      discountAmount.value = cartStore.cart.discount_total || 0
      
      console.log('[Checkout] Refreshed totals:', {
        subtotal: cartStore.cart.subtotal,
        shipping: shippingCost.value,
        tax: taxAmount.value,
        discount: discountAmount.value,
        total: cartStore.cart.total
      })
    }
  }

  // Add watcher to update totals when cart changes
  watch(() => cartStore.cart, () => {
    refreshTotals()
  }, { immediate: true })

  // Add watcher to update cartId when cartStore.cartId changes
  watch(() => cartStore.cartId, (newCartId) => {
    if (newCartId) {
      cartId.value = newCartId;
    }
  }, { immediate: true })

  // Modify the initCheckout function to call refreshTotals
  const initCheckout = async () => {
    loading.value = true
    error.value = undefined
    
    try {
      // Check if cart exists and has items
      if (cartStore.isEmpty) {
        throw new Error(t('checkout.empty_cart_message'))
      }
      
      // Set the cart ID from the cart store
      const id = cartStore.cartId
      
      if (!id) {
        throw new Error(t('checkout.no_cart_id'))
      }
      
      // Skip cart validation if we already have a valid cart in store for performance
      if (!cartStore.cart) {
        // Only validate cart if we don't have cart data
        try {
          const cart = await medusaService.retrieveCart(id)
          if (!cart) {
            console.log('Cart not found, may need to create a new one')
            await cartStore.createCart()
            throw new Error(t('checkout.cart_not_available'))
          }
        } catch (cartErr) {
          console.error('Error validating cart:', cartErr)
          // If there's an error with the cart (including "already completed"), clear cart state and create a new one
          await cartStore.clearCart()
          
          // Try to re-initialize with a new cart
          await cartStore.createCart()
          
          if (cartStore.isEmpty) {
            throw new Error(t('checkout.cart_error_recreated'))
          } else {
            // We have successfully created a new cart with the items, proceed
            console.log('Created new cart after error:', cartStore.cartId)
          }
        }
      }
      
      // Refresh totals to show shipping cost
      refreshTotals()
      
      // Initialize shipping options only if needed
      if (shippingOptions.value.length === 0) {
        await updateShippingOptions()
      }
    } catch (err) {
      console.error('Error initializing checkout:', err)
      error.value = err instanceof Error ? err.message : t('checkout.initialization_error')
    } finally {
      loading.value = false
    }
  }

  // Computed properties for display
  const selectedShippingName = computed(() => {
    const option = shippingOptions.value.find((opt: ShippingOption) => opt.id === selectedShippingOption.value)
    return option ? option.name : t('checkout.standard_shipping')
  })
  
  const selectedPaymentName = computed(() => {
    const providerId = selectedPaymentOption.value?.toLowerCase() || ''; // Get the selected provider ID

    console.log(`[DEBUG] selectedPaymentOption.value in computed: ${selectedPaymentOption.value}`); // Add debug log

    if (!providerId) {
      return t('checkout.select_payment_method'); // Or a default message
    }

    // Direct check for COD variations
    if (providerId.includes('cash_on_delivery') || 
        providerId.includes('cod') || 
        providerId.includes('utánvét') || 
        providerId === 'manual') { // Treat 'manual' as COD for now
      return t('checkout.cash_on_delivery');
    }
    
    // Direct check for Stripe/Card variations
    if (providerId.includes('stripe') || 
        providerId.includes('card') || 
        providerId.includes('kártya')) {
      return t('checkout.credit_card');
    }

    // Fallback: Try finding the name in paymentOptions (less reliable)
    const option = paymentOptions.value.find((opt: PaymentOption) => opt.id.toLowerCase() === providerId);
    if (option?.name) {
       console.warn(`[DEBUG] Falling back to option.name for providerId: ${providerId}`);
       // Attempt to translate common names if possible, otherwise use the name directly
       if (option.name.toLowerCase().includes('cash') || option.name.toLowerCase().includes('utánvét')) {
         return t('checkout.cash_on_delivery');
       }
       if (option.name.toLowerCase().includes('card') || option.name.toLowerCase().includes('stripe') || option.name.toLowerCase().includes('kártya')) {
         return t('checkout.credit_card');
       }
       return option.name; // Return the untranslated name as a last resort within this block
    }

    // Final fallback if no match found
    console.warn(`[DEBUG] No specific name found for providerId: ${providerId}, using default.`);
    return t('checkout.payment_method'); // Generic fallback
  });
  
  // Format price with currency
  const formatPrice = (price: number) => {
    // Make sure price is a valid number
    const validPrice = typeof price === 'number' && !isNaN(price) ? price : 0
    
    // Use cart's currency if available
    const cartCurrency = cartStore.cart?.region?.currency_code
    
    return formatCurrency(validPrice, cartCurrency)
  }
  
  // Computed property for cart currency
  const cartCurrency = computed(() => {
    return cartStore.cart?.region?.currency_code || orderSummary.value?.region?.currency_code || 'RON'
  })
  
  // Navigation functions
  const stepLoading = ref(false)
  const goToNextStep = async () => {
    // Check the current active step
    if (currentStep.value < steps.length - 1) {
      // Show loading indicator before changing step
      stepLoading.value = true
      
      // Scroll first to give immediate feedback
      scrollToCheckoutSteps()
      
      // Increment step and clear loading
      currentStep.value++
      stepLoading.value = false
      
    } else {
      // Before proceeding to review, make sure we preserve the selected payment method
      // Only do minimal validation without additional cart calls for performance
      if (selectedPaymentOption.value) {
        console.log(`Preserving payment provider ${selectedPaymentOption.value} when going to review step`)
        // Skip unnecessary cart retrieval and re-selection for performance
      }
      
      // Handle step change directly
      if (currentStep.value < steps.length - 1) {
        // Scroll first to give immediate feedback
        scrollToCheckoutSteps()
        currentStep.value++
      }
    }
    
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    })
  }
  
  const goToPreviousStep = () => {
    if (currentStep.value > 0) {
      // Show loading indicator before changing step
      stepLoading.value = true
      
      // Scroll first to give immediate feedback
      scrollToCheckoutSteps()
      
      // Decrement step and clear loading
      currentStep.value--
      stepLoading.value = false
    }
  }
  
  // Add this new function to handle scrolling
  const scrollToCheckoutSteps = () => {
    // Add a small delay to ensure DOM updates before scrolling
    setTimeout(() => {
      const checkoutStepsElement = document.querySelector('.checkout-steps-container')
      if (checkoutStepsElement) {
        checkoutStepsElement.scrollIntoView({ behavior: 'smooth', block: 'start' })
      }
    }, 100)
  }
  
  // Customer information handling
  const handleUpdateCustomerData = async (data: CustomerData) => {
    customerData.value = { ...data }
    
    // Save customer data to localStorage for persistence (removed immediate cart email update for performance)
    if (data.email) {
      localStorage.setItem('checkoutCustomerData', JSON.stringify(data))
      console.log('Customer data updated locally:', data.email)
    }

    // Handle account creation if requested
    if (data.createAccount && data.password) {
      try {
        // Use the register function from useAuth
        const { register } = useAuth()
        const result = await register(
          data.email,
          data.password,
          data.first_name,
          data.last_name,
          data.phone
        )
        
        if (result && result.isSignUpComplete) {
          // Show success message
          toast.add({
            title: t('checkout.account_created'),
            description: t('checkout.account_created_description'),
            color: 'green'
          })
          
          // Update customer has_account status
          customerData.value.has_account = true
          
          // Make sure the cart is associated with the customer
          try {
            // Explicitly associate the cart with the customer after account creation
            try {
              // Cart association should happen automatically when user is authenticated
              console.log('User account created - cart association should happen automatically');
              
              // Removed cart refresh for better performance
              console.log('Account created and associated with cart');
            } catch (associationError) {
              console.error('Error refreshing cart after account creation:', associationError);
            }
            
            console.log('Cart associated with newly created customer account');
          } catch (cartErr) {
            console.error('Error associating cart with customer account:', cartErr);
          }
        } else {
          // Account creation was initiated but requires verification
          toast.add({
            title: t('checkout.account_pending_verification'),
            description: t('checkout.please_verify_email'),
            color: 'blue'
          })
          
          // Save the current checkout state
          const checkoutData = {
            email: data.email,
            firstName: data.first_name,
            lastName: data.last_name,
            phone: data.phone,
            cartId: cartId.value,
            shippingAddress: shippingAddress.value,
            billingAddress: billingAddress.value,
            currentStep: currentStep.value
          }
          
          // Save checkout data to localStorage
          localStorage.setItem('checkoutData', JSON.stringify(checkoutData))
          
          // Redirect to verification page
          navigateTo('/verify')
          return
        }
      } catch (error: any) {
        console.error('Error creating account during checkout:', error)
        toast.add({
          title: t('checkout.account_creation_error'),
          description: error.message || t('checkout.account_creation_error_description'),
          color: 'red'
        })
      }
    }
  }

  
  // Function to handle address update from ShippingAddress component
  const handleUpdateAddress = (addressData: any) => {
    console.log('[DEBUG] Address update received in checkout page:', addressData);
    console.log('[DEBUG] SaveAddress flag value:', addressData.saveAddress);
    
    // Store both shipping and billing addresses
    shippingAddress.value = {
      ...addressData.shipping,
      sameAsBilling: addressData.sameAsBilling,
      billing: addressData.billing, // Store billing data for state restoration
      selectedShippingAddressId: addressData.selectedShippingAddressId,
      selectedBillingAddressId: addressData.selectedBillingAddressId,
      // Make sure we explicitly transfer the saveAddress flag
      saveAddress: addressData.saveAddress || false
    };
    
    // Update the checkout page's sameAsBilling ref - THIS WAS MISSING!
    sameAsBilling.value = addressData.sameAsBilling;
    
    // Handle billing address based on the same-as-billing flag
    if (addressData.sameAsBilling) {
      billingAddress.value = { ...addressData.shipping };
    } else {
      billingAddress.value = { ...addressData.billing };
    }
    
    console.log('[DEBUG] Updated checkout sameAsBilling to:', sameAsBilling.value);
    console.log('[DEBUG] Billing address updated to:', billingAddress.value);
    
    console.log('[DEBUG] Updated shipping address with saveAddress flag:', shippingAddress.value.saveAddress);
  };

  // Create addresses composable
  const useAddressHandling = () => {
    // Clean address data for API
    const cleanAddressForAPI = (address: any): AddressWithMetadata => {
      // Start with base address fields
      const cleanedAddress: AddressWithMetadata = {
        first_name: address.first_name,
        last_name: address.last_name,
        company: address.company || '',
        address_1: address.address_1,
        address_2: address.address_2 || '',
        city: address.city,
        province: address.province || '',
        postal_code: address.postal_code,
        country_code: address.country_code.toLowerCase(),
        phone: address.phone || ''
      }

      // Initialize metadata object if any metadata fields exist
      if (address.metadata || address.is_easybox || address.easybox_locker_id) {
        cleanedAddress.metadata = {
          ...(address.metadata || {}),
          // Preserve easybox info if it exists
          ...(address.is_easybox ? { is_easybox: true } : {}),
          ...(address.easybox_locker_id ? { easybox_locker_id: address.easybox_locker_id } : {}),
          ...(address.easybox_locker_name ? { easybox_locker_name: address.easybox_locker_name } : {})
        }
      }

      return cleanedAddress
    }

    // Save address to user account with enhanced duplicate detection
    const saveAddressToAccount = async (address: AddressWithMetadata, isShipping: boolean, isDefault: boolean) => {
      // Get existing addresses to check for duplicates
      const existingAddressesResponse = await medusaService.getCustomerAddresses()
      const existingAddresses = existingAddressesResponse?.addresses || []

      // Prepare address data - NEVER set default flags in checkout to prevent overwrites
      const addressToSave = {
        ...address,
        metadata: {
          ...address.metadata,
          address_type: address.metadata?.address_type || (isShipping ? 'shipping' : 'billing')
          // Do NOT set is_default_shipping or is_default_billing here!
        }
      }

      // Enhanced duplicate detection function
      const areAddressesIdentical = (addr1: any, addr2: any, checkType: boolean = true): boolean => {
        // Core address fields that define uniqueness
        const coreFields = [
          'first_name', 'last_name', 'address_1', 'address_line_1',
          'city', 'postal_code', 'country_code'
        ]
        
        // Optional fields that should also be compared
        const optionalFields = ['company', 'address_2', 'address_line_2', 'province', 'state', 'phone']
        
        // Check core fields (required for identity)
        const coreMatch = coreFields.every(field => {
          // Handle both address_1/address_line_1 naming variations
          let val1 = addr1[field] || addr1[field === 'address_1' ? 'address_line_1' : field === 'address_line_1' ? 'address_1' : field] || ''
          let val2 = addr2[field] || addr2[field === 'address_1' ? 'address_line_1' : field === 'address_line_1' ? 'address_1' : field] || ''
          
          val1 = val1.toString().toLowerCase().trim()
          val2 = val2.toString().toLowerCase().trim()
          
          return val1 === val2
        })
        
        if (!coreMatch) return false
        
        // Check optional fields
        const optionalMatch = optionalFields.every(field => {
          let val1 = addr1[field] || ''
          let val2 = addr2[field] || ''
          
          val1 = val1.toString().toLowerCase().trim()
          val2 = val2.toString().toLowerCase().trim()
          
          return val1 === val2
        })
        
        if (!optionalMatch) return false
        
        // If we're checking types, ensure they're compatible
        if (checkType) {
          const type1 = addr1.metadata?.address_type || addr1.type || 'both'
          const type2 = addr2.metadata?.address_type || addr2.type || 'both'
          
          // Types are compatible if they're the same or one is 'both'
          return type1 === type2 || type1 === 'both' || type2 === 'both'
        }
        
        return true
      }

      // Check for exact duplicate (same address, same type)
      const exactDuplicate = existingAddresses.find((addr: any) => 
        areAddressesIdentical(addr, addressToSave, true)
      )

      if (exactDuplicate) {
        console.log('[DEBUG] Exact duplicate address found, skipping save:', exactDuplicate.id)
        return exactDuplicate
      }

      // Check for identical address with different type
      const identicalDifferentType = existingAddresses.find((addr: any) => {
        const existingType = addr.metadata?.address_type || addr.type || 'both'
        const newType = addressToSave.metadata?.address_type || 'both'
        return areAddressesIdentical(addr, addressToSave, false) && existingType !== newType
      })

      if (identicalDifferentType) {
        console.log('[DEBUG] Found identical address with different type, updating to support both')
        
        // Update existing address to support both types - no default flags
        const updateData = {
          ...identicalDifferentType,
          type: 'both',
          metadata: {
            ...identicalDifferentType.metadata,
            address_type: 'both'
            // Do NOT set is_default_shipping or is_default_billing here!
          }
        }

        try {
          const response = await medusaService.updateAddress(identicalDifferentType.id, updateData)
          if (response && response.success) {
            console.log('[DEBUG] Successfully updated existing address to support both types')
            return response.data
          }
        } catch (updateError) {
          console.warn('[DEBUG] Failed to update existing address, creating new one instead:', updateError)
        }
      }

      // No duplicates found, create new address
      try {
        const response = await medusaService.addAddress(addressToSave)
        if (response && response.success) {
          console.log('[DEBUG] Successfully created new address')
          return response.data
        } else {
          throw new Error('Failed to create address')
        }
      } catch (createError) {
        console.error('[DEBUG] Error creating address:', createError)
        throw createError
      }
    }

    // Update cart state with metadata
    const updateCartState = async (cartId: string, shippingAddressData: any, billingAddressData: any, sameAsBilling: boolean) => {
      try {
        // Clean and prepare addresses with metadata
        const cleanedShippingAddress = cleanAddressForAPI(shippingAddressData)
        const cleanedBillingAddress = sameAsBilling 
          ? cleanAddressForAPI(shippingAddressData)
          : cleanAddressForAPI(billingAddressData)

        // Update cart with addresses
        await medusaService.updateCart(cartId, {
          shipping_address: cleanedShippingAddress,
          billing_address: cleanedBillingAddress
        })

        // Only refresh cart if needed - reduce API calls
        // await cartStore.refreshCart() // Removed for performance

        // Verify addresses were saved
        if (cartStore.cart) {
          console.log('Addresses after update:', {
            shipping: cartStore.cart.shipping_address,
            billing: cartStore.cart.billing_address,
            metadata: cartStore.cart.metadata
          })
        }
      } catch (err) {
        console.error('Failed to update cart state:', err)
        throw err
      }
    }

    // Save addresses to user account with enhanced logic
    const handleAddressSaving = async (shippingAddressData: any, billingAddressData: any, sameAsBilling: boolean) => {
      try {
        // Removed unnecessary 500ms delay for performance
        
        // Save shipping address - but NOT if it's an easybox address
        const cleanedShippingAddress = cleanAddressForAPI(shippingAddressData)
        
        // Check if this is an easybox address by looking at cart metadata
        const isEasyboxDelivery = cartStore.cart?.metadata?.easybox_locker_id ||
                                  cleanedShippingAddress.company?.includes('EasyBox') ||
                                  cleanedShippingAddress.address_2?.includes('EasyBox ID:')
        
        if (!isEasyboxDelivery) {
          const savedShippingAddress = await saveAddressToAccount(cleanedShippingAddress, true, false)
          console.log('[DEBUG] Shipping address saved to account (not easybox):', savedShippingAddress?.id)
        } else {
          console.log('[DEBUG] Skipping address save - this is an easybox delivery')
        }

        // Save billing address if different
        if (!sameAsBilling && billingAddressData) {
          const cleanedBillingAddress = cleanAddressForAPI(billingAddressData)
          const savedBillingAddress = await saveAddressToAccount(cleanedBillingAddress, false, false)
          console.log('[DEBUG] Billing address saved to account:', savedBillingAddress?.id)
        }

        console.log('[DEBUG] Address saving completed successfully')
      } catch (err) {
        console.error('[DEBUG] Error saving addresses to account:', err)
        // Don't throw the error to prevent breaking the checkout flow
        // Just log it and continue
      }
    }

    return {
      cleanAddressForAPI,
      updateCartState,
      handleAddressSaving
    }
  }

  // Use inside setup function
  const { updateCartState, handleAddressSaving } = useAddressHandling()

  // Initialize
  onMounted(async () => {
    // First check if we're coming back from verification with saved checkout data
    let restoredCheckoutState = false;
    
    try {
      const savedCheckoutData = localStorage.getItem('checkoutData');
      if (savedCheckoutData) {
        const checkoutData = JSON.parse(savedCheckoutData);
        console.log('Found saved checkout data, restoring state:', checkoutData);
        
        // Restore customer data
        if (checkoutData.email) {
          customerData.value = {
            email: checkoutData.email,
            first_name: checkoutData.firstName || '',
            last_name: checkoutData.lastName || '',
            phone: checkoutData.phone || '',
            has_account: true // Since they've just verified their account
          };
        }
        
        // Restore shipping address if available
        if (checkoutData.shippingAddress) {
          shippingAddress.value = checkoutData.shippingAddress;
        }
        
        // Restore billing address if available
        if (checkoutData.billingAddress) {
          billingAddress.value = checkoutData.billingAddress;
        }
        
        // Restore cart ID if available
        if (checkoutData.cartId) {
          cartId.value = checkoutData.cartId;
          await cartStore.setCartId(checkoutData.cartId);
        }
        
        // Restore current step if available
        if (typeof checkoutData.currentStep === 'number') {
          // Don't go back to earlier steps than shipping (step 1)
          currentStep.value = Math.max(checkoutData.currentStep, 1);
        }
        
        // Clear the saved checkout data now that we've restored it
        localStorage.removeItem('checkoutData');
        restoredCheckoutState = true;
        
        // Update addresses on the cart only if we have valid data
        if (cartId.value && shippingAddress.value && shippingAddress.value.address_1) {
          try {
            await updateCartState(
              cartId.value,
              shippingAddress.value,
              billingAddress.value,
              sameAsBilling.value
            );
          } catch (error) {
            console.error('Error updating addresses after restoring checkout state:', error);
          }
        }
        
        // Cart association happens automatically when user is authenticated
        console.log('Cart association happens automatically for authenticated users');
        
        // Only update shipping options if we're on shipping step, skip cart refresh for performance
        if (currentStep.value === 1) {
          await updateShippingOptions()
        }
        
        toast.add({
          title: t('checkout.welcome_back'),
          description: t('checkout.checkout_state_restored'),
          color: 'green'
        });
      }
    } catch (error) {
      console.error('Error restoring checkout state:', error);
    }
    
    // If we didn't restore checkout state, continue with normal initialization
    if (!restoredCheckoutState) {
      loading.value = true
      error.value = undefined
      
      // Get cart ID from store
      cartId.value = cartStore.cartId
      console.log(`Current cart ID: ${cartId.value}`)
      
      try {
        // Make sure we have a valid cart
        if (!cartId.value) {
          console.log('No cart ID found, creating new cart')
          await cartStore.createCart()
          cartId.value = cartStore.cartId
        } else {
          // Cart already exists, no need to refresh
          console.log('Using existing cart:', cartId.value);
        }
        
        // Check for EasyBox delivery data and auto-fill shipping address
        if (cartStore.cart) {
          console.log('Checking for EasyBox delivery data in cart...')
          
          // Check if this is an EasyBox delivery
          const isEasyboxDelivery = cartStore.cart.is_easybox_delivery || 
                                    (cartStore.cart.metadata && cartStore.cart.metadata.shipping_method && 
                                     cartStore.cart.metadata.shipping_method.name === 'easybox')
          
          if (isEasyboxDelivery && cartStore.cart.easybox_delivery) {
            console.log('EasyBox delivery detected, auto-filling shipping address...')
            
            const easyboxData = cartStore.cart.easybox_delivery
            console.log('EasyBox data:', easyboxData)
            
            // Auto-fill shipping address with EasyBox location data
            shippingAddress.value = {
              ...shippingAddress.value,
              // Keep customer name fields from existing data if available
              first_name: cartStore.cart.shipping_address?.first_name || shippingAddress.value.first_name || '',
              last_name: cartStore.cart.shipping_address?.last_name || shippingAddress.value.last_name || '',
              company: `${easyboxData.locker_name}` || '',
              address_1: easyboxData.locker_address || '',
              address_2: `EasyBox ID: ${easyboxData.locker_id}` || '',
              city: easyboxData.locker_city || '',
              province: easyboxData.locker_county || '',
              postal_code: cartStore.cart.shipping_address?.postal_code || '000000', // Default postal code for EasyBox
              country_code: 'ro', // EasyBox is only in Romania
              phone: cartStore.cart.shipping_address?.phone || shippingAddress.value.phone || ''
            }
            
            console.log('Auto-filled shipping address with EasyBox data:', shippingAddress.value)
            
          }
          
          // Also check legacy metadata format for backward compatibility
          else if (cartStore.cart.metadata) {
            const metadata = cartStore.cart.metadata
            
            if (metadata.easybox_locker_id && metadata.easybox_locker_name) {
              console.log('Legacy EasyBox metadata detected, auto-filling shipping address...')
              
              // Auto-fill shipping address with EasyBox location data from metadata
              shippingAddress.value = {
                ...shippingAddress.value,
                // Keep customer name fields from existing data if available
                first_name: cartStore.cart.shipping_address?.first_name || shippingAddress.value.first_name || '',
                last_name: cartStore.cart.shipping_address?.last_name || shippingAddress.value.last_name || '',
                company: metadata.easybox_locker_name || '',
                address_1: metadata.easybox_locker_address || '',
                address_2: `EasyBox ID: ${metadata.easybox_locker_id}` || '',
                city: metadata.easybox_locker_city || '',
                province: metadata.easybox_locker_county || '',
                postal_code: cartStore.cart.shipping_address?.postal_code || '000000', // Default postal code for EasyBox
                country_code: 'ro', // EasyBox is only in Romania
                phone: cartStore.cart.shipping_address?.phone || shippingAddress.value.phone || ''
              }
              
              console.log('Auto-filled shipping address with legacy EasyBox metadata:', shippingAddress.value)
              
              // Show notification to user
              toast.add({
                title: t('checkout.easybox_address_filled') || 'EasyBox Address Auto-Filled',
                description: t('checkout.easybox_address_filled_description') || `Shipping address has been automatically filled with your selected EasyBox location: ${metadata.easybox_locker_name}`,
                color: 'green'
              })
            }
          }
        }
        
        // Initialize customer data - prioritize authenticated user data
        if (isAuthenticated.value) {
          console.log('User is authenticated, fetching customer data...');
          try {
            // First ensure the customer store is initialized
            const { fetchCustomer } = useCustomer();

            // Fetch authenticated customer data using the updated getCustomer method
            const authenticatedCustomer = await medusaService.getCustomer();
            console.log('getCustomer() returned:', authenticatedCustomer);

            if (authenticatedCustomer?.email) {
              console.log('Successfully retrieved customer email from API:', authenticatedCustomer.email);
              customerData.value = {
                email: authenticatedCustomer.email,
                first_name: authenticatedCustomer.first_name || '',
                last_name: authenticatedCustomer.last_name || '',
                phone: authenticatedCustomer.phone || '',
                has_account: true
              };
              console.log('Set customerData.value to:', customerData.value);

              // If we have customer data and haven't filled shipping address yet, update name fields
              if (customerData.value.first_name || customerData.value.last_name) {
                shippingAddress.value.first_name = shippingAddress.value.first_name || customerData.value.first_name;
                shippingAddress.value.last_name = shippingAddress.value.last_name || customerData.value.last_name;
                shippingAddress.value.phone = shippingAddress.value.phone || customerData.value.phone || '';
              }

              // Cart association happens automatically when user is authenticated
              console.log('Cart will be automatically associated with authenticated customer');
            } else {
              console.warn('getCustomer() returned customer but without email, trying to fetch directly...');

              // Fallback: try to fetch customer directly
              try {
                const directCustomer = await fetchCustomer();
                if (directCustomer?.email) {
                  console.log('Successfully retrieved customer email from direct fetch:', directCustomer.email);
                  customerData.value = {
                    email: directCustomer.email,
                    first_name: directCustomer.first_name || '',
                    last_name: directCustomer.last_name || '',
                    phone: directCustomer.phone || '',
                    has_account: true
                  };
                  console.log('Set customerData.value from direct fetch to:', customerData.value);
                  
                  // Update shipping address name fields if available
                  if (customerData.value.first_name || customerData.value.last_name) {
                    shippingAddress.value.first_name = shippingAddress.value.first_name || customerData.value.first_name;
                    shippingAddress.value.last_name = shippingAddress.value.last_name || customerData.value.last_name;
                    shippingAddress.value.phone = shippingAddress.value.phone || customerData.value.phone || '';
                  }
                }
              } catch (directFetchError) {
                console.error('Error with direct customer fetch:', directFetchError);
              }
            }
          } catch (customerError) {
            console.error('Error fetching authenticated customer data:', customerError);
            // Fall back to cart-based initialization below
          }
        } else {
          console.log('User is NOT authenticated');
        }
        
        // Check if we already have customer email
        let hasCustomerEmail = !!(customerData.value && customerData.value.email);
        
        // Try to restore customer data from localStorage first
        if (!hasCustomerEmail) {
          const savedCustomerData = localStorage.getItem('checkoutCustomerData');
          if (savedCustomerData) {
            try {
              const parsedCustomerData = JSON.parse(savedCustomerData);
              console.log('Restoring customer data from localStorage:', parsedCustomerData);
              customerData.value = {
                email: parsedCustomerData.email || '',
                first_name: parsedCustomerData.first_name || '',
                last_name: parsedCustomerData.last_name || '',
                phone: parsedCustomerData.phone || '',
                has_account: isAuthenticated.value
              };
              hasCustomerEmail = !!parsedCustomerData.email;
            } catch (e) {
              console.error('Error parsing saved customer data:', e);
            }
          }
        }
        
        // If no customer data from localStorage or authentication, initialize from cart if available
        if (!hasCustomerEmail && cartStore.cart?.email) {
          console.log('Initializing customer data from cart...');
          customerData.value = {
            email: cartStore.cart.email,
            first_name: cartStore.cart.shipping_address?.first_name || '',
            last_name: cartStore.cart.shipping_address?.last_name || '',
            phone: cartStore.cart.shipping_address?.phone || '',
            has_account: isAuthenticated.value
          };
          
          // Also update shipping address name fields from cart if not already set
          if (cartStore.cart.shipping_address) {
            shippingAddress.value.first_name = shippingAddress.value.first_name || cartStore.cart.shipping_address.first_name || '';
            shippingAddress.value.last_name = shippingAddress.value.last_name || cartStore.cart.shipping_address.last_name || '';
            shippingAddress.value.phone = shippingAddress.value.phone || cartStore.cart.shipping_address.phone || '';
          }
        }
        
        // Log final customer data for debugging
        console.log('Final customer data initialization:', {
          email: customerData.value.email,
          hasAccount: customerData.value.has_account,
          isAuthenticated: isAuthenticated.value
        });
        
      } catch (error) {
        console.error('Error initializing cart:', error)
      } finally {
        loading.value = false
      }
    }
  })

  // Helper function to get product title - similar to CartItem and ReviewOrder components
  const getProductTitle = (item: any): string => {
    // Check for product_title first (highest priority)
    if (item.product_title) {
      return item.product_title;
    }
    
    // Check for variant's product title (second priority)
    if (item.variant?.product?.title) {
      return item.variant.product.title;
    }
    
    // Check for title directly on item (third priority)
    if (item.title) {
      return item.title;
    }
    
    // Check for variant title as fallback (fourth priority)
    if (item.variant?.title) {
      return item.variant.title;
    }
    
    // Check for description as fallback (fifth priority)
    if (item.description) {
      return item.description;
    }
    
    // Default fallback
    return 'Product';
  }

  // Define main handleShippingAddressSubmit function
  const handleShippingAddressSubmit = async () => {
    stepLoading.value = true
    try {
      // Batch all customer and address updates for better performance
      const updates = []

      // Only ensure customer association if needed
      if (isAuthenticated.value && !cartStore.cart?.customer_id) {
        console.log('Ensuring customer association before updating addresses...')
        const config = useRuntimeConfig()
        updates.push($fetch(`/store/carts/${cartId.value}/customer`, {
          method: 'POST',
          baseURL: config.public.cloudflareApiUrl || 'https://bapi.handmadein.ro',
          body: {}
        }))
      }

      // Only update email if it's different and needed
      if (customerData.value.email && (!cartStore.cart?.email || cartStore.cart.email !== customerData.value.email)) {
        console.log('Updating cart email during shipping address submission:', customerData.value.email)
        // Use direct API call instead of cart store for better performance
        updates.push(medusaService.updateCart(cartId.value, {
          email: customerData.value.email
        }))
      }

      // Handle addresses with metadata preservation
      updates.push(updateCartState(
        cartId.value, 
        shippingAddress.value, 
        billingAddress.value, 
        sameAsBilling.value
      ))

      // Execute all updates in parallel for better performance
      await Promise.all(updates)

      // Only update shipping options, skip cart refresh for now to improve performance
      await updateShippingOptions()

      // Move to next step
      if (currentStep.value < steps.length - 1) {
        scrollToCheckoutSteps()
        currentStep.value++
      }
    } catch (error: any) {
      console.error('Error handling shipping address submission:', error)
      let errorMessage = error.message || t('checkout.address_update_error')
      
      if (errorMessage.includes('authentication')) {
        errorMessage = t('checkout.authentication_error')
      } else if (errorMessage.includes('email')) {
        errorMessage = t('checkout.email_required')
      }
      
      toast.add({
        title: t('checkout.error'),
        description: errorMessage,
        color: 'red'
      })
    } finally {
      stepLoading.value = false
    }
  }

  // Add shipping options handling
  const updateShippingOptions = async () => {
    try {
      if (!cartId.value) {
        console.error('No cart ID available')
        return
      }

      // Get shipping options from the backend
      const options = await medusaService.getShippingOptions(cartId.value)
      if (options && Array.isArray(options)) {
        shippingOptions.value = options

        // Clear selected option if it no longer exists in the new options
        if (selectedShippingOption.value) {
          const stillExists = options.some(opt => opt.id === selectedShippingOption.value)
          if (!stillExists) {
            selectedShippingOption.value = null
          }
        }

        console.log('Updated shipping options:', options)
      } else {
        console.error('Unexpected shipping options format:', options)
        // Set empty array instead of throwing to prevent loading state from getting stuck
        shippingOptions.value = []
      }
    } catch (err) {
      console.error('Error fetching shipping options:', err)
      // Don't throw error - just set empty array to prevent loading state from getting stuck
      shippingOptions.value = []
      
      // Show toast notification to user
      toast.add({
        title: t('checkout.error'),
        description: t('checkout.shipping_options_error') || 'Failed to load shipping options',
        color: 'red'
      })
    }
  }

  // Handle shipping method selection
  const handleSelectShipping = async (optionId: string) => {
    try {
      selectedShippingOption.value = optionId
    
      if (cartId.value) {
        await medusaService.addShippingMethod(cartId.value, optionId)
        // Removed cart refresh for better performance - will refresh at step transition
        console.log('Shipping method updated:', optionId)
      }
    } catch (err) {
      console.error('Failed to update shipping method:', err)
      throw err
    }
  }

  // Handle shipping method submission
  const handleShippingMethodSubmit = async () => {
    stepLoading.value = true
    try {
      if (!selectedShippingOption.value) {
        throw new Error(t('checkout.select_shipping_method'))
      }

      // Apply shipping method without unnecessary additional calls
      await handleSelectShipping(selectedShippingOption.value)
      
      // Move to next step immediately for better performance
      if (currentStep.value < steps.length - 1) {
        scrollToCheckoutSteps()
        currentStep.value++
      }
    } catch (err) {
      console.error('Error submitting shipping method:', err)
      toast.add({
        title: t('checkout.error'),
        description: err instanceof Error ? err.message : t('checkout.shipping_method_error'),
        color: 'red'
      })
    } finally {
      stepLoading.value = false
    }
  }

  // Handle payment selection
  const handleSelectPayment = (providerId: string) => {
    selectedPaymentOption.value = providerId
  }

  // Handle payment readiness
  const handlePaymentReady = () => {
    paymentReady.value = true
  }

  // Handle payment method submission
  const handlePaymentMethodSubmit = async () => {
    stepLoading.value = true
    try {
      if (!selectedPaymentOption.value) {
        throw new Error(t('checkout.select_payment_method'))
      }

      // Select payment provider efficiently
      await medusaService.selectPaymentProvider(cartId.value, selectedPaymentOption.value)
      
      // Move to next step immediately for better performance
      if (currentStep.value < steps.length - 1) {
        scrollToCheckoutSteps()
        currentStep.value++
      }
    } catch (err) {
      console.error('Error submitting payment method:', err)
      toast.add({
        title: t('checkout.error'),
        description: err instanceof Error ? err.message : t('checkout.payment_method_error'),
        color: 'red'
      })
    } finally {
      stepLoading.value = false
    }
  }

  // Handle payment completion
  const handlePaymentComplete = async (data: any) => {
    paymentData.value = data
    await goToNextStep()
  }

  // Handle discount code application
  const handleApplyDiscount = async () => {
    if (!discountCode.value.trim()) {
      return
    }

    applyingDiscount.value = true
    discountError.value = ''
    discountSuccess.value = ''

    try {
      // Apply discount code to cart
      await medusaService.updateCart(cartId.value, {
        discounts: [{ code: discountCode.value.trim() }]
      })

      // Only refresh cart data for discount - no full refresh needed
      // await cartStore.refreshCart() // Removed for performance
      
      discountSuccess.value = t('checkout.discount_applied')
      discountCode.value = ''
      
      toast.add({
        title: t('checkout.success'),
        description: t('checkout.discount_applied'),
        color: 'green'
      })
    } catch (error: any) {
      discountError.value = error.message || t('checkout.discount_error')
      toast.add({
        title: t('checkout.error'),
        description: discountError.value,
        color: 'red'
      })
    } finally {
      applyingDiscount.value = false
    }
  }

  // Handle place order - this was missing!
  const handlePlaceOrder = async () => {
    stepLoading.value = true
    try {
      // Ensure customer email is set on the cart before completing
      if (customerData.value.email && cartId.value) {
        console.log('Ensuring customer email is set before order completion:', customerData.value.email)
        
        // Update cart with customer email using cart store method for better persistence
        const emailUpdateSuccess = await cartStore.updateCartEmail(customerData.value.email)
        
        if (!emailUpdateSuccess) {
          console.warn('Failed to update email using cart store, trying direct API call...')
          await medusaService.updateCart(cartId.value, {
            email: customerData.value.email
          })
          await cartStore.refreshCart()
        }
        
        // Verify email was saved
        if (cartStore.cart && cartStore.cart.email !== customerData.value.email) {
          console.warn('Email not properly saved to cart, trying one more time...')
          await medusaService.updateCart(cartId.value, {
            email: customerData.value.email
          })
          await cartStore.refreshCart()
        }
        
        // Final verification
        if (cartStore.cart && cartStore.cart.email !== customerData.value.email) {
          console.error('CRITICAL: Email could not be persisted to cart before order completion!')
          throw new Error('Failed to save customer email. Please try again.')
        } else {
          console.log('✓ Email successfully persisted in cart:', cartStore.cart?.email)
        }
      }

      // Complete the checkout
      console.log('Completing checkout for cart:', cartId.value)
      const orderResult = await medusaService.completeCheckout(cartId.value)
      
      console.log('[DEBUG] Order completion result:', JSON.stringify(orderResult, null, 2))
      
      if (orderResult && orderResult.type === 'order') {
        // Try multiple paths to extract order ID based on actual completeCheckout response format
        let extractedOrderId = null;
        
        // Method 1: orderResult.data?.id (completeCheckout returns { type: 'order', data: order })
        if (orderResult.data?.id) {
          extractedOrderId = orderResult.data.id;
        }
        // Method 2: orderResult.data?.order?.id (fallback)
        else if (orderResult.data?.order?.id) {
          extractedOrderId = orderResult.data.order.id;
        }
        // Method 3: orderResult.id (top level ID)
        else if (orderResult.id) {
          extractedOrderId = orderResult.id;
        }
        
        console.log('[DEBUG] Extracted order ID:', extractedOrderId)
        
        orderId.value = extractedOrderId
        lastOrderDetails.value = orderResult.data?.order || orderResult.data || null
        
        // Clear the old cart and create a fresh one for continued shopping
        console.log('[DEBUG] Clearing cart and creating new one after successful order')
        await cartStore.resetCart()
        
        // Redirect to order confirmation page instead of staying on checkout
        if (orderId.value) {
          console.log('Redirecting to order confirmation page:', orderId.value)
          await navigateTo(`/checkout/success?orderId=${orderId.value}`)
        } else {
          console.error('[ERROR] No order ID found in result, using fallback redirect')
          await navigateTo('/checkout/success')
        }
        
        toast.add({
          title: t('checkout.order_success'),
          description: t('checkout.order_success_description'),
          color: 'green'
        })
      } else {
        throw new Error('Order completion failed')
      }
    } catch (error: any) {
      console.error('Error placing order:', error)
      toast.add({
        title: t('checkout.order_error'),
        description: error.message || t('checkout.order_error_description'),
        color: 'red'
      })
    } finally {
      stepLoading.value = false
    }
  }

  // Handle order details viewing
  const viewOrderDetails = () => {
    if (orderId.value) {
      router.push(`/orders/${orderId.value}`)
    }
  }
  </script>

<style scoped>
.checkout-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
}

.checkout-item-image {
  flex: 0 0 60px;
  width: 60px;
  height: 60px;
  min-width: 60px;
  overflow: hidden;
  border-radius: 6px;
  background-color: #f9f9f9;
}

.checkout-item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.checkout-item-details {
  flex: 1;
  min-width: 0;
  overflow: hidden;
}

.checkout-item-title {
  font-weight: 500;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.checkout-item-variant {
  font-size: 12px;
  color: #6b7280;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.checkout-item-quantity {
  font-size: 12px;
  color: #6b7280;
}

.checkout-item-price {
  flex: 0 0 auto;
  font-weight: 500;
  text-align: right;
  white-space: nowrap;
}
</style>



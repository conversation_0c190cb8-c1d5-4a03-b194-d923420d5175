import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { Context } from 'hono';
import <PERSON><PERSON> from 'stripe';
import { CacheService } from '../../services/cache';
import { cacheMiddleware, cacheKeyGenerators } from '../../middleware/cache';

type Bindings = {
  DB: D1Database;
  JWT_SECRET: string;
  FRONTEND_URL: string;
  ADMIN_URL: string;
  STRIPE_ENVIRONMENT: string;
  STRIPE_SECRET_KEY_TEST: string;
  STRIPE_SECRET_KEY_LIVE: string;
  STRIPE_WEBHOOK_SECRET: string;
  CACHE: KVNamespace;
  SESSIONS: KVNamespace;
};

const app = new Hono<{ Bindings: Bindings }>();

// CORS middleware
app.use('/*', cors({
  origin: (origin) => {
    const allowedOrigins = [
      'http://localhost:3000',
      'http://localhost:3001', 
      'http://localhost:3002',
      'https://handmadein.ro',
      'https://www.handmadein.ro',
      'https://admin.handmadein.ro',
      'https://staging.handmadein.ro',
      'https://admin-staging.handmadein.ro'
    ];
    return allowedOrigins.includes(origin) || origin?.endsWith('.handmadein.ro') ? origin : null;
  },
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Accept', 'Accept-Language', 'X-Locale', 'X-Session-ID'],
  credentials: true,
}));

// Utility functions
function handleError(c: Context, error: any, message = 'Internal server error') {
  console.error('Payment error:', error);
  return c.json({ success: false, error: message, details: error.message }, 500);
}

function safeParseJson(value: any): any {
  if (!value) return null;
  if (typeof value === 'string') {
    try {
      return JSON.parse(value);
    } catch {
      return value;
    }
  }
  return value;
}

// Initialize Stripe with the appropriate key based on environment
function getStripeInstance(c: Context): Stripe {
  const isTestMode = c.env.STRIPE_ENVIRONMENT === 'test';
  const secretKey = isTestMode ? c.env.STRIPE_SECRET_KEY_TEST : c.env.STRIPE_SECRET_KEY_LIVE;
  
  return new Stripe(secretKey, {
    apiVersion: '2023-10-16',
  });
}

// Get or create cart
async function getCart(cartId: string, db: D1Database) {
  const cartQuery = `SELECT * FROM carts WHERE id = ?`;
  const cart = await db.prepare(cartQuery).bind(cartId).first();
  
  if (!cart) {
    throw new Error('Cart not found');
  }
  
  return cart;
}

// Calculate cart total
async function calculateCartTotal(cartId: string, db: D1Database): Promise<number> {
  // Get cart items total
  const itemsQuery = `
    SELECT SUM(quantity * unit_price) as items_total 
    FROM cart_items 
    WHERE cart_id = ?
  `;
  const itemsResult = await db.prepare(itemsQuery).bind(cartId).first();
  const itemsTotal = itemsResult?.items_total || 0;
  
  // Get cart for shipping and other costs
  const cart = await getCart(cartId, db);
  const metadata = safeParseJson(cart.metadata) || {};
  
  const shippingTotal = metadata.shipping_method?.price || 0;
  const taxTotal = 0; // TODO: Implement tax calculation
  const discountTotal = metadata.discount_total || 0;
    return Math.max(0, itemsTotal + shippingTotal + taxTotal - discountTotal);
}

// Get available payment methods (cached)
app.get('/methods', cacheMiddleware({
  ttl: 1800, // 30 minutes
  keyGenerator: cacheKeyGenerators.paymentMethods
}), async (c) => {
  try {
    // Try to get from KV cache directly
    let paymentMethods: any[] = await c.env.CACHE.get('payment_methods:all', { type: 'json' }) || [];
    
    if (paymentMethods.length === 0) {
      console.log('Payment methods cache miss, fetching from database/configuration');
      
      // Build payment methods dynamically
      paymentMethods = [];
      
      // Always include Cash on Delivery
      paymentMethods.push({
        id: 'cash_on_delivery',
        name: 'Plata ramburs',
        description: 'Plătiți prin ramburs la livrare',
        type: 'manual',
        enabled: true,
        metadata: {
          instructions: 'Veți plăti curierului la primirea comenzii.'
        }
      });
      
      // Check if Stripe is configured and add credit card option
      const isTestMode = c.env.STRIPE_ENVIRONMENT === 'test';
      const secretKey = isTestMode ? c.env.STRIPE_SECRET_KEY_TEST : c.env.STRIPE_SECRET_KEY_LIVE;
      
      if (secretKey && secretKey !== 'your-stripe-secret-key') {
        paymentMethods.push({
          id: 'stripe',
          name: 'Card bancar',
          description: 'Plată securizată cu cardul bancar',
          type: 'card',
          enabled: true,
          metadata: {
            accepts: ['visa', 'mastercard'],
            test_mode: isTestMode
          }
        });
      }
      
      // Add manual/wire transfer option
      paymentMethods.push({
        id: 'bank_transfer',
        name: 'Transfer bancar',
        description: 'Transfer manual în contul bancar',
        type: 'manual',
        enabled: true,
        metadata: {
          instructions: 'Veți primi detaliile de plată prin email după confirmarea comenzii.'
        }
      });
      
      // Cache the payment methods for 30 minutes
      await c.env.CACHE.put('payment_methods:all', JSON.stringify(paymentMethods), {
        expirationTtl: 1800
      });
    }
    
    return c.json({
      success: true,
      payment_methods: paymentMethods
    });
    
  } catch (error) {
    console.error('Error fetching payment methods:', error);
    return handleError(c, error, 'Failed to fetch payment methods');
  }
});

// Initialize payment sessions for a cart
app.post('/:cartId/payment-sessions', async (c) => {
  try {
    const cartId = c.req.param('cartId');
    const db = c.env.DB;
    const stripe = getStripeInstance(c);
    
    console.log(`Initializing payment sessions for cart: ${cartId}`);
      // Check cache for cart totals first to avoid recalculation
    const cacheKey = `cart_totals:${cartId}`;
    let cartTotal: number = await c.env.CACHE.get(cacheKey, { type: 'json' }) as number || 0;
    
    if (!cartTotal || cartTotal <= 0) {
      // Get cart and validate
      const cart = await getCart(cartId, db);
      cartTotal = await calculateCartTotal(cartId, db);
      
      // Cache cart total for 5 minutes (short because cart can change frequently)
      await c.env.CACHE.put(cacheKey, JSON.stringify(cartTotal), {
        expirationTtl: 300
      });
    }
    
    if (cartTotal <= 0) {
      return c.json({
        success: false,
        error: 'Cart total must be greater than 0 to initialize payment'
      }, 400);
    }
    
    // Convert to cents for Stripe (assuming RON currency)
    const amountInCents = Math.round(cartTotal * 100);
    
    console.log(`Cart total: ${cartTotal} RON (${amountInCents} cents)`);
    
    // Check if payment sessions already exist for this cart
    const existingSessionsQuery = `SELECT * FROM payment_sessions WHERE cart_id = ? ORDER BY created_at ASC`;
    const existingSessions = await db.prepare(existingSessionsQuery).bind(cartId).all();
    
    // If sessions exist and are recent (less than 10 minutes old), return them
    if (existingSessions.results && existingSessions.results.length > 0) {
      const latestSession = existingSessions.results[0] as any;
      const sessionAge = Date.now() - new Date(latestSession.created_at).getTime();
      
      if (sessionAge < 10 * 60 * 1000) { // 10 minutes
        console.log(`Returning existing payment sessions for cart ${cartId}`);
        
        const formattedSessions = (existingSessions.results || []).map((session: any) => ({
          ...session,
          data: safeParseJson(session.data) || {},
          is_selected: session.is_selected === 1 || session.is_selected === '1' || session.is_selected === true,
          is_initiated: session.is_initiated === 1 || session.is_initiated === '1' || session.is_initiated === true
        }));
        
        return c.json({
          success: true,
          payment_sessions: formattedSessions,
          cached: true
        });
      }
    }
    
    // Create Stripe PaymentIntent
    const paymentIntent = await stripe.paymentIntents.create({
      amount: amountInCents,
      currency: 'ron',
      capture_method: 'manual', // Authorize funds but don't capture immediately
      automatic_payment_methods: {
        enabled: true,
      },
      metadata: {
        cart_id: cartId,
        order_source: 'handmadein_ro'
      }
    });
    
    console.log(`Created Stripe PaymentIntent: ${paymentIntent.id}`);
    
    // Delete existing payment sessions for this cart
    await db.prepare(`DELETE FROM payment_sessions WHERE cart_id = ?`).bind(cartId).run();
    
    // Create Stripe payment session
    const stripeSessionData = {
      client_secret: paymentIntent.client_secret,
      payment_intent_id: paymentIntent.id,
      amount: amountInCents,
      currency: 'ron'
    };
    
    const insertStripeSession = db.prepare(`
      INSERT INTO payment_sessions (id, cart_id, provider_id, amount, currency, status, data, is_selected, is_initiated, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))
    `);
    
    await insertStripeSession.bind(
      `ps_stripe_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      cartId,
      'stripe',
      amountInCents,
      'ron',
      'pending',
      JSON.stringify(stripeSessionData),
      false, // not selected by default
      true   // is initiated
    ).run();
    
    // Create manual payment session as fallback
    const manualSessionData = {
      payment_method: 'manual',
      instructions: 'Plata va fi procesată manual după confirmarea comenzii.'
    };
    
    const insertManualSession = db.prepare(`
      INSERT INTO payment_sessions (id, cart_id, provider_id, amount, currency, status, data, is_selected, is_initiated, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))
    `);
    
    await insertManualSession.bind(
      `ps_manual_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      cartId,
      'manual',
      amountInCents,
      'ron',
      'pending',
      JSON.stringify(manualSessionData),
      false, // not selected by default
      true   // is initiated
    ).run();
    
    // Get all payment sessions for response
    const sessionsQuery = `SELECT * FROM payment_sessions WHERE cart_id = ? ORDER BY created_at ASC`;
    const sessions = await db.prepare(sessionsQuery).bind(cartId).all();
    
    const formattedSessions = (sessions.results || []).map((session: any) => ({
      ...session,
      data: safeParseJson(session.data) || {},
      is_selected: session.is_selected === 1 || session.is_selected === '1' || session.is_selected === true,
      is_initiated: session.is_initiated === 1 || session.is_initiated === '1' || session.is_initiated === true
    }));
    
    console.log(`Created ${formattedSessions.length} payment sessions for cart ${cartId}`);
    
    return c.json({
      success: true,
      payment_sessions: formattedSessions
    });
    
  } catch (error) {
    console.error('Error initializing payment sessions:', error);
    return handleError(c, error, 'Failed to initialize payment sessions');
  }
});

// Select a payment provider for the cart
app.post('/:cartId/payment-sessions/:sessionId/select', async (c) => {
  try {
    const cartId = c.req.param('cartId');
    const sessionId = c.req.param('sessionId');
    const db = c.env.DB;
    
    console.log(`Selecting payment session ${sessionId} for cart ${cartId}`);
    
    // Validate that the payment session exists for this cart
    const sessionQuery = `SELECT * FROM payment_sessions WHERE id = ? AND cart_id = ?`;
    const session = await db.prepare(sessionQuery).bind(sessionId, cartId).first();
    
    if (!session) {
      return c.json({
        success: false,
        error: 'Payment session not found'
      }, 404);
    }
    
    // Deselect all other payment sessions for this cart
    await db.prepare(`UPDATE payment_sessions SET is_selected = 0 WHERE cart_id = ?`).bind(cartId).run();
    
    // Select the specified payment session
    await db.prepare(`UPDATE payment_sessions SET is_selected = 1, updated_at = datetime('now') WHERE id = ?`).bind(sessionId).run();
    
    console.log(`Successfully selected payment session ${sessionId}`);
    
    // Get updated session data
    const updatedSession = await db.prepare(sessionQuery).bind(sessionId, cartId).first();
    
    if (!updatedSession) {
      return c.json({
        success: false,
        error: 'Payment session not found after update'
      }, 404);
    }
    
    const formattedSession = {
      ...updatedSession,
      data: safeParseJson(updatedSession.data) || {},
      is_selected: true,
      is_initiated: updatedSession.is_initiated === 1 || updatedSession.is_initiated === '1' || updatedSession.is_initiated === true
    };
    
    return c.json({
      success: true,
      payment_session: formattedSession
    });
    
  } catch (error) {
    console.error('Error selecting payment session:', error);
    return handleError(c, error, 'Failed to select payment session');
  }
});

// Get payment sessions for a cart
app.get('/:cartId/payment-sessions', async (c) => {
  try {
    const cartId = c.req.param('cartId');
    const db = c.env.DB;
    
    console.log(`Getting payment sessions for cart: ${cartId}`);
    
    // Verify cart exists
    await getCart(cartId, db);
    
    // Get payment sessions
    const sessionsQuery = `SELECT * FROM payment_sessions WHERE cart_id = ? ORDER BY created_at ASC`;
    const sessions = await db.prepare(sessionsQuery).bind(cartId).all();
    
    const formattedSessions = (sessions.results || []).map((session: any) => ({
      ...session,
      data: safeParseJson(session.data) || {},
      is_selected: session.is_selected === 1 || session.is_selected === '1' || session.is_selected === true,
      is_initiated: session.is_initiated === 1 || session.is_initiated === '1' || session.is_initiated === true
    }));
    
    return c.json({
      success: true,
      payment_sessions: formattedSessions
    });
    
  } catch (error) {
    console.error('Error getting payment sessions:', error);
    return handleError(c, error, 'Failed to get payment sessions');
  }
});

// Refresh/update Stripe payment session (useful for client_secret updates)
app.post('/:cartId/payment-sessions/:sessionId/refresh', async (c) => {
  try {
    const cartId = c.req.param('cartId');
    const sessionId = c.req.param('sessionId');
    const db = c.env.DB;
    const stripe = getStripeInstance(c);
    
    console.log(`Refreshing payment session ${sessionId} for cart ${cartId}`);
    
    // Get the payment session
    const sessionQuery = `SELECT * FROM payment_sessions WHERE id = ? AND cart_id = ? AND provider_id = 'stripe'`;
    const session = await db.prepare(sessionQuery).bind(sessionId, cartId).first();
    
    if (!session) {
      return c.json({
        success: false,
        error: 'Stripe payment session not found'
      }, 404);
    }
    
    const sessionData = safeParseJson(session.data) || {};
    
    // Get current cart total
    const cartTotal = await calculateCartTotal(cartId, db);
    const amountInCents = Math.round(cartTotal * 100);
    
    // Update the PaymentIntent amount if it has changed
    if (sessionData.payment_intent_id && sessionData.amount !== amountInCents) {
      console.log(`Updating PaymentIntent amount from ${sessionData.amount} to ${amountInCents} cents`);
      
      const paymentIntent = await stripe.paymentIntents.update(sessionData.payment_intent_id, {
        amount: amountInCents,
      });
      
      // Update session data
      const updatedSessionData = {
        ...sessionData,
        amount: amountInCents,
        client_secret: paymentIntent.client_secret
      };
      
      // Update in database
      await db.prepare(`
        UPDATE payment_sessions 
        SET data = ?, amount = ?, updated_at = datetime('now') 
        WHERE id = ?
      `).bind(JSON.stringify(updatedSessionData), amountInCents, sessionId).run();
      
      console.log(`Updated PaymentIntent ${sessionData.payment_intent_id} with new amount`);
      
      return c.json({
        success: true,
        payment_session: {
          ...session,
          data: updatedSessionData,
          amount: amountInCents,
          is_selected: session.is_selected === 1 || session.is_selected === '1' || session.is_selected === true,
          is_initiated: session.is_initiated === 1 || session.is_initiated === '1' || session.is_initiated === true
        }
      });
    }
    
    // No update needed
    return c.json({
      success: true,
      payment_session: {
        ...session,
        data: sessionData,
        is_selected: session.is_selected === 1 || session.is_selected === '1' || session.is_selected === true,
        is_initiated: session.is_initiated === 1 || session.is_initiated === '1' || session.is_initiated === true
      }
    });
    
  } catch (error) {
    console.error('Error refreshing payment session:', error);
    return handleError(c, error, 'Failed to refresh payment session');
  }
});

export { app as paymentRoutes }; 
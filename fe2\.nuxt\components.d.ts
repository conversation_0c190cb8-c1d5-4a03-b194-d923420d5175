
import type { DefineComponent, SlotsType } from 'vue'
type IslandComponent<T extends DefineComponent> = T & DefineComponent<{}, {refresh: () => Promise<void>}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, SlotsType<{ fallback: { error: unknown } }>>
type HydrationStrategies = {
  hydrateOnVisible?: IntersectionObserverInit | true
  hydrateOnIdle?: number | true
  hydrateOnInteraction?: keyof HTMLElementEventMap | Array<keyof HTMLElementEventMap> | true
  hydrateOnMediaQuery?: string
  hydrateAfter?: number
  hydrateWhen?: boolean
  hydrateNever?: true
}
type LazyComponent<T> = (T & DefineComponent<HydrationStrategies, {}, {}, {}, {}, {}, {}, { hydrated: () => void }>)
interface _GlobalComponents {
      'AISearchModal': typeof import("../components/AISearchModal.vue")['default']
    'AccountHeader': typeof import("../components/Account/AccountHeader.vue")['default']
    'AccountSettings': typeof import("../components/Account/AccountSettings.vue")['default']
    'AccountAddressManagement': typeof import("../components/Account/AddressManagement.vue")['default']
    'AccountOrderHistory': typeof import("../components/Account/OrderHistory.vue")['default']
    'AccountAddresses': typeof import("../components/AccountAddresses.vue")['default']
    'AddressTypeDisplay': typeof import("../components/AddressTypeDisplay.vue")['default']
    'BillingAddress': typeof import("../components/BillingAddress.vue")['default']
    'BillingAddressToggle': typeof import("../components/BillingAddressToggle.vue")['default']
    'Breadcrumb': typeof import("../components/Breadcrumb.vue")['default']
    'CartAddedPopup': typeof import("../components/CartAddedPopup.vue")['default']
    'CartItem': typeof import("../components/CartItem.vue")['default']
    'CartProvider': typeof import("../components/CartProvider.vue")['default']
    'CategoryHero': typeof import("../components/CategoryHero.vue")['default']
    'CheckoutVerify': typeof import("../components/CheckoutVerify.vue")['default']
    'CollectionHighlights': typeof import("../components/CollectionHighlights.vue")['default']
    'CookieConsent': typeof import("../components/CookieConsent.vue")['default']
    'CustomerInformation': typeof import("../components/CustomerInformation.vue")['default']
    'EasyboxLockerSelector': typeof import("../components/EasyboxLockerSelector.vue")['default']
    'EmptyState': typeof import("../components/EmptyState.vue")['default']
    'ErrorBoundary': typeof import("../components/ErrorBoundary.vue")['default']
    'HeroSection': typeof import("../components/HeroSection.vue")['default']
    'LoadingSkeleton': typeof import("../components/LoadingSkeleton.vue")['default']
    'LoadingSpinner': typeof import("../components/LoadingSpinner.vue")['default']
    'LocaleSelector': typeof import("../components/LocaleSelector.vue")['default']
    'LocaleSwitcher': typeof import("../components/LocaleSwitcher.vue")['default']
    'MobileMenu': typeof import("../components/MobileMenu.vue")['default']
    'PageHero': typeof import("../components/PageHero.vue")['default']
    'PageTransition': typeof import("../components/PageTransition.vue")['default']
    'Pagination': typeof import("../components/Pagination.vue")['default']
    'PaymentMethod': typeof import("../components/PaymentMethod.vue")['default']
    'PaymentProviderSelector': typeof import("../components/PaymentProviderSelector.vue")['default']
    'ProductCard': typeof import("../components/ProductCard.vue")['default']
    'ProductGrid': typeof import("../components/ProductGrid.vue")['default']
    'ProductQuickView': typeof import("../components/ProductQuickView.vue")['default']
    'ProductRecommendations': typeof import("../components/ProductRecommendations.vue")['default']
    'ProductReviews': typeof import("../components/ProductReviews.vue")['default']
    'RatingsDebugger': typeof import("../components/RatingsDebugger.vue")['default']
    'RecentlyViewed': typeof import("../components/RecentlyViewed.vue")['default']
    'ReviewOrder': typeof import("../components/ReviewOrder.vue")['default']
    'SearchModal': typeof import("../components/SearchModal.vue")['default']
    'ShippingAddress': typeof import("../components/ShippingAddress.vue")['default']
    'ShippingMethod': typeof import("../components/ShippingMethod.vue")['default']
    'StarRating': typeof import("../components/StarRating.vue")['default']
    'StripePaymentElement': typeof import("../components/StripePaymentElement.vue")['default']
    'StripePaymentForm': typeof import("../components/StripePaymentForm.vue")['default']
    'TheFooter': typeof import("../components/TheFooter.vue")['default']
    'TheHeader': typeof import("../components/TheHeader.vue")['default']
    'ThematicTopBar': typeof import("../components/ThematicTopBar.vue")['default']
    'ToastNotification': typeof import("../components/ToastNotification.vue")['default']
    'TrustedShop': typeof import("../components/TrustedShop.vue")['default']
    'NuxtWelcome': typeof import("../node_modules/nuxt/dist/app/components/welcome.vue")['default']
    'NuxtLayout': typeof import("../node_modules/nuxt/dist/app/components/nuxt-layout")['default']
    'NuxtErrorBoundary': typeof import("../node_modules/nuxt/dist/app/components/nuxt-error-boundary.vue")['default']
    'ClientOnly': typeof import("../node_modules/nuxt/dist/app/components/client-only")['default']
    'DevOnly': typeof import("../node_modules/nuxt/dist/app/components/dev-only")['default']
    'ServerPlaceholder': typeof import("../node_modules/nuxt/dist/app/components/server-placeholder")['default']
    'NuxtLink': typeof import("../node_modules/nuxt/dist/app/components/nuxt-link")['default']
    'NuxtLoadingIndicator': typeof import("../node_modules/nuxt/dist/app/components/nuxt-loading-indicator")['default']
    'NuxtTime': typeof import("../node_modules/nuxt/dist/app/components/nuxt-time.vue")['default']
    'NuxtRouteAnnouncer': typeof import("../node_modules/nuxt/dist/app/components/nuxt-route-announcer")['default']
    'NuxtImg': typeof import("../node_modules/@nuxt/image/dist/runtime/components/NuxtImg.vue")['default']
    'NuxtPicture': typeof import("../node_modules/@nuxt/image/dist/runtime/components/NuxtPicture.vue")['default']
    'Icon': typeof import("../node_modules/@nuxt/icon/dist/runtime/components/index")['default']
    'NuxtLinkLocale': typeof import("../node_modules/@nuxtjs/i18n/dist/runtime/components/NuxtLinkLocale")['default']
    'SwitchLocalePathLink': typeof import("../node_modules/@nuxtjs/i18n/dist/runtime/components/SwitchLocalePathLink")['default']
    'NuxtPage': typeof import("../node_modules/nuxt/dist/pages/runtime/page")['default']
    'NoScript': typeof import("../node_modules/nuxt/dist/head/runtime/components")['NoScript']
    'Link': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Link']
    'Base': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Base']
    'Title': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Title']
    'Meta': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Meta']
    'Style': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Style']
    'Head': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Head']
    'Html': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Html']
    'Body': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Body']
    'NuxtIsland': typeof import("../node_modules/nuxt/dist/app/components/nuxt-island")['default']
    'NuxtRouteAnnouncer': IslandComponent<typeof import("../node_modules/nuxt/dist/app/components/server-placeholder")['default']>
      'LazyAISearchModal': LazyComponent<typeof import("../components/AISearchModal.vue")['default']>
    'LazyAccountHeader': LazyComponent<typeof import("../components/Account/AccountHeader.vue")['default']>
    'LazyAccountSettings': LazyComponent<typeof import("../components/Account/AccountSettings.vue")['default']>
    'LazyAccountAddressManagement': LazyComponent<typeof import("../components/Account/AddressManagement.vue")['default']>
    'LazyAccountOrderHistory': LazyComponent<typeof import("../components/Account/OrderHistory.vue")['default']>
    'LazyAccountAddresses': LazyComponent<typeof import("../components/AccountAddresses.vue")['default']>
    'LazyAddressTypeDisplay': LazyComponent<typeof import("../components/AddressTypeDisplay.vue")['default']>
    'LazyBillingAddress': LazyComponent<typeof import("../components/BillingAddress.vue")['default']>
    'LazyBillingAddressToggle': LazyComponent<typeof import("../components/BillingAddressToggle.vue")['default']>
    'LazyBreadcrumb': LazyComponent<typeof import("../components/Breadcrumb.vue")['default']>
    'LazyCartAddedPopup': LazyComponent<typeof import("../components/CartAddedPopup.vue")['default']>
    'LazyCartItem': LazyComponent<typeof import("../components/CartItem.vue")['default']>
    'LazyCartProvider': LazyComponent<typeof import("../components/CartProvider.vue")['default']>
    'LazyCategoryHero': LazyComponent<typeof import("../components/CategoryHero.vue")['default']>
    'LazyCheckoutVerify': LazyComponent<typeof import("../components/CheckoutVerify.vue")['default']>
    'LazyCollectionHighlights': LazyComponent<typeof import("../components/CollectionHighlights.vue")['default']>
    'LazyCookieConsent': LazyComponent<typeof import("../components/CookieConsent.vue")['default']>
    'LazyCustomerInformation': LazyComponent<typeof import("../components/CustomerInformation.vue")['default']>
    'LazyEasyboxLockerSelector': LazyComponent<typeof import("../components/EasyboxLockerSelector.vue")['default']>
    'LazyEmptyState': LazyComponent<typeof import("../components/EmptyState.vue")['default']>
    'LazyErrorBoundary': LazyComponent<typeof import("../components/ErrorBoundary.vue")['default']>
    'LazyHeroSection': LazyComponent<typeof import("../components/HeroSection.vue")['default']>
    'LazyLoadingSkeleton': LazyComponent<typeof import("../components/LoadingSkeleton.vue")['default']>
    'LazyLoadingSpinner': LazyComponent<typeof import("../components/LoadingSpinner.vue")['default']>
    'LazyLocaleSelector': LazyComponent<typeof import("../components/LocaleSelector.vue")['default']>
    'LazyLocaleSwitcher': LazyComponent<typeof import("../components/LocaleSwitcher.vue")['default']>
    'LazyMobileMenu': LazyComponent<typeof import("../components/MobileMenu.vue")['default']>
    'LazyPageHero': LazyComponent<typeof import("../components/PageHero.vue")['default']>
    'LazyPageTransition': LazyComponent<typeof import("../components/PageTransition.vue")['default']>
    'LazyPagination': LazyComponent<typeof import("../components/Pagination.vue")['default']>
    'LazyPaymentMethod': LazyComponent<typeof import("../components/PaymentMethod.vue")['default']>
    'LazyPaymentProviderSelector': LazyComponent<typeof import("../components/PaymentProviderSelector.vue")['default']>
    'LazyProductCard': LazyComponent<typeof import("../components/ProductCard.vue")['default']>
    'LazyProductGrid': LazyComponent<typeof import("../components/ProductGrid.vue")['default']>
    'LazyProductQuickView': LazyComponent<typeof import("../components/ProductQuickView.vue")['default']>
    'LazyProductRecommendations': LazyComponent<typeof import("../components/ProductRecommendations.vue")['default']>
    'LazyProductReviews': LazyComponent<typeof import("../components/ProductReviews.vue")['default']>
    'LazyRatingsDebugger': LazyComponent<typeof import("../components/RatingsDebugger.vue")['default']>
    'LazyRecentlyViewed': LazyComponent<typeof import("../components/RecentlyViewed.vue")['default']>
    'LazyReviewOrder': LazyComponent<typeof import("../components/ReviewOrder.vue")['default']>
    'LazySearchModal': LazyComponent<typeof import("../components/SearchModal.vue")['default']>
    'LazyShippingAddress': LazyComponent<typeof import("../components/ShippingAddress.vue")['default']>
    'LazyShippingMethod': LazyComponent<typeof import("../components/ShippingMethod.vue")['default']>
    'LazyStarRating': LazyComponent<typeof import("../components/StarRating.vue")['default']>
    'LazyStripePaymentElement': LazyComponent<typeof import("../components/StripePaymentElement.vue")['default']>
    'LazyStripePaymentForm': LazyComponent<typeof import("../components/StripePaymentForm.vue")['default']>
    'LazyTheFooter': LazyComponent<typeof import("../components/TheFooter.vue")['default']>
    'LazyTheHeader': LazyComponent<typeof import("../components/TheHeader.vue")['default']>
    'LazyThematicTopBar': LazyComponent<typeof import("../components/ThematicTopBar.vue")['default']>
    'LazyToastNotification': LazyComponent<typeof import("../components/ToastNotification.vue")['default']>
    'LazyTrustedShop': LazyComponent<typeof import("../components/TrustedShop.vue")['default']>
    'LazyNuxtWelcome': LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/welcome.vue")['default']>
    'LazyNuxtLayout': LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-layout")['default']>
    'LazyNuxtErrorBoundary': LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-error-boundary.vue")['default']>
    'LazyClientOnly': LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/client-only")['default']>
    'LazyDevOnly': LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/dev-only")['default']>
    'LazyServerPlaceholder': LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/server-placeholder")['default']>
    'LazyNuxtLink': LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-link")['default']>
    'LazyNuxtLoadingIndicator': LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-loading-indicator")['default']>
    'LazyNuxtTime': LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-time.vue")['default']>
    'LazyNuxtRouteAnnouncer': LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-route-announcer")['default']>
    'LazyNuxtImg': LazyComponent<typeof import("../node_modules/@nuxt/image/dist/runtime/components/NuxtImg.vue")['default']>
    'LazyNuxtPicture': LazyComponent<typeof import("../node_modules/@nuxt/image/dist/runtime/components/NuxtPicture.vue")['default']>
    'LazyIcon': LazyComponent<typeof import("../node_modules/@nuxt/icon/dist/runtime/components/index")['default']>
    'LazyNuxtLinkLocale': LazyComponent<typeof import("../node_modules/@nuxtjs/i18n/dist/runtime/components/NuxtLinkLocale")['default']>
    'LazySwitchLocalePathLink': LazyComponent<typeof import("../node_modules/@nuxtjs/i18n/dist/runtime/components/SwitchLocalePathLink")['default']>
    'LazyNuxtPage': LazyComponent<typeof import("../node_modules/nuxt/dist/pages/runtime/page")['default']>
    'LazyNoScript': LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['NoScript']>
    'LazyLink': LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Link']>
    'LazyBase': LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Base']>
    'LazyTitle': LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Title']>
    'LazyMeta': LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Meta']>
    'LazyStyle': LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Style']>
    'LazyHead': LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Head']>
    'LazyHtml': LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Html']>
    'LazyBody': LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Body']>
    'LazyNuxtIsland': LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-island")['default']>
    'LazyNuxtRouteAnnouncer': LazyComponent<IslandComponent<typeof import("../node_modules/nuxt/dist/app/components/server-placeholder")['default']>>
}

declare module 'vue' {
  export interface GlobalComponents extends _GlobalComponents { }
}

export const AISearchModal: typeof import("../components/AISearchModal.vue")['default']
export const AccountHeader: typeof import("../components/Account/AccountHeader.vue")['default']
export const AccountSettings: typeof import("../components/Account/AccountSettings.vue")['default']
export const AccountAddressManagement: typeof import("../components/Account/AddressManagement.vue")['default']
export const AccountOrderHistory: typeof import("../components/Account/OrderHistory.vue")['default']
export const AccountAddresses: typeof import("../components/AccountAddresses.vue")['default']
export const AddressTypeDisplay: typeof import("../components/AddressTypeDisplay.vue")['default']
export const BillingAddress: typeof import("../components/BillingAddress.vue")['default']
export const BillingAddressToggle: typeof import("../components/BillingAddressToggle.vue")['default']
export const Breadcrumb: typeof import("../components/Breadcrumb.vue")['default']
export const CartAddedPopup: typeof import("../components/CartAddedPopup.vue")['default']
export const CartItem: typeof import("../components/CartItem.vue")['default']
export const CartProvider: typeof import("../components/CartProvider.vue")['default']
export const CategoryHero: typeof import("../components/CategoryHero.vue")['default']
export const CheckoutVerify: typeof import("../components/CheckoutVerify.vue")['default']
export const CollectionHighlights: typeof import("../components/CollectionHighlights.vue")['default']
export const CookieConsent: typeof import("../components/CookieConsent.vue")['default']
export const CustomerInformation: typeof import("../components/CustomerInformation.vue")['default']
export const EasyboxLockerSelector: typeof import("../components/EasyboxLockerSelector.vue")['default']
export const EmptyState: typeof import("../components/EmptyState.vue")['default']
export const ErrorBoundary: typeof import("../components/ErrorBoundary.vue")['default']
export const HeroSection: typeof import("../components/HeroSection.vue")['default']
export const LoadingSkeleton: typeof import("../components/LoadingSkeleton.vue")['default']
export const LoadingSpinner: typeof import("../components/LoadingSpinner.vue")['default']
export const LocaleSelector: typeof import("../components/LocaleSelector.vue")['default']
export const LocaleSwitcher: typeof import("../components/LocaleSwitcher.vue")['default']
export const MobileMenu: typeof import("../components/MobileMenu.vue")['default']
export const PageHero: typeof import("../components/PageHero.vue")['default']
export const PageTransition: typeof import("../components/PageTransition.vue")['default']
export const Pagination: typeof import("../components/Pagination.vue")['default']
export const PaymentMethod: typeof import("../components/PaymentMethod.vue")['default']
export const PaymentProviderSelector: typeof import("../components/PaymentProviderSelector.vue")['default']
export const ProductCard: typeof import("../components/ProductCard.vue")['default']
export const ProductGrid: typeof import("../components/ProductGrid.vue")['default']
export const ProductQuickView: typeof import("../components/ProductQuickView.vue")['default']
export const ProductRecommendations: typeof import("../components/ProductRecommendations.vue")['default']
export const ProductReviews: typeof import("../components/ProductReviews.vue")['default']
export const RatingsDebugger: typeof import("../components/RatingsDebugger.vue")['default']
export const RecentlyViewed: typeof import("../components/RecentlyViewed.vue")['default']
export const ReviewOrder: typeof import("../components/ReviewOrder.vue")['default']
export const SearchModal: typeof import("../components/SearchModal.vue")['default']
export const ShippingAddress: typeof import("../components/ShippingAddress.vue")['default']
export const ShippingMethod: typeof import("../components/ShippingMethod.vue")['default']
export const StarRating: typeof import("../components/StarRating.vue")['default']
export const StripePaymentElement: typeof import("../components/StripePaymentElement.vue")['default']
export const StripePaymentForm: typeof import("../components/StripePaymentForm.vue")['default']
export const TheFooter: typeof import("../components/TheFooter.vue")['default']
export const TheHeader: typeof import("../components/TheHeader.vue")['default']
export const ThematicTopBar: typeof import("../components/ThematicTopBar.vue")['default']
export const ToastNotification: typeof import("../components/ToastNotification.vue")['default']
export const TrustedShop: typeof import("../components/TrustedShop.vue")['default']
export const NuxtWelcome: typeof import("../node_modules/nuxt/dist/app/components/welcome.vue")['default']
export const NuxtLayout: typeof import("../node_modules/nuxt/dist/app/components/nuxt-layout")['default']
export const NuxtErrorBoundary: typeof import("../node_modules/nuxt/dist/app/components/nuxt-error-boundary.vue")['default']
export const ClientOnly: typeof import("../node_modules/nuxt/dist/app/components/client-only")['default']
export const DevOnly: typeof import("../node_modules/nuxt/dist/app/components/dev-only")['default']
export const ServerPlaceholder: typeof import("../node_modules/nuxt/dist/app/components/server-placeholder")['default']
export const NuxtLink: typeof import("../node_modules/nuxt/dist/app/components/nuxt-link")['default']
export const NuxtLoadingIndicator: typeof import("../node_modules/nuxt/dist/app/components/nuxt-loading-indicator")['default']
export const NuxtTime: typeof import("../node_modules/nuxt/dist/app/components/nuxt-time.vue")['default']
export const NuxtRouteAnnouncer: typeof import("../node_modules/nuxt/dist/app/components/nuxt-route-announcer")['default']
export const NuxtImg: typeof import("../node_modules/@nuxt/image/dist/runtime/components/NuxtImg.vue")['default']
export const NuxtPicture: typeof import("../node_modules/@nuxt/image/dist/runtime/components/NuxtPicture.vue")['default']
export const Icon: typeof import("../node_modules/@nuxt/icon/dist/runtime/components/index")['default']
export const NuxtLinkLocale: typeof import("../node_modules/@nuxtjs/i18n/dist/runtime/components/NuxtLinkLocale")['default']
export const SwitchLocalePathLink: typeof import("../node_modules/@nuxtjs/i18n/dist/runtime/components/SwitchLocalePathLink")['default']
export const NuxtPage: typeof import("../node_modules/nuxt/dist/pages/runtime/page")['default']
export const NoScript: typeof import("../node_modules/nuxt/dist/head/runtime/components")['NoScript']
export const Link: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Link']
export const Base: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Base']
export const Title: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Title']
export const Meta: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Meta']
export const Style: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Style']
export const Head: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Head']
export const Html: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Html']
export const Body: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Body']
export const NuxtIsland: typeof import("../node_modules/nuxt/dist/app/components/nuxt-island")['default']
export const NuxtRouteAnnouncer: IslandComponent<typeof import("../node_modules/nuxt/dist/app/components/server-placeholder")['default']>
export const LazyAISearchModal: LazyComponent<typeof import("../components/AISearchModal.vue")['default']>
export const LazyAccountHeader: LazyComponent<typeof import("../components/Account/AccountHeader.vue")['default']>
export const LazyAccountSettings: LazyComponent<typeof import("../components/Account/AccountSettings.vue")['default']>
export const LazyAccountAddressManagement: LazyComponent<typeof import("../components/Account/AddressManagement.vue")['default']>
export const LazyAccountOrderHistory: LazyComponent<typeof import("../components/Account/OrderHistory.vue")['default']>
export const LazyAccountAddresses: LazyComponent<typeof import("../components/AccountAddresses.vue")['default']>
export const LazyAddressTypeDisplay: LazyComponent<typeof import("../components/AddressTypeDisplay.vue")['default']>
export const LazyBillingAddress: LazyComponent<typeof import("../components/BillingAddress.vue")['default']>
export const LazyBillingAddressToggle: LazyComponent<typeof import("../components/BillingAddressToggle.vue")['default']>
export const LazyBreadcrumb: LazyComponent<typeof import("../components/Breadcrumb.vue")['default']>
export const LazyCartAddedPopup: LazyComponent<typeof import("../components/CartAddedPopup.vue")['default']>
export const LazyCartItem: LazyComponent<typeof import("../components/CartItem.vue")['default']>
export const LazyCartProvider: LazyComponent<typeof import("../components/CartProvider.vue")['default']>
export const LazyCategoryHero: LazyComponent<typeof import("../components/CategoryHero.vue")['default']>
export const LazyCheckoutVerify: LazyComponent<typeof import("../components/CheckoutVerify.vue")['default']>
export const LazyCollectionHighlights: LazyComponent<typeof import("../components/CollectionHighlights.vue")['default']>
export const LazyCookieConsent: LazyComponent<typeof import("../components/CookieConsent.vue")['default']>
export const LazyCustomerInformation: LazyComponent<typeof import("../components/CustomerInformation.vue")['default']>
export const LazyEasyboxLockerSelector: LazyComponent<typeof import("../components/EasyboxLockerSelector.vue")['default']>
export const LazyEmptyState: LazyComponent<typeof import("../components/EmptyState.vue")['default']>
export const LazyErrorBoundary: LazyComponent<typeof import("../components/ErrorBoundary.vue")['default']>
export const LazyHeroSection: LazyComponent<typeof import("../components/HeroSection.vue")['default']>
export const LazyLoadingSkeleton: LazyComponent<typeof import("../components/LoadingSkeleton.vue")['default']>
export const LazyLoadingSpinner: LazyComponent<typeof import("../components/LoadingSpinner.vue")['default']>
export const LazyLocaleSelector: LazyComponent<typeof import("../components/LocaleSelector.vue")['default']>
export const LazyLocaleSwitcher: LazyComponent<typeof import("../components/LocaleSwitcher.vue")['default']>
export const LazyMobileMenu: LazyComponent<typeof import("../components/MobileMenu.vue")['default']>
export const LazyPageHero: LazyComponent<typeof import("../components/PageHero.vue")['default']>
export const LazyPageTransition: LazyComponent<typeof import("../components/PageTransition.vue")['default']>
export const LazyPagination: LazyComponent<typeof import("../components/Pagination.vue")['default']>
export const LazyPaymentMethod: LazyComponent<typeof import("../components/PaymentMethod.vue")['default']>
export const LazyPaymentProviderSelector: LazyComponent<typeof import("../components/PaymentProviderSelector.vue")['default']>
export const LazyProductCard: LazyComponent<typeof import("../components/ProductCard.vue")['default']>
export const LazyProductGrid: LazyComponent<typeof import("../components/ProductGrid.vue")['default']>
export const LazyProductQuickView: LazyComponent<typeof import("../components/ProductQuickView.vue")['default']>
export const LazyProductRecommendations: LazyComponent<typeof import("../components/ProductRecommendations.vue")['default']>
export const LazyProductReviews: LazyComponent<typeof import("../components/ProductReviews.vue")['default']>
export const LazyRatingsDebugger: LazyComponent<typeof import("../components/RatingsDebugger.vue")['default']>
export const LazyRecentlyViewed: LazyComponent<typeof import("../components/RecentlyViewed.vue")['default']>
export const LazyReviewOrder: LazyComponent<typeof import("../components/ReviewOrder.vue")['default']>
export const LazySearchModal: LazyComponent<typeof import("../components/SearchModal.vue")['default']>
export const LazyShippingAddress: LazyComponent<typeof import("../components/ShippingAddress.vue")['default']>
export const LazyShippingMethod: LazyComponent<typeof import("../components/ShippingMethod.vue")['default']>
export const LazyStarRating: LazyComponent<typeof import("../components/StarRating.vue")['default']>
export const LazyStripePaymentElement: LazyComponent<typeof import("../components/StripePaymentElement.vue")['default']>
export const LazyStripePaymentForm: LazyComponent<typeof import("../components/StripePaymentForm.vue")['default']>
export const LazyTheFooter: LazyComponent<typeof import("../components/TheFooter.vue")['default']>
export const LazyTheHeader: LazyComponent<typeof import("../components/TheHeader.vue")['default']>
export const LazyThematicTopBar: LazyComponent<typeof import("../components/ThematicTopBar.vue")['default']>
export const LazyToastNotification: LazyComponent<typeof import("../components/ToastNotification.vue")['default']>
export const LazyTrustedShop: LazyComponent<typeof import("../components/TrustedShop.vue")['default']>
export const LazyNuxtWelcome: LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/welcome.vue")['default']>
export const LazyNuxtLayout: LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-layout")['default']>
export const LazyNuxtErrorBoundary: LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-error-boundary.vue")['default']>
export const LazyClientOnly: LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/client-only")['default']>
export const LazyDevOnly: LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/dev-only")['default']>
export const LazyServerPlaceholder: LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/server-placeholder")['default']>
export const LazyNuxtLink: LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-link")['default']>
export const LazyNuxtLoadingIndicator: LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-loading-indicator")['default']>
export const LazyNuxtTime: LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-time.vue")['default']>
export const LazyNuxtRouteAnnouncer: LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-route-announcer")['default']>
export const LazyNuxtImg: LazyComponent<typeof import("../node_modules/@nuxt/image/dist/runtime/components/NuxtImg.vue")['default']>
export const LazyNuxtPicture: LazyComponent<typeof import("../node_modules/@nuxt/image/dist/runtime/components/NuxtPicture.vue")['default']>
export const LazyIcon: LazyComponent<typeof import("../node_modules/@nuxt/icon/dist/runtime/components/index")['default']>
export const LazyNuxtLinkLocale: LazyComponent<typeof import("../node_modules/@nuxtjs/i18n/dist/runtime/components/NuxtLinkLocale")['default']>
export const LazySwitchLocalePathLink: LazyComponent<typeof import("../node_modules/@nuxtjs/i18n/dist/runtime/components/SwitchLocalePathLink")['default']>
export const LazyNuxtPage: LazyComponent<typeof import("../node_modules/nuxt/dist/pages/runtime/page")['default']>
export const LazyNoScript: LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['NoScript']>
export const LazyLink: LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Link']>
export const LazyBase: LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Base']>
export const LazyTitle: LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Title']>
export const LazyMeta: LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Meta']>
export const LazyStyle: LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Style']>
export const LazyHead: LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Head']>
export const LazyHtml: LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Html']>
export const LazyBody: LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Body']>
export const LazyNuxtIsland: LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-island")['default']>
export const LazyNuxtRouteAnnouncer: LazyComponent<IslandComponent<typeof import("../node_modules/nuxt/dist/app/components/server-placeholder")['default']>>

export const componentNames: string[]

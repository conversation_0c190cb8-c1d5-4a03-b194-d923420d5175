/**
 * Scheduled worker for cleaning up expired invoice files and generating feeds
 * This should be configured to run every hour
 */

import { generateAllFeeds } from './services/feed-scheduler';
import { CacheWarmingService } from './services/cache-warming';

type Bindings = {
  DB: D1Database;
  JWT_SECRET: string;
  FRONTEND_URL: string;
  ADMIN_URL: string;
  ENVIRONMENT: string;
  CACHE: KVNamespace;
  SESSIONS: KVNamespace;
  ASSETS: R2Bucket;
  UPLOADS: R2Bucket;
  STORE_URL?: string;
};

export async function scheduled(
  controller: ScheduledController,
  env: Bindings,
  ctx: ExecutionContext
): Promise<void> {
  console.log('Starting scheduled tasks');
  // Check which cron trigger fired this function
  if (controller.cron === '5 * * * *') {
    // Feed generation every hour at minute 5
    console.log('Running feed generation task');
    try {
      await generateAllFeeds(env);
      console.log('✅ Feed generation completed successfully');
    } catch (error) {
      console.error('❌ Feed generation failed:', error);
    }
    
    // Also warm caches during feed generation
    console.log('Running cache warming task');
    try {
      const cacheWarming = new CacheWarmingService(env);
      await cacheWarming.warmAllCaches();
      console.log('✅ Cache warming completed successfully');
    } catch (error) {
      console.error('❌ Cache warming failed:', error);
    }
    return;
  }

  // Default: Invoice cleanup task (7 * * * *)
  console.log('Starting scheduled invoice cleanup task');

  try {
    let deletedCount = 0;
    const currentTime = Date.now();
    
    // List all objects in the UPLOADS bucket
    const list = await env.UPLOADS.list();
    
    for (const object of list.objects) {
      // Check if this is an invoice file (based on naming pattern)
      if (object.key.match(/^[A-Z]+\d+_\d+\.pdf$/)) {
        // Get object metadata to check expiration
        const objectDetails = await env.UPLOADS.get(object.key);
        
        if (objectDetails?.customMetadata?.expiresAt) {
          const expiresAt = parseInt(objectDetails.customMetadata.expiresAt);
          
          if (currentTime > expiresAt) {
            // File has expired, delete it
            await env.UPLOADS.delete(object.key);
            deletedCount++;
            console.log(`Deleted expired invoice: ${object.key}`);
          }
        } else {
          // If no expiration metadata, check file age (fallback)
          // Extract timestamp from filename (format: SERIE123_timestamp.pdf)
          const timestampMatch = object.key.match(/_(\d+)\.pdf$/);
          if (timestampMatch) {
            const fileTimestamp = parseInt(timestampMatch[1]);
            const oneHour = 60 * 60 * 1000;
            
            if (currentTime - fileTimestamp > oneHour) {
              // File is older than 1 hour, delete it
              await env.UPLOADS.delete(object.key);
              deletedCount++;
              console.log(`Deleted old invoice (no metadata): ${object.key}`);
            }
          }
        }
      }
    }
    
    console.log(`Invoice cleanup completed. Deleted ${deletedCount} expired files.`);
  } catch (error) {
    console.error('Error during scheduled invoice cleanup:', error);
  }
} 
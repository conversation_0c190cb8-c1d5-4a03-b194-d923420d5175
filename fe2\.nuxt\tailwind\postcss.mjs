// generated by the @nuxtjs/tailwindcss <https://github.com/nuxt-modules/tailwindcss> module at 6/14/2025, 5:34:57 PM
import "@nuxtjs/tailwindcss/config-ctx"
import configMerger from "@nuxtjs/tailwindcss/merger";

import cfg2 from "./../../tailwind.config.js";
const config = [
{"content":{"files":["C:/Users/<USER>/PhpstormProjects/ro/fe2/components/**/*.{vue,js,jsx,mjs,ts,tsx}","C:/Users/<USER>/PhpstormProjects/ro/fe2/components/global/**/*.{vue,js,jsx,mjs,ts,tsx}","C:/Users/<USER>/PhpstormProjects/ro/fe2/components/**/*.{vue,js,jsx,mjs,ts,tsx}","C:/Users/<USER>/PhpstormProjects/ro/fe2/layouts/**/*.{vue,js,jsx,mjs,ts,tsx}","C:/Users/<USER>/PhpstormProjects/ro/fe2/plugins/**/*.{js,ts,mjs}","C:/Users/<USER>/PhpstormProjects/ro/fe2/composables/**/*.{js,ts,mjs}","C:/Users/<USER>/PhpstormProjects/ro/fe2/utils/**/*.{js,ts,mjs}","C:/Users/<USER>/PhpstormProjects/ro/fe2/pages/**/*.{vue,js,jsx,mjs,ts,tsx}","C:/Users/<USER>/PhpstormProjects/ro/fe2/{A,a}pp.{vue,js,jsx,mjs,ts,tsx}","C:/Users/<USER>/PhpstormProjects/ro/fe2/{E,e}rror.{vue,js,jsx,mjs,ts,tsx}","C:/Users/<USER>/PhpstormProjects/ro/fe2/app.config.{js,ts,mjs}"]}},
{},
cfg2
].reduce((acc, curr) => configMerger(acc, curr), {});

const resolvedConfig = config;

export default resolvedConfig;
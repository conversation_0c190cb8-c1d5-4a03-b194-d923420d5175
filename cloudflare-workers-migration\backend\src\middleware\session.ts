import { Context, Next } from 'hono';
import { SessionService, SessionData } from '../services/session';

export interface SessionMiddlewareOptions {
  autoCreate?: boolean;
  sessionIdHeader?: string;
  sessionIdCookie?: string;
  trackActivity?: boolean;
  trackPages?: boolean;
  requireSession?: boolean;
  skipSession?: boolean; // Skip session entirely for performance-critical routes
  lazyUpdate?: boolean; // Only update session on significant changes
  minUpdateInterval?: number; // Minimum seconds between session updates (default: 300 = 5 minutes)
}

/**
 * Session middleware for automatic session management
 * OPTIMIZED: Reduced KV operations and simplified session tracking
 */
export function sessionMiddleware(options: SessionMiddlewareOptions = {}) {
  const {
    autoCreate = true,
    sessionIdHeader = 'X-Session-ID',
    sessionIdCookie = 'session_id',
    trackActivity = true,
    trackPages = true,
    requireSession = false,
    skipSession = false,
    lazyUpdate = true,
    minUpdateInterval = 300 // 5 minutes default
  } = options;

  return async (c: Context, next: Next) => {
    // PERFORMANCE: Skip session entirely for performance-critical routes
    if (skipSession) {
      return next();
    }

    const sessionService = new SessionService(c.env);
    c.set('sessionService', sessionService);

    // Try to get session ID from header or cookie
    let sessionId = c.req.header(sessionIdHeader) || getCookie(c, sessionIdCookie);
    let session: SessionData | null = null;

    if (sessionId) {
      session = await sessionService.getSession(sessionId);
    }

    // Create new session if needed and allowed
    if (!session && autoCreate) {
      const userAgent = c.req.header('User-Agent');
      const referer = c.req.header('Referer');
      const url = new URL(c.req.url);
      
      // Extract tracking parameters
      const trackingData = {
        utm_source: url.searchParams.get('utm_source') || undefined,
        utm_medium: url.searchParams.get('utm_medium') || undefined,
        utm_campaign: url.searchParams.get('utm_campaign') || undefined,
        referrer: referer || undefined,
        landing_page: url.pathname
      };

      session = await sessionService.createSession({
        activity: {
          last_seen: new Date().toISOString(),
          user_agent: userAgent,
          pages_visited: 1,
          time_spent: 0
        },
        tracking: trackingData
      });

      sessionId = session.id;
      
      // Set session cookie
      setCookie(c, sessionIdCookie, sessionId, {
        httpOnly: true,
        secure: c.env.ENVIRONMENT === 'production',
        sameSite: 'Lax',
        maxAge: 30 * 24 * 60 * 60 // 30 days
      });
    }

    // Check if session is required but missing
    if (requireSession && !session) {
      return c.json({
        success: false,
        error: 'Session required'
      }, 401);
    }

    // PERFORMANCE: Optimized session tracking with lazy updates
    if (session && trackActivity && !lazyUpdate) {
      // Traditional approach - only for routes that explicitly need immediate updates
      const lastSeenTime = new Date(session.activity.last_seen).getTime();
      const now = Date.now();
      const updateInterval = minUpdateInterval * 1000; // Convert to milliseconds

      if (lastSeenTime < (now - updateInterval)) {
        if (trackPages) {
          // Minimal session update - only essential data
          await sessionService.touchSession(session.id, {
            activity: {
              last_seen: new Date().toISOString(),
              pages_visited: session.activity.pages_visited + 1,
              time_spent: session.activity.time_spent,
              ip_address: session.activity.ip_address,
              user_agent: session.activity.user_agent
            }
          });
        } else {
          await sessionService.touchSession(session.id);
        }
      }
      // Update in-memory session without KV write for better performance
      else if (trackPages) {
        session.activity.pages_visited += 1;
        session.activity.last_seen = new Date().toISOString();
      }
    } else if (session && lazyUpdate) {
      // LAZY UPDATE: Only update in-memory, skip KV writes for better performance
      // KV updates will happen on session-critical operations only
      if (trackPages) {
        session.activity.pages_visited += 1;
        session.activity.last_seen = new Date().toISOString();
      }
    }

    // Add session data to context
    c.set('session', session);
    c.set('sessionId', sessionId);

    await next();
  };
}

/**
 * Helper function to get cookie value
 */
function getCookie(c: Context, name: string): string | undefined {
  const cookieHeader = c.req.header('Cookie');
  if (!cookieHeader) return undefined;

  const cookies = cookieHeader.split(';').map(cookie => cookie.trim());
  for (const cookie of cookies) {
    const [key, value] = cookie.split('=');
    if (key === name) {
      return decodeURIComponent(value);
    }
  }

  return undefined;
}

/**
 * Helper function to set cookie
 */
function setCookie(c: Context, name: string, value: string, options: {
  httpOnly?: boolean;
  secure?: boolean;
  sameSite?: 'Strict' | 'Lax' | 'None';
  maxAge?: number;
  path?: string;
  domain?: string;
} = {}) {
  let cookieString = `${name}=${encodeURIComponent(value)}`;
  
  if (options.httpOnly) cookieString += '; HttpOnly';
  if (options.secure) cookieString += '; Secure';
  if (options.sameSite) cookieString += `; SameSite=${options.sameSite}`;
  if (options.maxAge) cookieString += `; Max-Age=${options.maxAge}`;
  if (options.path) cookieString += `; Path=${options.path}`;
  if (options.domain) cookieString += `; Domain=${options.domain}`;

  c.header('Set-Cookie', cookieString);
}

/**
 * Middleware for authenticated sessions only
 */
export function requireAuthenticatedSession() {
  return async (c: Context, next: Next) => {
    const session = c.get('session') as SessionData | null;
    
    if (!session || !session.is_authenticated) {
      return c.json({
        success: false,
        error: 'Authentication required'
      }, 401);
    }

    await next();
  };
}

/**
 * Middleware for admin sessions only
 */
export function requireAdminSession() {
  return async (c: Context, next: Next) => {
    const session = c.get('session') as SessionData | null;
    
    if (!session || !session.is_authenticated || session.authentication?.role !== 'admin') {
      return c.json({
        success: false,
        error: 'Admin access required'
      }, 403);
    }

    await next();
  };
}

/**
 * Helper to get session from context
 */
export function getSessionFromContext(c: Context): SessionData | null {
  return c.get('session') as SessionData | null;
}

/**
 * Helper to get session service from context
 */
export function getSessionServiceFromContext(c: Context): SessionService {
  return c.get('sessionService') as SessionService;
}

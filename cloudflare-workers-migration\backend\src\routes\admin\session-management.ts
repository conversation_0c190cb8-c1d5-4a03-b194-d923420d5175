import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { SessionService } from '../../services/session';

type Bindings = {
  DB: D1Database;
  JWT_SECRET: string;
  FRONTEND_URL: string;
  ADMIN_URL: string;
  CACHE: KVNamespace;
  SESSIONS: KVNamespace;
};

const app = new Hono<{ Bindings: Bindings }>();

// CORS middleware
app.use('/*', cors({
  origin: (origin) => {
    const allowedOrigins = [
      'http://localhost:3000',
      'http://localhost:3001',
      'http://localhost:3002',
      'https://admin.handmadein.ro',
      'https://admin-staging.handmadein.ro'
    ];
    return allowedOrigins.includes(origin) || origin?.endsWith('.handmadein.ro') ? origin : null;
  },
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization'],
  credentials: true,
}));

// Get session analytics
app.get('/analytics', async (c) => {
  try {
    const sessionService = new SessionService(c.env);
    const analytics = await sessionService.getSessionAnalytics();
    
    return c.json({
      success: true,
      analytics
    });
  } catch (error) {
    console.error('Error getting session analytics:', error);
    return c.json({
      success: false,
      error: 'Failed to get session analytics'
    }, 500);
  }
});

// Get session by ID (admin only)
app.get('/:sessionId', async (c) => {
  try {
    const sessionId = c.req.param('sessionId');
    const sessionService = new SessionService(c.env);
    
    const session = await sessionService.getSession(sessionId);
    
    if (!session) {
      return c.json({
        success: false,
        error: 'Session not found'
      }, 404);
    }

    return c.json({
      success: true,
      session
    });
  } catch (error) {
    console.error('Error getting session:', error);
    return c.json({
      success: false,
      error: 'Failed to get session'
    }, 500);
  }
});

// Get sessions by user ID
app.get('/user/:userId', async (c) => {
  try {
    const userId = c.req.param('userId');
    const sessionService = new SessionService(c.env);
    
    const sessions = await sessionService.getSessionsByUserId(userId);
    
    return c.json({
      success: true,
      sessions,
      count: sessions.length
    });
  } catch (error) {
    console.error('Error getting sessions by user ID:', error);
    return c.json({
      success: false,
      error: 'Failed to get sessions by user ID'
    }, 500);
  }
});

// Clean up expired sessions
app.post('/cleanup', async (c) => {
  try {
    const sessionService = new SessionService(c.env);
    const deletedCount = await sessionService.cleanupExpiredSessions();
    
    return c.json({
      success: true,
      message: `Cleaned up ${deletedCount} expired sessions`,
      deleted_count: deletedCount
    });
  } catch (error) {
    console.error('Error cleaning up sessions:', error);
    return c.json({
      success: false,
      error: 'Failed to clean up sessions'
    }, 500);
  }
});

// Force delete session by ID
app.delete('/:sessionId', async (c) => {
  try {
    const sessionId = c.req.param('sessionId');
    const sessionService = new SessionService(c.env);
    
    const success = await sessionService.deleteSession(sessionId);
    
    return c.json({
      success,
      message: success ? 'Session deleted successfully' : 'Failed to delete session'
    });
  } catch (error) {
    console.error('Error deleting session:', error);
    return c.json({
      success: false,
      error: 'Failed to delete session'
    }, 500);
  }
});

// Update session data (admin override)
app.put('/:sessionId', async (c) => {
  try {
    const sessionId = c.req.param('sessionId');
    const sessionService = new SessionService(c.env);
    const body = await c.req.json();
    
    const updatedSession = await sessionService.updateSession(sessionId, body);
    
    if (!updatedSession) {
      return c.json({
        success: false,
        error: 'Session not found'
      }, 404);
    }

    return c.json({
      success: true,
      session: updatedSession
    });
  } catch (error) {
    console.error('Error updating session:', error);
    return c.json({
      success: false,
      error: 'Failed to update session'
    }, 500);
  }
});

// Get session health check
app.get('/health/check', async (c) => {
  try {
    // Test SESSIONS KV availability
    await c.env.SESSIONS.get('health-check');
    await c.env.SESSIONS.put('health-check', 'ok', { expirationTtl: 60 });
    
    const sessionService = new SessionService(c.env);
    const analytics = await sessionService.getSessionAnalytics();
    
    return c.json({
      success: true,
      sessions_kv_available: true,
      total_sessions: analytics.total_sessions,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Session health check failed:', error);
    return c.json({
      success: false,
      sessions_kv_available: false,
      error: error instanceof Error ? error.message : String(error)
    }, 500);
  }
});

// Bulk operations
app.post('/bulk/cleanup-user/:userId', async (c) => {
  try {
    const userId = c.req.param('userId');
    const sessionService = new SessionService(c.env);
    
    const userSessions = await sessionService.getSessionsByUserId(userId);
    let deletedCount = 0;
    
    for (const session of userSessions) {
      const success = await sessionService.deleteSession(session.id);
      if (success) deletedCount++;
    }
    
    return c.json({
      success: true,
      message: `Deleted ${deletedCount} sessions for user ${userId}`,
      deleted_count: deletedCount
    });
  } catch (error) {
    console.error('Error cleaning up user sessions:', error);
    return c.json({
      success: false,
      error: 'Failed to clean up user sessions'
    }, 500);
  }
});

// Get session statistics summary
app.get('/stats/summary', async (c) => {
  try {
    const sessionService = new SessionService(c.env);
    
    // Get basic analytics
    const analytics = await sessionService.getSessionAnalytics();
    
    // Calculate additional metrics
    const metrics = {
      ...analytics,
      authentication_rate: analytics.total_sessions > 0 
        ? (analytics.authenticated_sessions / analytics.total_sessions * 100).toFixed(2) + '%'
        : '0%',
      cart_conversion_rate: analytics.total_sessions > 0
        ? (analytics.sessions_with_carts / analytics.total_sessions * 100).toFixed(2) + '%'
        : '0%',
      top_language: Object.keys(analytics.top_languages).length > 0
        ? Object.entries(analytics.top_languages).sort(([,a], [,b]) => b - a)[0][0]
        : 'unknown',
      most_common_referrer: Object.keys(analytics.top_referrers).length > 0
        ? Object.entries(analytics.top_referrers).sort(([,a], [,b]) => b - a)[0][0]
        : 'direct'
    };
    
    return c.json({
      success: true,
      metrics
    });
  } catch (error) {
    console.error('Error getting session summary:', error);
    return c.json({
      success: false,
      error: 'Failed to get session summary'
    }, 500);
  }
});

export { app as adminSessionRoutes };

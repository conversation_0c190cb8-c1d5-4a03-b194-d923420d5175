import { WorkerEnv } from 'handmadein-shared';

export interface EmailTemplate {
  subject: string;
  html: string;
  text?: string;
}

export interface EmailData {
  to: string;
  from?: string;
  subject: string;
  html: string;
  text?: string;
  attachments?: Array<{
    filename: string;
    content: string;
    contentType: string;
  }>;
}

export class EmailService {
  private env: WorkerEnv;
  private defaultFrom: string;

  constructor(env: WorkerEnv) {
    this.env = env;
    this.defaultFrom = 'Handmade in RO <<EMAIL>>';
  }

  async sendEmail(data: EmailData): Promise<boolean> {
    try {
      const response = await fetch('https://api.resend.com/emails', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.env.RESEND_API_KEY}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          from: data.from || this.defaultFrom,
          to: [data.to],
          subject: data.subject,
          html: data.html,
          text: data.text,
          attachments: data.attachments,
        }),
      });

      if (!response.ok) {
        const error = await response.text();
        console.error('Email sending failed:', error);
        return false;
      }

      const result = await response.json() as { id?: string };
      console.log('Email sent successfully:', result.id || 'unknown');
      return true;
    } catch (error) {
      console.error('Email service error:', error);
      return false;
    }
  }

  async sendOrderConfirmation(order: any, customer: any): Promise<boolean> {
    const template = this.getOrderConfirmationTemplate(order, customer);
    
    return this.sendEmail({
      to: customer.email,
      subject: `Confirmare Comandă #${order.display_id} - Handmade in RO`,
      html: template.html,
      text: template.text,
    });
  }

  async sendPasswordReset(email: string, resetToken: string): Promise<boolean> {
    const resetUrl = `${this.env.FRONTEND_URL}/reset-password?token=${resetToken}`;
    
    const template = this.getPasswordResetTemplate(resetUrl);
    
    return this.sendEmail({
      to: email,
      subject: 'Resetează Parola - Handmade in RO',
      html: template.html,
      text: template.text,
    });
  }

  async sendWelcomeEmail(customer: any): Promise<boolean> {
    const template = this.getWelcomeTemplate(customer);
    
    return this.sendEmail({
      to: customer.email,
      subject: template.subject,
      html: template.html,
      text: template.text,
    });
  }

  async sendAdminOrderNotification(order: any, customer: any): Promise<boolean> {
    const template = this.getAdminOrderNotificationTemplate(order, customer);
    
    return this.sendEmail({
      to: '<EMAIL>',
      subject: `🛍️ Comandă Nouă #${order.number || order.display_id} - ${(order.total_amount || order.total || 0).toFixed(2)} lei`,
      html: template.html,
      text: template.text,
    });
  }

  async sendOrderStatusUpdate(order: any, customer: any, status: string): Promise<boolean> {
    const template = this.getOrderStatusTemplate(order, customer, status);
    
    return this.sendEmail({
      to: customer.email,
      subject: `Comanda #${order.display_id} - ${this.getOrderStatusInRomanian(status)} - Handmade in RO`,
      html: template.html,
      text: template.text,
    });
  }

  private getOrderConfirmationTemplate(order: any, customer: any): EmailTemplate {
    const itemsHtml = order.items.map((item: any) => `
      <tr>
        <td style="padding: 12px; border-bottom: 1px solid #e0e0e0;">
          ${item.title}
          <div style="font-size: 12px; color: #666; margin-top: 4px;">Produs artizanal românesc</div>
        </td>
        <td style="padding: 12px; border-bottom: 1px solid #e0e0e0; text-align: center;">
          ${item.quantity}
        </td>
        <td style="padding: 12px; border-bottom: 1px solid #e0e0e0; text-align: right;">
          ${(item.unit_price * item.quantity).toFixed(2)} lei
        </td>
      </tr>
    `).join('');

    const subtotal = order.subtotal ? (order.subtotal).toFixed(2) : '0.00';
    const shippingTotal = order.shipping_total ? (order.shipping_total).toFixed(2) : '0.00';
    const taxTotal = order.tax_total ? (order.tax_total).toFixed(2) : '0.00';
    const total = order.total ? (order.total).toFixed(2) : '0.00';

    const commonStyles = `
      body { 
        font-family: 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
        line-height: 1.6;
        color: #333;
        background-color: #f5f5f5;
        margin: 0;
        padding: 20px;
      }
      .email-container {
        max-width: 600px;
        margin: 0 auto;
        background: #ffffff;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        overflow: hidden;
      }
      .header {
        background: #2c5f2d;
        color: #fff;
        padding: 30px;
        text-align: center;
      }
      .main-content {
        padding: 30px;
      }
      .footer {
        background: #f8f9fa;
        padding: 20px;
        text-align: center;
        font-size: 12px;
        color: #666;
        border-top: 1px solid #e0e0e0;
      }
      h1 {
        margin: 0;
        font-size: 24px;
        font-weight: 600;
      }
      h2 {
        color: #2c5f2d;
        font-size: 20px;
        margin: 20px 0 15px 0;
      }
      .order-info {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 6px;
        margin: 20px 0;
      }
      .button {
        background: #2c5f2d;
        color: #ffffff !important;
        padding: 12px 24px;
        text-decoration: none;
        border-radius: 4px;
        display: inline-block;
        font-weight: 500;
        margin: 15px 0;
      }
      table {
        width: 100%;
        border-collapse: collapse;
        margin: 20px 0;
        border: 1px solid #e0e0e0;
        border-radius: 6px;
        overflow: hidden;
      }
      th {
        background: #2c5f2d;
        color: white;
        padding: 12px;
        text-align: left;
        font-weight: 500;
      }
      .total-row {
        background: #f8f9fa;
        font-weight: 600;
      }
      .next-steps {
        background: #e8f5e8;
        padding: 20px;
        border-radius: 6px;
        margin: 20px 0;
        border-left: 4px solid #2c5f2d;
      }
    `;

    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Confirmare Comandă - Handmade in RO</title>
        <style>${commonStyles}</style>
      </head>
      <body>
        <div class="email-container">
          <div class="header">
            <h1>Confirmare Comandă #${order.display_id}</h1>
            <p style="margin: 10px 0 0 0; font-size: 16px; opacity: 0.9;">
              Handmade in RO
            </p>
          </div>
          
          <div class="main-content">
            <p>Bună ziua, ${customer.first_name || 'Stimate Client'},</p>
            
            <p>
              Îți mulțumim pentru comandă! Comanda ta a fost primită și confirmată cu succes. 
              Produsele comandate sunt deja finalizate și vor fi pregătite pentru expediere în cel mai scurt timp.
            </p>
            
            <div class="order-info">
              <h2>Detaliile comenzii</h2>
              <p><strong>Numărul comenzii:</strong> #${order.display_id}</p>
              <p><strong>Data comenzii:</strong> ${new Date(order.created_at).toLocaleDateString('ro-RO', { 
                weekday: 'long', 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
              })}</p>
              <p><strong>Status:</strong> ${this.getOrderStatusInRomanian(order.status)}</p>
            </div>

            ${this.generateShippingSection(order)}
            
            <h2>Produsele comandate</h2>
            <table>
              <thead>
                <tr>
                  <th>Produs</th>
                  <th style="text-align: center;">Cantitate</th>
                  <th style="text-align: right;">Preț</th>
                </tr>
              </thead>
              <tbody>
                ${itemsHtml}
              </tbody>
              <tfoot>
                <tr>
                  <td style="padding: 12px; text-align: right;" colspan="2">
                    <strong>Subtotal:</strong>
                  </td>
                  <td style="padding: 12px; text-align: right;">
                    <strong>${subtotal} lei</strong>
                  </td>
                </tr>
                <tr>
                  <td style="padding: 8px 12px; text-align: right;" colspan="2">
                    Transport:
                  </td>
                  <td style="padding: 8px 12px; text-align: right;">
                    ${shippingTotal} lei
                  </td>
                </tr>
                <tr>
                  <td style="padding: 8px 12px; text-align: right;" colspan="2">
                    TVA (Neplătitor):
                  </td>
                  <td style="padding: 8px 12px; text-align: right;">
                    ${taxTotal} lei
                  </td>
                </tr>
                <tr class="total-row">
                  <td style="padding: 12px; text-align: right; font-size: 18px;" colspan="2">
                    <strong>TOTAL:</strong>
                  </td>
                  <td style="padding: 12px; text-align: right; font-size: 18px;">
                    <strong>${total} lei</strong>
                  </td>
                </tr>
              </tfoot>
            </table>
            
            <div class="next-steps">
              <h2>Ce urmează?</h2>
              <ul style="margin: 10px 0; padding-left: 20px;">
                <li>Comanda ta este confirmată și va fi procesată în cel mai scurt timp</li>
                <li>Vei primi un email cu detalii de tracking când comanda va fi expediată</li>
                <li>Poți urmări statusul comenzii în contul tău</li>
              </ul>
            </div>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="${this.env.FRONTEND_URL}/account/orders/${order.id}" class="button">
                Vezi comanda în contul tău
              </a>
            </div>
            
            <p>Pentru întrebări despre comanda ta, ne poți contacta la:</p>
            <p>
              <strong>Email:</strong> <a href="mailto:<EMAIL>"><EMAIL></a><br>
              <strong>Telefon:</strong> +40 ***********
            </p>
            
            <p>Îți mulțumim pentru încrederea acordată!</p>
            <p><strong>Echipa Handmade in RO</strong></p>
          </div>
          
          <div class="footer">
            <p><strong>Handmade in RO</strong></p>
            <p>Produse artizanale autentice românești</p>
            <p style="margin-top: 15px; font-size: 11px;">
              © ${new Date().getFullYear()} Handmade in RO. Toate drepturile rezervate.
            </p>
          </div>
        </div>
      </body>
      </html>
    `;

    const text = `
CONFIRMARE COMANDĂ #${order.display_id} - Handmade in RO

Bună ziua, ${customer.first_name || 'Stimate Client'},

Îți mulțumim pentru comandă! Comanda ta a fost primită și confirmată cu succes. 
Produsele comandate sunt deja finalizate și vor fi pregătite pentru expediere în cel mai scurt timp.

DETALIILE COMENZII:
• Numărul comenzii: #${order.display_id}
• Data comenzii: ${new Date(order.created_at).toLocaleDateString('ro-RO', { 
  weekday: 'long', 
  year: 'numeric', 
  month: 'long', 
  day: 'numeric' 
})}
• Status: ${this.getOrderStatusInRomanian(order.status)}

${this.generateShippingTextSection(order)}

PRODUSELE COMANDATE:
${order.items.map((item: any) => `• ${item.title} x${item.quantity} - ${(item.unit_price * item.quantity).toFixed(2)} lei`).join('\n')}

TOTAL COMANDĂ:
• Subtotal: ${subtotal} lei
• Transport: ${shippingTotal} lei  
• TVA (Neplătitor): ${taxTotal} lei
• TOTAL: ${total} lei

CE URMEAZĂ:
• Comanda ta este confirmată și va fi procesată în cel mai scurt timp
• Vei primi un email cu detalii de tracking când comanda va fi expediată
• Poți urmări statusul comenzii în contul tău

Vezi comanda în contul tău: ${this.env.FRONTEND_URL}/account/orders/${order.id}

Pentru întrebări despre comanda ta, ne poți contacta la:
Email: <EMAIL>
Telefon: +40 ***********

Îți mulțumim pentru încrederea acordată!
Echipa Handmade in RO

Produse artizanale autentice românești
© ${new Date().getFullYear()} Handmade in RO. Toate drepturile rezervate.
    `;

    return { subject: `Confirmare Comandă #${order.display_id} - Handmade in RO`, html, text };
  }

  private getOrderStatusInRomanian(status: string): string {
    const statusMap: Record<string, string> = {
      'pending': 'În așteptare',
      'confirmed': 'Confirmată',
      'processing': 'În procesare',
      'shipped': 'Expediată',
      'delivered': 'Livrată',
      'completed': 'Finalizată',
      'cancelled': 'Anulată',
      'refunded': 'Rambursată'
    };
    return statusMap[status] || status;
  }

  private getPasswordResetTemplate(resetUrl: string): EmailTemplate {
    // Enhanced styles specifically for password reset email
    const resetEmailStyles = `
      body { 
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        line-height: 1.6;
        color: #334433;
        background-color: #f9f7f2;
        margin: 0;
        padding: 0;
      }
      .email-container {
        max-width: 600px;
        margin: 0 auto;
        padding: 0;
        background-color: #ffffff;
        border-radius: 12px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        overflow: hidden;
      }
      .header {
        background: linear-gradient(135deg, #2c5530 0%, #3a6b3e 100%);
        color: #ffffff;
        padding: 40px 30px;
        text-align: center;
        position: relative;
      }
      .header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1.5" fill="rgba(255,255,255,0.08)"/><circle cx="60" cy="80" r="1" fill="rgba(255,255,255,0.06)"/><circle cx="30" cy="70" r="1.2" fill="rgba(255,255,255,0.09)"/></svg>');
        opacity: 0.3;
      }
      .header-content {
        position: relative;
        z-index: 1;
      }
      .lock-icon {
        width: 60px;
        height: 60px;
        margin: 0 auto 20px;
        background: rgba(255,255,255,0.15);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .content {
        padding: 40px 30px;
      }
      .footer {
        background: linear-gradient(45deg, #f9f7f2 0%, #f0ede0 100%);
        padding: 30px;
        text-align: center;
        font-size: 14px;
        color: #666;
        border-top: 3px solid #e0d8c5;
      }
      h1 {
        color: #ffffff;
        margin: 0;
        font-size: 28px;
        font-weight: 700;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
      }
      h2 {
        color: #2c5530;
        font-size: 24px;
        margin: 0 0 20px 0;
        font-weight: 600;
      }
      .reset-button {
        display: inline-block;
        background: linear-gradient(135deg, #c75f34 0%, #d4703d 100%);
        color: white !important;
        padding: 18px 40px;
        text-decoration: none;
        border-radius: 8px;
        font-weight: 700;
        font-size: 16px;
        margin: 30px 0;
        text-align: center;
        box-shadow: 0 4px 15px rgba(199, 95, 52, 0.3);
        transition: all 0.3s ease;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }
      .reset-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(199, 95, 52, 0.4);
      }
      .security-note {
        background: linear-gradient(135deg, #fff3e0 0%, #ffeaa7 100%);
        padding: 20px;
        margin: 25px 0;
        border-radius: 8px;
        border-left: 4px solid #f39c12;
        position: relative;
      }
      .security-note::before {
        content: '🔒';
        font-size: 20px;
        position: absolute;
        top: 15px;
        left: 15px;
      }
      .security-note-content {
        margin-left: 35px;
      }
      .artisan-section {
        background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
        padding: 25px;
        margin: 30px 0;
        border-radius: 10px;
        border: 2px solid #d4e6d4;
        text-align: center;
      }
      .artisan-emoji {
        font-size: 40px;
        margin-bottom: 15px;
        display: block;
      }
      .support-section {
        background: #f8f9fa;
        padding: 20px;
        margin: 25px 0;
        border-radius: 8px;
        border: 1px solid #e9ecef;
        text-align: center;
      }
      .footer-logo {
        font-size: 18px;
        font-weight: bold;
        color: #2c5530;
        margin-bottom: 10px;
      }
      .footer-tagline {
        font-style: italic;
        color: #7d8471;
        margin-bottom: 15px;
      }
      a {
        color: #c75f34;
        text-decoration: none;
      }
      a:hover {
        text-decoration: underline;
      }
      .divider {
        height: 3px;
        background: linear-gradient(90deg, #2c5530 0%, #c75f34 50%, #2c5530 100%);
        margin: 30px 0;
        border-radius: 2px;
      }
    `;

    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Resetează Parola - Handmade in RO</title>
        <style>${resetEmailStyles}</style>
      </head>
      <body>
        <div class="email-container">
          <div class="header">
            <div class="header-content">
              <div class="lock-icon">
                🔐
              </div>
              <h1>Resetează Parola</h1>
              <p style="margin: 10px 0 0 0; font-size: 16px; opacity: 0.9;">
                Pentru contul tău Handmade in RO
              </p>
            </div>
          </div>
          
          <div class="content">
            <h2>Salut, iubitor de artizanat! 👋</h2>
            
            <p style="font-size: 16px; margin-bottom: 20px;">
              Am primit o solicitare pentru resetarea parolei contului tău de pe <strong>Handmade in RO</strong>. 
              Nu-ți face griji - se întâmplă și celor mai buni meșteri!
            </p>
            
            <div class="artisan-section">
              <span class="artisan-emoji">🏺</span>
              <p style="margin: 0; font-weight: 600; color: #2c5530;">
                <em>"Ca un ceramist care remodelează lutul, și tu poți să-ți remodelezi parola!"</em>
              </p>
            </div>
            
            <p style="font-size: 16px; margin: 25px 0;">
              Pentru a-ți seta o parolă nouă și sigură, apasă pe butonul de mai jos:
            </p>
            
            <div style="text-align: center;">
              <a href="${resetUrl}" class="reset-button">
                🔓 Resetează Parola Acum
              </a>
            </div>
            
            <div class="security-note">
              <div class="security-note-content">
                <p style="margin: 0 0 10px 0; font-weight: 600; color: #d68910;">
                  ⏰ Important pentru securitate:
                </p>
                <ul style="margin: 0; padding-left: 20px; color: #856404;">
                  <li>Acest link va expira în <strong>1 oră</strong></li>
                  <li>Poate fi folosit o singură dată</li>
                  <li>Dacă nu ai solicitat resetarea, ignoră acest email</li>
                </ul>
              </div>
            </div>
            
            <div class="divider"></div>
            
            <p style="font-size: 14px; color: #666; margin: 20px 0;">
              Dacă butonul nu funcționează, poți copia și lipi link-ul de mai jos în browser:
            </p>
            <p style="word-break: break-all; background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; font-size: 12px; border: 1px solid #e9ecef;">
              <a href="${resetUrl}" style="color: #c75f34;">${resetUrl}</a>
            </p>
            
            <div class="support-section">
              <p style="margin: 0 0 10px 0; font-weight: 600; color: #2c5530;">
                🤝 Ai nevoie de ajutor?
              </p>
              <p style="margin: 0; font-size: 14px; color: #666;">
                Echipa noastră de asistență este aici pentru tine!<br>
                Scrie-ne la <a href="mailto:<EMAIL>"><EMAIL></a>
              </p>
            </div>
            
            <p style="margin: 30px 0 0 0; font-size: 16px; color: #2c5530;">
              Cu drag și pasiune pentru artizanatul românesc,<br>
              <strong>Echipa Handmade in RO</strong> 🇷🇴
            </p>
          </div>
          
          <div class="footer">
            <div class="footer-logo">🏺 Handmade in RO</div>
            <div class="footer-tagline">"Păstrăm vie tradiția prin artizanat autentic"</div>
            
            <p style="margin: 15px 0 5px 0; font-size: 12px;">
              Acest email a fost trimis pentru securitatea contului tău.<br>
              © ${new Date().getFullYear()} Handmade in RO. Toate drepturile rezervate.
            </p>
            
            <div style="margin-top: 20px; padding-top: 15px; border-top: 1px solid #d0c9b8;">
              <span style="font-size: 24px;">🌾 🏺 🧵 🪶 🎨</span><br>
              <span style="font-size: 11px; color: #999;">Artizanat autentic românesc din generație în generație</span>
            </div>
          </div>
        </div>
      </body>
      </html>
    `;

    const text = `
Resetează Parola - Handmade in RO

Salut, iubitor de artizanat!

Am primit o solicitare pentru resetarea parolei contului tău de pe Handmade in RO. Nu-ți face griji - se întâmplă și celor mai buni meșteri!

Ca un ceramist care remodelează lutul, și tu poți să-ți remodelezi parola!

Pentru a-ți seta o parolă nouă și sigură, accesează link-ul de mai jos:
${resetUrl}

IMPORTANT pentru securitate:
• Acest link va expira în 1 oră
• Poate fi folosit o singură dată  
• Dacă nu ai solicitat resetarea, ignoră acest email

Ai nevoie de ajutor?
Echipa noastră de asistență este aici pentru tine!
Scrie-<NAME_EMAIL>

Cu drag și pasiune pentru artizanatul românesc,
Echipa Handmade in RO 🇷🇴

---
🏺 Handmade in RO
"Păstrăm vie tradiția prin artizanat autentic"

© ${new Date().getFullYear()} Handmade in RO. Toate drepturile rezervate.
Artizanat autentic românesc din generație în generație
    `;

    return { subject: '🔐 Resetează Parola - Atelierul Meșterilor Români', html, text };
  }

  private getWelcomeTemplate(customer: any): EmailTemplate {
    // Enhanced welcome email styles with chess/backgammon theme
    const welcomeStyles = `
      body { 
        font-family: 'Georgia', 'Times New Roman', serif;
        line-height: 1.7;
        color: #2d3748;
        background: linear-gradient(135deg, #f4f1e8 0%, #e8dcc6 50%, #f0e6d2 100%);
        margin: 0;
        padding: 20px;
      }
      .email-container {
        max-width: 650px;
        margin: 0 auto;
        background: #ffffff;
        border-radius: 20px;
        box-shadow: 0 15px 40px rgba(139, 69, 19, 0.2);
        overflow: hidden;
        border: 4px solid #d4af37;
        position: relative;
      }
      .header {
        background: linear-gradient(135deg, #8b4513 0%, #a0522d 30%, #cd853f 70%, #8b4513 100%);
        color: #fff;
        padding: 50px 30px;
        text-align: center;
        position: relative;
      }
      .header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200"><circle cx="30" cy="40" r="2" fill="rgba(212,175,55,0.4)"/><circle cx="170" cy="60" r="1.5" fill="rgba(212,175,55,0.3)"/><circle cx="140" cy="160" r="1.8" fill="rgba(212,175,55,0.35)"/><circle cx="50" cy="150" r="1.2" fill="rgba(212,175,55,0.25)"/></svg>');
        opacity: 0.6;
      }
      .header-content {
        position: relative;
        z-index: 1;
      }
      .welcome-decoration {
        font-size: 60px;
        margin-bottom: 20px;
        text-shadow: 3px 3px 6px rgba(0,0,0,0.4);
        animation: float 3s ease-in-out infinite;
      }
      @keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-10px); }
      }
      .main-content {
        padding: 50px 40px;
        background: #ffffff;
      }
      .footer {
        background: linear-gradient(45deg, #f7f3e9 0%, #e8dcc6 50%, #f0e6d2 100%);
        padding: 35px 30px;
        text-align: center;
        color: #5a5a5a;
        border-top: 4px solid #d4af37;
      }
      h1 {
        color: #fff;
        margin: 0;
        font-size: 32px;
        font-weight: bold;
        text-shadow: 3px 3px 6px rgba(0,0,0,0.4);
        font-family: 'Georgia', serif;
      }
      h2 {
        color: #8b4513;
        font-size: 26px;
        margin: 0 0 25px 0;
        font-family: 'Georgia', serif;
        text-align: center;
      }
      .personal-greeting {
        font-size: 20px;
        color: #2d3748;
        margin-bottom: 30px;
        text-align: center;
        font-style: italic;
        background: linear-gradient(135deg, #fff8dc 0%, #f5f5dc 100%);
        padding: 20px;
        border-radius: 12px;
        border: 2px solid #d4af37;
      }
      .welcome-message {
        background: linear-gradient(135deg, #e6f3ff 0%, #cce7ff 100%);
        padding: 35px;
        margin: 30px 0;
        border-radius: 15px;
        border: 2px solid #4682b4;
        text-align: center;
        position: relative;
      }
      .welcome-message::before {
        content: '🏺';
        font-size: 40px;
        position: absolute;
        top: -20px;
        left: 50%;
        transform: translateX(-50%);
        background: white;
        padding: 0 12px;
      }
      .products-showcase {
        background: linear-gradient(135deg, #f0fff0 0%, #e6ffe6 100%);
        padding: 30px;
        margin: 30px 0;
        border-radius: 15px;
        border: 2px solid #32cd32;
      }
      .product-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin: 25px 0;
      }
      .product-item {
        background: white;
        padding: 20px;
        border-radius: 10px;
        border: 2px solid #d4af37;
        text-align: center;
        box-shadow: 0 4px 8px rgba(212, 175, 55, 0.2);
      }
      .product-icon {
        font-size: 36px;
        margin-bottom: 10px;
        display: block;
      }
      .button {
        background: linear-gradient(135deg, #d4af37 0%, #ffd700 50%, #d4af37 100%);
        color: #8b4513 !important;
        padding: 18px 35px;
        text-decoration: none;
        border-radius: 10px;
        display: inline-block;
        font-weight: bold;
        font-size: 18px;
        margin: 25px 0;
        box-shadow: 0 6px 12px rgba(212, 175, 55, 0.4);
        transition: all 0.3s ease;
        text-transform: uppercase;
        letter-spacing: 1px;
      }
      .button:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 16px rgba(212, 175, 55, 0.5);
      }
      a {
        color: #8b4513;
        text-decoration: none;
      }
      a:hover {
        text-decoration: underline;
      }
      .cultural-wisdom {
        background: linear-gradient(135deg, #fff8dc 0%, #f5f5dc 100%);
        padding: 25px;
        margin: 30px 0;
        border-radius: 12px;
        border-left: 5px solid #d4af37;
        font-style: italic;
        text-align: center;
        color: #8b4513;
      }
      .artisan-promise {
        background: linear-gradient(135deg, #f5f5dc 0%, #e6ddc4 100%);
        padding: 30px;
        margin: 30px 0;
        border-radius: 15px;
        text-align: center;
        color: #5d4e37;
        border: 3px solid #cd853f;
        position: relative;
      }
      .artisan-promise::before {
        content: '🤝';
        font-size: 36px;
        position: absolute;
        top: -18px;
        left: 50%;
        transform: translateX(-50%);
        background: white;
        padding: 0 10px;
      }
      .decorative-separator {
        height: 5px;
        background: linear-gradient(90deg, #8b4513 0%, #d4af37 20%, #cd853f 40%, #d4af37 60%, #8b4513 80%, #d4af37 100%);
        margin: 40px 0;
        border-radius: 3px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      }
    `;

    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Bine ai venit în Atelierul Meșterilor Români!</title>
        <style>${welcomeStyles}</style>
      </head>
      <body>
        <div class="email-container">
          <div class="header">
            <div class="header-content">
              <div class="welcome-decoration">♟️ 🏺 ♜ 🎯</div>
              <h1>Bine ai venit în familia noastră!</h1>
              <p style="margin: 20px 0 0 0; font-size: 18px; opacity: 0.95;">
                Atelierul Meșterilor Români te salută
              </p>
            </div>
          </div>
          
          <div class="main-content">
            <div class="personal-greeting">
              Dragă ${customer.first_name || 'Iubitor al meșteșugurilor'}, 🌟
            </div>
            
            <div class="welcome-message">
              <h2 style="color: #4682b4; margin: 20px 0 20px 0;">Îți mulțumim că te-ai alăturat!</h2>
              <p style="margin: 0; font-size: 17px; color: #2d3748; line-height: 1.8;">
                Contul tău a fost creat cu succes și acum faci parte din comunitatea noastră 
                pasionată de <strong>artizanatul autentic românesc</strong>. Suntem mândri să 
                împărțim cu tine tradiția strămoșească prin fiecare produs creat manual.
              </p>
            </div>
            
            <div class="cultural-wisdom">
              <span style="font-size: 24px; display: block; margin-bottom: 15px;">🎨</span>
              <p style="margin: 0; font-size: 18px; font-weight: 600;">
                <em>"Nu cumperi doar un obiect, ci o poveste, o tradiție și sufletul meșterului."</em>
              </p>
              <small style="color: #999; margin-top: 10px; display: block;">- Înțelepciune din atelierele românești -</small>
            </div>
            
            <div class="products-showcase">
              <h3 style="color: #2d8f2d; text-align: center; margin: 0 0 25px 0; font-size: 22px;">
                🎯 Descoperă Comoarele Noastre Handmade
              </h3>
              
              <div class="product-grid">
                <div class="product-item">
                  <span class="product-icon">♟️</span>
                  <h4 style="color: #8b4513; margin: 10px 0;">Șah Românesc</h4>
                  <p style="margin: 0; font-size: 14px; color: #666;">
                    Seturi de șah lucrate manual din lemn natural, fiecare piesă fiind o mică operă de artă
                  </p>
                </div>
                
                <div class="product-item">
                  <span class="product-icon">🎲</span>
                  <h4 style="color: #8b4513; margin: 10px 0;">Table Tradiționale</h4>
                  <p style="margin: 0; font-size: 14px; color: #666;">
                    Jocuri de table (backgammon) autentice, realizate după tehnici ancestrale
                  </p>
                </div>
                
                <div class="product-item">
                  <span class="product-icon">🏺</span>
                  <h4 style="color: #8b4513; margin: 10px 0;">Decorațiuni din Lemn</h4>
                  <p style="margin: 0; font-size: 14px; color: #666;">
                    Obiecte decorative și utilitare din lemn masiv, lucrate cu tehnici tradiționale
                  </p>
                </div>
                
                <div class="product-item">
                  <span class="product-icon">🎯</span>
                  <h4 style="color: #8b4513; margin: 10px 0;">Jocuri Românești</h4>
                  <p style="margin: 0; font-size: 14px; color: #666;">
                    Colecția completă de jocuri tradiționale: rummy, dame, și multe altele
                  </p>
                </div>
              </div>
            </div>
            
            <div style="text-align: center; margin: 40px 0;">
              <a href="${this.env.FRONTEND_URL || 'https://handmadein.ro'}" class="button">
                🛍️ Explorează Atelierul
              </a>
            </div>
            
            <div class="artisan-promise">
              <h3 style="color: #5d4e37; margin: 20px 0 20px 0; font-size: 20px;">Promisiunea Noastră</h3>
              <div style="text-align: left; max-width: 500px; margin: 0 auto;">
                <div style="margin: 15px 0; display: flex; align-items: center;">
                  <span style="color: #32cd32; font-size: 18px; margin-right: 10px;">✅</span>
                  <span><strong>100% Handmade:</strong> Fiecare produs este lucrat manual de meșterii noștri</span>
                </div>
                <div style="margin: 15px 0; display: flex; align-items: center;">
                  <span style="color: #32cd32; font-size: 18px; margin-right: 10px;">✅</span>
                  <span><strong>Lemn Natural:</strong> Folosim doar lemn de calitate superioară din România</span>
                </div>
                <div style="margin: 15px 0; display: flex; align-items: center;">
                  <span style="color: #32cd32; font-size: 18px; margin-right: 10px;">✅</span>
                  <span><strong>Tradiție Autentică:</strong> Tehnici transmise din generație în generație</span>
                </div>
                <div style="margin: 15px 0; display: flex; align-items: center;">
                  <span style="color: #32cd32; font-size: 18px; margin-right: 10px;">✅</span>
                  <span><strong>Calitate Garantată:</strong> Fiecare produs vine cu garanția meșterului</span>
                </div>
              </div>
            </div>
            
            <div class="decorative-separator"></div>
            
            <div style="text-align: center; margin: 35px 0;">
              <h3 style="color: #8b4513; margin: 0 0 20px 0; font-size: 22px;">
                🎯 De ce să alegi meșteșugurile noastre?
              </h3>
              <p style="font-size: 16px; line-height: 1.8; color: #2d3748; max-width: 550px; margin: 0 auto;">
                În era producției în masă, noi păstrăm vie flacăra autenticității. Fiecare șah, 
                fiecare joc de table, fiecare sculptură în lemn poartă în sine sufletul meșterului 
                și tăria tradițiilor românești. Când cumperi de la noi, susții artizanatul local 
                și păstrezi vie cultura strămoșilor.
              </p>
            </div>
            
            <div class="contact-section">
              <h4 style="color: #8b4513; margin: 0 0 15px 0;">🤝 Suntem aici pentru tine!</h4>
              <p style="margin: 0 0 10px 0; color: #666; font-size: 15px;">
                Ai întrebări despre produsele noastre sau tehnicile de lucru?
              </p>
              <p style="margin: 0; color: #8b4513; font-weight: 600;">
                📧 <a href="mailto:<EMAIL>"><EMAIL></a> |
                📞 <a href="tel:+***********">+40 ***********</a>
              </p>
            </div>
            
            <div style="text-align: center; margin: 40px 0; padding: 25px; background: linear-gradient(135deg, #fff8dc 0%, #f5f5dc 100%); border-radius: 12px;">
              <p style="margin: 0 0 15px 0; font-size: 16px; color: #8b4513;">
                <strong>Cu recunoștință pentru încrederea acordată,</strong>
              </p>
              <p style="margin: 0; font-size: 18px; color: #d4af37; font-weight: bold;">
                🏺 Echipa Atelierului Meșterilor Români 🏺
              </p>
              <p style="margin: 10px 0 0 0; font-size: 14px; color: #999;">
                Unde tradiția întâlnește pasiunea pentru meșteșug
              </p>
            </div>
          </div>
          
          <div class="footer">
            <div style="font-size: 24px; margin-bottom: 20px;">🇷🇴 ♟️ 🏺 🎲 🎯 🇷🇴</div>
            <div style="font-size: 20px; font-weight: bold; color: #8b4513; margin-bottom: 12px;">
              Atelierul Meșterilor Români
            </div>
            <div style="font-style: italic; color: #5a5a5a; margin-bottom: 20px; font-size: 16px;">
              "Tradiția strămoșească prin mâinile noastre"
            </div>
            <div style="margin: 20px 0; padding: 15px; background: rgba(212, 175, 55, 0.1); border-radius: 8px;">
              <p style="margin: 0; font-size: 13px; color: #666;">
                🌟 <strong>Urmărește-ne pentru noutăți:</strong><br>
                Produse noi • Tehnici tradiționale • Povești din atelier
              </p>
            </div>
            <p style="margin: 20px 0 5px 0; font-size: 12px; color: #999;">
              Acest email a fost trimis către ${customer.email}<br>
              © ${new Date().getFullYear()} Handmade in RO. Toate drepturile rezervate.
            </p>
            <div style="margin-top: 25px; padding-top: 20px; border-top: 2px solid #d0c9b8;">
              <span style="font-size: 11px; color: #888; line-height: 1.4;">
                Artizanat autentic românesc • Tehnici ancestrale • Materiale naturale<br>
                Din generație în generație • Cu dragoste pentru tradiție • Made in România
              </span>
            </div>
          </div>
        </div>
      </body>
      </html>
    `;

    const text = `
🏺 BINE AI VENIT ÎN ATELIERUL MEȘTERILOR ROMÂNI! 🏺

Dragă ${customer.first_name || 'Iubitor al meșteșugurilor'},

🌟 ÎȚI MULȚUMIM CĂ TE-AI ALĂTURAT!

Contul tău a fost creat cu succes și acum faci parte din comunitatea noastră pasionată de artizanatul autentic românesc. Suntem mândri să împărțim cu tine tradiția strămoșească prin fiecare produs creat manual.

🎨 "Nu cumperi doar un obiect, ci o poveste, o tradiție și sufletul meșterului."
- Înțelepciune din atelierele românești -

🎯 DESCOPERĂ COMOARELE NOASTRE HANDMADE:

♟️ ȘAH ROMÂNESC
• Seturi de șah lucrate manual din lemn natural
• Fiecare piesă este o mică operă de artă

🎲 TABLE TRADIȚIONALE  
• Jocuri de table (backgammon) autentice
• Realizate după tehnici ancestrale

🏺 DECORAȚIUNI DIN LEMN
• Obiecte decorative și utilitare din lemn masiv
• Lucrate cu tehnici tradiționale

🎯 JOCURI ROMÂNEȘTI
• Colecția completă: rummy, dame, și multe altele
• Păstrăm vie cultura strămoșilor

🤝 PROMISIUNEA NOASTRĂ:
✅ 100% Handmade - Fiecare produs este lucrat manual
✅ Lemn Natural - Doar lemn de calitate superioară din România  
✅ Tradiție Autentică - Tehnici transmise din generație în generație
✅ Calitate Garantată - Fiecare produs vine cu garanția meșterului

🎯 DE CE SĂ ALEGI MEȘTEȘUGURILE NOASTRE?

În era producției în masă, noi păstrăm vie flacăra autenticității. Fiecare șah, fiecare joc de table, fiecare sculptură în lemn poartă în sine sufletul meșterului și tăria tradițiilor românești. Când cumperi de la noi, susții artizanatul local și păstrezi vie cultura strămoșilor.

Explorează atelierul: ${this.env.FRONTEND_URL || 'https://handmadein.ro'}

🤝 SUNTEM AICI PENTRU TINE!
Ai întrebări despre produsele noastre sau tehnicile de lucru?
Contact: <EMAIL> | +40 ***********  

Cu recunoștință pentru încrederea acordată,
🏺 Echipa Atelierului Meșterilor Români 🏺
"Unde tradiția întâlnește pasiunea pentru meșteșug"

🇷🇴 Atelierul Meșterilor Români 🇷🇴
"Tradiția strămoșească prin mâinile noastre"
© ${new Date().getFullYear()} Handmade in RO. Toate drepturile rezervate.
    `;

    return { 
      subject: '🏺 Bine ai venit în Atelierul Meșterilor Români! ♟️ Tradiție și Artizanat Autentic', 
      html, 
      text 
    };
  }

  private getOrderStatusTemplate(order: any, customer: any, status: string): EmailTemplate {
    const statusMessages = {
      'completed': '🎉 Comanda ta a fost finalizată cu succes! Meșterii noștri au pus suflet în fiecare detaliu.',
      'shipped': '🚚 Comanda ta este în drum spre tine! Curând vei putea admira meșteșugul autentic românesc.',
      'delivered': '🏠 Comanda ta a fost livrată cu succes! Sperăm să te bucuri de noile tale comori handmade.',
      'processing': '🔨 Meșterii noștri lucrează cu pasiune la comanda ta. Fiecare piesă este creată cu dragoste.',
      'confirmed': '✅ Comanda ta a fost confirmată și va fi procesată în curând de atelierul nostru.',
      'canceled': '❌ Comanda ta a fost anulată. Dacă ai întrebări, nu ezita să ne contactezi.',
      'cancelled': '❌ Comanda ta a fost anulată. Dacă ai întrebări, nu ezita să ne contactezi.',
      'refunded': '💰 Comanda ta a fost rambursată. Mulțumim pentru înțelegere.',
    };

    const statusIcons = {
      'completed': '🏆',
      'shipped': '🚛',
      'delivered': '🎁',
      'processing': '⚒️',
      'confirmed': '✅',
      'canceled': '❌',
      'cancelled': '❌',
      'refunded': '💰'
    };

    const romanianStatus = this.getOrderStatusInRomanian(status);
    const message = statusMessages[status as keyof typeof statusMessages] || `Statusul comenzii tale a fost actualizat la: ${romanianStatus}`;
    const statusIcon = statusIcons[status as keyof typeof statusIcons] || '📧';

    // Enhanced status email styles with Romanian artisan theme
    const statusStyles = `
      body { 
        font-family: 'Georgia', 'Times New Roman', serif;
        line-height: 1.7;
        color: #2d3748;
        background: linear-gradient(135deg, #f7f3e9 0%, #e8dcc6 100%);
        margin: 0;
        padding: 20px;
      }
      .email-container {
        max-width: 650px;
        margin: 0 auto;
        background: #ffffff;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(139, 69, 19, 0.15);
        overflow: hidden;
        border: 3px solid #d4af37;
      }
      .header {
        background: linear-gradient(135deg, #8b4513 0%, #a0522d 50%, #8b4513 100%);
        color: #fff;
        padding: 40px 30px;
        text-align: center;
        position: relative;
      }
      .header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="25" r="1.5" fill="rgba(212,175,55,0.3)"/><circle cx="80" cy="35" r="1" fill="rgba(212,175,55,0.2)"/><circle cx="65" cy="75" r="1.2" fill="rgba(212,175,55,0.25)"/><circle cx="35" cy="80" r="0.8" fill="rgba(212,175,55,0.2)"/></svg>');
        opacity: 0.4;
      }
      .header-content {
        position: relative;
        z-index: 1;
      }
      .status-decoration {
        font-size: 50px;
        margin-bottom: 15px;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
      }
      .main-content {
        padding: 40px 30px;
        background: #ffffff;
      }
      .footer {
        background: linear-gradient(45deg, #f7f3e9 0%, #e8dcc6 100%);
        padding: 30px;
        text-align: center;
        font-size: 14px;
        color: #5a5a5a;
        border-top: 3px solid #d4af37;
      }
      h1 {
        color: #fff;
        margin: 0;
        font-size: 28px;
        font-weight: bold;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
      }
      h2 {
        color: #8b4513;
        font-size: 24px;
        margin: 0 0 20px 0;
        font-family: 'Georgia', serif;
        text-align: center;
      }
      .greeting {
        font-size: 18px;
        color: #2d3748;
        margin-bottom: 25px;
        text-align: center;
        font-style: italic;
      }
      .status-update-box {
        background: linear-gradient(135deg, #fff8dc 0%, #f5f5dc 100%);
        padding: 30px;
        margin: 30px 0;
        border-radius: 15px;
        border: 3px solid #d4af37;
        text-align: center;
        box-shadow: 0 6px 12px rgba(212, 175, 55, 0.2);
        position: relative;
      }
      .status-update-box::before {
        content: '${statusIcon}';
        font-size: 40px;
        position: absolute;
        top: -20px;
        left: 50%;
        transform: translateX(-50%);
        background: white;
        padding: 0 12px;
        border-radius: 50%;
        border: 3px solid #d4af37;
      }
      .order-details {
        background: linear-gradient(135deg, #e6f3ff 0%, #cce7ff 100%);
        padding: 25px;
        margin: 25px 0;
        border-radius: 12px;
        border: 2px solid #4682b4;
      }
      .artisan-message {
        background: linear-gradient(135deg, #f0fff0 0%, #e6ffe6 100%);
        padding: 25px;
        margin: 25px 0;
        border-radius: 12px;
        border-left: 5px solid #32cd32;
        font-style: italic;
        text-align: center;
      }
      .button {
        background: linear-gradient(135deg, #d4af37 0%, #ffd700 50%, #d4af37 100%);
        color: #8b4513 !important;
        padding: 15px 30px;
        text-decoration: none;
        border-radius: 8px;
        display: inline-block;
        font-weight: bold;
        font-size: 16px;
        margin: 20px 0;
        box-shadow: 0 4px 8px rgba(212, 175, 55, 0.3);
        transition: all 0.3s ease;
        text-transform: uppercase;
        letter-spacing: 1px;
      }
      .button:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(212, 175, 55, 0.4);
      }
      a {
        color: #8b4513;
        text-decoration: none;
      }
      a:hover {
        text-decoration: underline;
      }
      .contact-section {
        background: #f8f8f8;
        padding: 20px;
        border-radius: 8px;
        margin: 25px 0;
        text-align: center;
      }
      .decorative-border {
        height: 4px;
        background: linear-gradient(90deg, #8b4513 0%, #d4af37 25%, #8b4513 50%, #d4af37 75%, #8b4513 100%);
        margin: 30px 0;
        border-radius: 2px;
      }
      .cultural-quote {
        background: linear-gradient(135deg, #fff8dc 0%, #f5f5dc 100%);
        padding: 20px;
        margin: 25px 0;
        border-radius: 8px;
        border-left: 4px solid #d4af37;
        font-style: italic;
        text-align: center;
        color: #8b4513;
      }
    `;

    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Actualizare Status Comandă - Atelierul Meșterilor</title>
        <style>${statusStyles}</style>
      </head>
      <body>
        <div class="email-container">
          <div class="header">
            <div class="header-content">
              <div class="status-decoration">${statusIcon}</div>
              <h1>Actualizare Status Comandă</h1>
              <p style="margin: 15px 0 0 0; font-size: 16px; opacity: 0.9;">
                Comanda #${order.display_id} - ${romanianStatus}
              </p>
            </div>
          </div>
          
          <div class="main-content">
            <div class="greeting">
              Dragă ${customer.first_name || 'Stimate Client'}, 👋
            </div>
            
            <div class="status-update-box">
              <h2 style="color: #8b4513; margin: 25px 0 20px 0; font-size: 22px;">
                Vești bune despre comanda ta!
              </h2>
              <p style="margin: 0; font-size: 17px; color: #2d3748; line-height: 1.8;">
                ${message}
              </p>
            </div>
            
            <div class="order-details">
              <h3 style="color: #4682b4; margin: 0 0 15px 0; font-size: 20px; text-align: center;">
                📋 Detaliile Comenzii
              </h3>
              <div style="display: flex; justify-content: space-between; flex-wrap: wrap; gap: 15px;">
                <div>
                  <strong>Numărul comenzii:</strong><br>
                  <span style="color: #8b4513; font-weight: 600;">#${order.display_id}</span>
                </div>
                <div>
                  <strong>Status actual:</strong><br>
                  <span style="color: #32cd32; font-weight: 600;">${romanianStatus}</span>
                </div>
                <div>
                  <strong>Data actualizării:</strong><br>
                  <span style="color: #5a5a5a;">${new Date().toLocaleDateString('ro-RO', { 
                    weekday: 'long', 
                    year: 'numeric', 
                    month: 'long', 
                    day: 'numeric' 
                  })}</span>
                </div>
              </div>
            </div>
            
            ${status === 'processing' ? `
            <div class="artisan-message">
              <span style="font-size: 24px; display: block; margin-bottom: 15px;">🔨</span>
              <p style="margin: 0; font-size: 16px; color: #2d8f2d;">
                <strong>Meșterii noștri sunt la lucru!</strong><br>
                <em>Fiecare piesă din comanda ta este lucrată manual cu pasiune și atenție la detalii. 
                Procesul artizanal necesită timp pentru a asigura calitatea superioară pe care o meriți.</em>
              </p>
            </div>
            ` : ''}
            
            ${status === 'shipped' ? `
            <div class="artisan-message">
              <span style="font-size: 24px; display: block; margin-bottom: 15px;">🚛</span>
              <p style="margin: 0; font-size: 16px; color: #2d8f2d;">
                <strong>Comanda ta este în drum spre tine!</strong><br>
                <em>Produsele tale handmade au părăsit atelierul nostru și sunt acum în grija curierului. 
                Curând vei putea admira de aproape meșteșugul autentic românesc.</em>
              </p>
            </div>
            ` : ''}
            
            ${status === 'completed' || status === 'delivered' ? `
            <div class="artisan-message">
              <span style="font-size: 24px; display: block; margin-bottom: 15px;">🎉</span>
              <p style="margin: 0; font-size: 16px; color: #2d8f2d;">
                <strong>Felicitări! Comanda ta este finalizată!</strong><br>
                <em>Sperăm că te bucuri de noile tale comori handmade. Fiecare produs poartă în sine 
                sufletul meșterului și tradiția strămoșească română.</em>
              </p>
            </div>
            ` : ''}
            
            <div class="cultural-quote">
              <span style="font-size: 20px;">🎨</span><br>
              <em>"Măestria nu se măsoară în grabă, ci în pasiunea pusă în fiecare lovitură de daltă."</em><br>
              <small style="color: #999;">- Înțelepciune din atelierele românești -</small>
            </div>
            
            <div class="decorative-border"></div>
            
            <div style="text-align: center; margin: 35px 0;">
              <a href="${this.env.FRONTEND_URL}/account/orders/${order.id}" class="button">
                👁️ Vezi Detaliile Complete
              </a>
            </div>
            
            <div class="contact-section">
              <h4 style="color: #8b4513; margin: 0 0 15px 0;">🤝 Ai întrebări sau feedback?</h4>
              <p style="margin: 0 0 10px 0; color: #666; font-size: 15px;">
                Echipa noastră de artizani este mereu disponibilă pentru tine
              </p>
              <p style="margin: 0; color: #8b4513; font-weight: 600;">
                📧 <a href="mailto:<EMAIL>"><EMAIL></a> |
                📞 <a href="tel:+***********">+40 ***********</a>
              </p>
            </div>
            
            <div style="text-align: center; margin: 40px 0; padding: 25px; background: linear-gradient(135deg, #fff8dc 0%, #f5f5dc 100%); border-radius: 12px;">
              <p style="margin: 0 0 15px 0; font-size: 16px; color: #8b4513;">
                <strong>Cu recunoștință pentru încrederea acordată,</strong>
              </p>
              <p style="margin: 0; font-size: 18px; color: #d4af37; font-weight: bold;">
                🏺 Echipa Atelierului Meșterilor Români 🏺
              </p>
              <p style="margin: 10px 0 0 0; font-size: 14px; color: #999;">
                Unde tradiția întâlnește pasiunea pentru meșteșug
              </p>
            </div>
          </div>
          
          <div class="footer">
            <div style="font-size: 20px; margin-bottom: 15px;">🇷🇴 🏺 ♟️ 🧵 🎨 🇷🇴</div>
            <div style="font-size: 18px; font-weight: bold; color: #8b4513; margin-bottom: 10px;">
              Atelierul Meșterilor Români
            </div>
            <div style="font-style: italic; color: #5a5a5a; margin-bottom: 15px;">
              "Tradiția strămoșească prin mâinile noastre"
            </div>
            <p style="margin: 15px 0 5px 0; font-size: 12px; color: #999;">
              Acest email a fost trimis către ${customer.email}<br>
              © ${new Date().getFullYear()} Handmade in RO. Toate drepturile rezervate.
            </p>
            <div style="margin-top: 20px; padding-top: 15px; border-top: 1px solid #d0c9b8;">
              <span style="font-size: 11px; color: #999;">
                Artizanat autentic românesc • Din generație în generație • Cu dragoste pentru tradiție
              </span>
            </div>
          </div>
        </div>
      </body>
      </html>
    `;

    const text = `
${statusIcon} ACTUALIZARE STATUS COMANDĂ - Atelierul Meșterilor Români

Dragă ${customer.first_name || 'Stimate Client'},

📋 COMANDA #${order.display_id}
Status actual: ${romanianStatus}
Data actualizării: ${new Date().toLocaleDateString('ro-RO')}

🎯 VEȘTI BUNE DESPRE COMANDA TA!
${message}

${status === 'processing' ? `
🔨 MEȘTERII NOȘTRI SUNT LA LUCRU!
Fiecare piesă din comanda ta este lucrată manual cu pasiune și atenție la detalii. Procesul artizanal necesită timp pentru a asigura calitatea superioară pe care o meriți.
` : ''}

${status === 'shipped' ? `
🚛 COMANDA TA ESTE ÎN DRUM SPRE TINE!
Produsele tale handmade au părăsit atelierul nostru și sunt acum în grija curierului. Curând vei putea admira de aproape meșteșugul autentic românesc.
` : ''}

${status === 'completed' || status === 'delivered' ? `
🎉 FELICITĂRI! COMANDA TA ESTE FINALIZATĂ!
Sperăm că te bucuri de noile tale comori handmade. Fiecare produs poartă în sine sufletul meșterului și tradiția strămoșească română.
` : ''}

🎨 "Măestria nu se măsoară în grabă, ci în pasiunea pusă în fiecare lovitură de daltă."
- Înțelepciune din atelierele românești -

Vezi detaliile complete: ${this.env.FRONTEND_URL}/account/orders/${order.id}

🤝 AI ÎNTREBĂRI SAU FEEDBACK?
Echipa noastră de artizani este mereu disponibilă pentru tine
Contact: <EMAIL> | +40 ***********

Cu recunoștință pentru încrederea acordată,
🏺 Echipa Atelierului Meșterilor Români 🏺
"Unde tradiția întâlnește pasiunea pentru meșteșug"

🇷🇴 Atelierul Meșterilor Români 🇷🇴
"Tradiția strămoșească prin mâinile noastre"
© ${new Date().getFullYear()} Handmade in RO. Toate drepturile rezervate.
    `;

    return { 
      subject: `${statusIcon} Comanda #${order.display_id} - ${romanianStatus} - Atelierul Meșterilor Români`, 
      html, 
      text 
    };
  }

  private generateShippingSection(order: any): string {
    // Check for easybox delivery information from metadata
    const easyboxInfo = order.easybox_delivery || 
                       (order.metadata?.easybox_delivery) ||
                       (order.metadata?.easybox_locker_id ? {
                         locker_id: order.metadata.easybox_locker_id,
                         locker_name: order.metadata.easybox_locker_name,
                         locker_address: order.metadata.easybox_locker_address,
                         locker_city: order.metadata.easybox_locker_city,
                         locker_county: order.metadata.easybox_locker_county,
                         full_address: `${order.metadata.easybox_locker_name}, ${order.metadata.easybox_locker_address}, ${order.metadata.easybox_locker_city}, ${order.metadata.easybox_locker_county}`
                       } : null);
    
    const isEasyboxDelivery = Boolean(easyboxInfo);
    
    if (isEasyboxDelivery && easyboxInfo) {
      // EasyBox delivery section
      return `
        <div style="background: linear-gradient(135deg, #ff7f00 0%, #ff9500 100%); padding: 30px; margin: 25px 0; border-radius: 15px; color: white; text-align: center; box-shadow: 0 6px 20px rgba(255, 127, 0, 0.3);">
          <div style="font-size: 48px; margin-bottom: 15px;">📦</div>
          <h3 style="color: white; margin: 0 0 15px 0; font-size: 24px; text-shadow: 1px 1px 2px rgba(0,0,0,0.3);">
            🚚 Livrare EasyBox 🚚
          </h3>
          <p style="margin: 0 0 20px 0; font-size: 16px; opacity: 0.95; text-shadow: 1px 1px 2px rgba(0,0,0,0.2);">
            Comanda ta va fi livrată la un locker EasyBox pentru preluare ușoară și sigură!
          </p>
        </div>
        
        <div style="background: linear-gradient(135deg, #fff4e6 0%, #ffe6cc 100%); padding: 25px; margin: 20px 0; border-radius: 12px; border: 2px solid #ff7f00; box-shadow: 0 4px 12px rgba(255, 127, 0, 0.15);">
          <h4 style="color: #cc5500; margin: 0 0 20px 0; font-size: 18px; display: flex; align-items: center; gap: 10px;">
            <span style="font-size: 24px;">📍</span>
            Detalii Locker EasyBox
          </h4>
          
          <div style="background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #ff7f00; margin-bottom: 15px;">
            <div style="font-size: 16px; font-weight: bold; color: #cc5500; margin-bottom: 8px;">
              ${easyboxInfo.locker_name}
            </div>
            <div style="color: #333; margin-bottom: 4px;">${easyboxInfo.locker_address}</div>
            <div style="color: #333; margin-bottom: 10px;">${easyboxInfo.locker_city}, ${easyboxInfo.locker_county}</div>
            <div style="background: #fff4e6; padding: 8px 12px; border-radius: 6px; font-size: 14px; color: #cc5500;">
              <strong>ID Locker:</strong> ${easyboxInfo.locker_id}
            </div>
          </div>
          
          <div style="background: #e6f7ff; padding: 20px; border-radius: 8px; border-left: 4px solid #1890ff;">
            <h5 style="color: #0066cc; margin: 0 0 15px 0; font-size: 16px; display: flex; align-items: center; gap: 8px;">
              <span style="font-size: 20px;">ℹ️</span>
              Instrucțiuni de preluare:
            </h5>
            <div style="color: #333; line-height: 1.6; font-size: 14px;">
              <div style="margin-bottom: 8px;">🔔 <strong>Vei primi SMS cu codul de deschidere</strong> când pachetul ajunge la locker</div>
              <div style="margin-bottom: 8px;">⏰ <strong>Ai 72 de ore pentru preluare</strong> din momentul primirii SMS-ului</div>
              <div style="margin-bottom: 8px;">🔓 <strong>Folosește codul</strong> pentru a deschide locker-ul și a-ți prelua pachetul</div>
              <div>📱 <strong>Păstrează telefonul la îndemână</strong> pentru a primi notificarea</div>
            </div>
          </div>
          
          <div style="background: #f6ffed; padding: 15px; border-radius: 8px; margin-top: 15px; border: 1px solid #b7eb8f; text-align: center;">
            <div style="color: #52c41a; font-weight: bold; margin-bottom: 5px;">
              ✅ Avantajele EasyBox:
            </div>
            <div style="color: #389e0d; font-size: 14px; line-height: 1.5;">
              Securitate maximă • Disponibil 24/7 • Fără așteptare • Livrare rapidă
            </div>
          </div>
        </div>
      `;
    } else {
      // Standard shipping address section
      const shippingAddress = order.shipping_address;
      if (!shippingAddress) {
        return `
          <div style="background: #f8f9fa; padding: 20px; margin: 25px 0; border-radius: 8px; border-left: 4px solid #6c757d;">
            <h4 style="color: #6c757d; margin: 0 0 10px 0; font-size: 18px;">📦 Informații de livrare</h4>
            <p style="color: #6c757d; margin: 0;">Adresa de livrare va fi confirmată în curând.</p>
          </div>
        `;
      }
      
      return `
        <div style="background: linear-gradient(135deg, #e6f3ff 0%, #cce7ff 100%); padding: 25px; margin: 25px 0; border-radius: 12px; border: 2px solid #4682b4; box-shadow: 0 4px 12px rgba(70, 130, 180, 0.15);">
          <h4 style="color: #2c5aa0; margin: 0 0 20px 0; font-size: 18px; display: flex; align-items: center; gap: 10px;">
            <span style="font-size: 24px;">🏠</span>
            Adresa de Livrare
          </h4>
          
          <div style="background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #4682b4;">
            <div style="font-size: 16px; font-weight: bold; color: #2c5aa0; margin-bottom: 8px;">
              ${shippingAddress.first_name || ''} ${shippingAddress.last_name || ''}
            </div>
            ${shippingAddress.company ? `<div style="color: #333; margin-bottom: 4px;">${shippingAddress.company}</div>` : ''}
            <div style="color: #333; margin-bottom: 4px;">${shippingAddress.address_1 || ''}</div>
            ${shippingAddress.address_2 ? `<div style="color: #333; margin-bottom: 4px;">${shippingAddress.address_2}</div>` : ''}
            <div style="color: #333; margin-bottom: 4px;">
              ${shippingAddress.postal_code || ''} ${shippingAddress.city || ''}${shippingAddress.province ? `, ${shippingAddress.province}` : ''}
            </div>
            <div style="color: #333;">${shippingAddress.country_code?.toUpperCase() || ''}</div>
            ${shippingAddress.phone ? `<div style="color: #666; margin-top: 8px; font-size: 14px;">📞 ${shippingAddress.phone}</div>` : ''}
          </div>
          
          <div style="background: #f0f8ff; padding: 15px; border-radius: 8px; margin-top: 15px; border: 1px solid #b3d9ff; text-align: center;">
            <div style="color: #2c5aa0; font-weight: bold; margin-bottom: 5px;">
              🚚 Livrare Standard
            </div>
            <div style="color: #1e3a8a; font-size: 14px;">
              Pachetul va fi livrat la adresa specificată în timpul programului de lucru
            </div>
          </div>
        </div>
      `;
    }
  }

  private generateShippingTextSection(order: any): string {
    // Check for easybox delivery information from metadata
    const easyboxInfo = order.easybox_delivery || 
                       (order.metadata?.easybox_delivery) ||
                       (order.metadata?.easybox_locker_id ? {
                         locker_id: order.metadata.easybox_locker_id,
                         locker_name: order.metadata.easybox_locker_name,
                         locker_address: order.metadata.easybox_locker_address,
                         locker_city: order.metadata.easybox_locker_city,
                         locker_county: order.metadata.easybox_locker_county,
                         full_address: `${order.metadata.easybox_locker_name}, ${order.metadata.easybox_locker_address}, ${order.metadata.easybox_locker_city}, ${order.metadata.easybox_locker_county}`
                       } : null);
    
    const isEasyboxDelivery = Boolean(easyboxInfo);
    
    if (isEasyboxDelivery && easyboxInfo) {
      return `
📦 LIVRARE EASYBOX:
🚚 Comanda ta va fi livrată la un locker EasyBox pentru preluare ușoară și sigură!

📍 DETALII LOCKER:
• Nume: ${easyboxInfo.locker_name}
• Adresă: ${easyboxInfo.locker_address}
• Localitatea: ${easyboxInfo.locker_city}, ${easyboxInfo.locker_county}
• ID Locker: ${easyboxInfo.locker_id}

ℹ️ INSTRUCȚIUNI PRELUARE:
🔔 Vei primi SMS cu codul de deschidere când pachetul ajunge
⏰ Ai 72 de ore pentru preluare din momentul primirii SMS-ului
🔓 Folosește codul pentru a deschide locker-ul
📱 Păstrează telefonul la îndemână pentru notificare

✅ AVANTAJE EASYBOX:
Securitate maximă • Disponibil 24/7 • Fără așteptare • Livrare rapidă`;
    } else {
      const shippingAddress = order.shipping_address;
      if (!shippingAddress) {
        return `
📦 INFORMAȚII LIVRARE:
Adresa de livrare va fi confirmată în curând.`;
      }
      
      return `
🏠 ADRESA DE LIVRARE:
${shippingAddress.first_name || ''} ${shippingAddress.last_name || ''}
${shippingAddress.company ? `${shippingAddress.company}\n` : ''}${shippingAddress.address_1 || ''}
${shippingAddress.address_2 ? `${shippingAddress.address_2}\n` : ''}${shippingAddress.postal_code || ''} ${shippingAddress.city || ''}${shippingAddress.province ? `, ${shippingAddress.province}` : ''}
${shippingAddress.country_code?.toUpperCase() || ''}
${shippingAddress.phone ? `📞 ${shippingAddress.phone}` : ''}

🚚 LIVRARE STANDARD:
Pachetul va fi livrat la adresa specificată în timpul programului de lucru`;
    }
  }

  private getAdminOrderNotificationTemplate(order: any, customer: any): EmailTemplate {
    const itemsHtml = order.items.map((item: any) => `
      <tr>
        <td style="padding: 12px; border-bottom: 1px solid #e0e0e0;">
          ${item.product_title || item.title || 'Product'}
          ${item.variant_title ? `<br><span style="font-size: 12px; color: #666;">${item.variant_title}</span>` : ''}
          ${item.sku ? `<br><span style="font-size: 11px; color: #999;">SKU: ${item.sku}</span>` : ''}
        </td>
        <td style="padding: 12px; border-bottom: 1px solid #e0e0e0; text-align: center;">
          ${item.quantity}
        </td>
        <td style="padding: 12px; border-bottom: 1px solid #e0e0e0; text-align: right;">
          ${(item.unit_price * item.quantity).toFixed(2)} lei
        </td>
      </tr>
    `).join('');

    const subtotal = (order.subtotal || order.subtotal_amount || 0).toFixed(2);
    const shippingTotal = (order.shipping_total || order.shipping_amount || 0).toFixed(2);
    const taxTotal = (order.tax_total || order.tax_amount || 0).toFixed(2);
    const total = (order.total || order.total_amount || 0).toFixed(2);

    const customerInfo = customer || order.customer || {};
    const shippingAddress = order.shipping_address ? (typeof order.shipping_address === 'string' ? JSON.parse(order.shipping_address) : order.shipping_address) : {};
    const billingAddress = order.billing_address ? (typeof order.billing_address === 'string' ? JSON.parse(order.billing_address) : order.billing_address) : {};

    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Comandă Nouă - Admin Handmade in RO</title>
        <style>
          body {
            font-family: 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
            margin: 0;
            padding: 20px;
          }
          .email-container {
            max-width: 700px;
            margin: 0 auto;
            background: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
          }
          .header {
            background: #dc3545;
            color: #fff;
            padding: 25px;
            text-align: center;
          }
          .main-content {
            padding: 30px;
          }
          .alert-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
            border-left: 4px solid #f39c12;
          }
          .customer-info, .shipping-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
            margin: 20px 0;
          }
          h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
          }
          h2 {
            color: #dc3545;
            font-size: 18px;
            margin: 20px 0 15px 0;
          }
          table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            overflow: hidden;
          }
          th {
            background: #dc3545;
            color: white;
            padding: 12px;
            text-align: left;
            font-weight: 500;
          }
          .total-row {
            background: #f8f9fa;
            font-weight: 600;
          }
          .action-required {
            background: #e8f5e8;
            padding: 20px;
            border-radius: 6px;
            margin: 20px 0;
            border-left: 4px solid #28a745;
          }
        </style>
      </head>
      <body>
        <div class="email-container">
          <div class="header">
            <h1>🛍️ Comandă Nouă #${order.number || order.display_id}</h1>
            <p style="margin: 10px 0 0 0; font-size: 18px; opacity: 0.9;">
              Total: ${total} lei
            </p>
          </div>
          
          <div class="main-content">
            <div class="alert-box">
              <strong>📋 Comandă primită la:</strong> ${new Date(order.created_at).toLocaleString('ro-RO', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
              })}
            </div>

            <div class="customer-info">
              <h2>👤 Informații Client</h2>
              <p><strong>Nume:</strong> ${customerInfo.first_name || billingAddress.first_name || ''} ${customerInfo.last_name || billingAddress.last_name || ''}</p>
              <p><strong>Email:</strong> ${order.email || customerInfo.email || ''}</p>
              <p><strong>Telefon:</strong> ${customerInfo.phone || billingAddress.phone || shippingAddress.phone || 'Nu a fost furnizat'}</p>
              ${customerInfo.id ? `<p><strong>ID Client:</strong> ${customerInfo.id}</p>` : '<p><em>Client fără cont</em></p>'}
            </div>

            ${Object.keys(shippingAddress).length > 0 ? `
            <div class="shipping-info">
              <h2>🚚 Adresa de Livrare</h2>
              <p><strong>${shippingAddress.first_name || ''} ${shippingAddress.last_name || ''}</strong></p>
              ${shippingAddress.company ? `<p>${shippingAddress.company}</p>` : ''}
              <p>${shippingAddress.address_1 || ''}</p>
              ${shippingAddress.address_2 ? `<p>${shippingAddress.address_2}</p>` : ''}
              <p>${shippingAddress.postal_code || ''} ${shippingAddress.city || ''}${shippingAddress.province ? `, ${shippingAddress.province}` : ''}</p>
              <p>${shippingAddress.country_code?.toUpperCase() || ''}</p>
              ${shippingAddress.phone ? `<p><strong>Tel:</strong> ${shippingAddress.phone}</p>` : ''}
            </div>
            ` : ''}
            
            <h2>📦 Produse Comandate</h2>
            <table>
              <thead>
                <tr>
                  <th>Produs</th>
                  <th style="text-align: center;">Cantitate</th>
                  <th style="text-align: right;">Preț</th>
                </tr>
              </thead>
              <tbody>
                ${itemsHtml}
              </tbody>
              <tfoot>
                <tr>
                  <td style="padding: 12px; text-align: right;" colspan="2">
                    <strong>Subtotal:</strong>
                  </td>
                  <td style="padding: 12px; text-align: right;">
                    <strong>${subtotal} lei</strong>
                  </td>
                </tr>
                <tr>
                  <td style="padding: 8px 12px; text-align: right;" colspan="2">
                    Transport:
                  </td>
                  <td style="padding: 8px 12px; text-align: right;">
                    ${shippingTotal} lei
                  </td>
                </tr>
                <tr>
                  <td style="padding: 8px 12px; text-align: right;" colspan="2">
                    TVA:
                  </td>
                  <td style="padding: 8px 12px; text-align: right;">
                    ${taxTotal} lei
                  </td>
                </tr>
                <tr class="total-row">
                  <td style="padding: 12px; text-align: right; font-size: 18px;" colspan="2">
                    <strong>TOTAL:</strong>
                  </td>
                  <td style="padding: 12px; text-align: right; font-size: 18px;">
                    <strong>${total} lei</strong>
                  </td>
                </tr>
              </tfoot>
            </table>

            <div class="action-required">
              <h2>✅ Acțiuni necesare:</h2>
              <ul style="margin: 10px 0; padding-left: 20px;">
                <li>Verifică disponibilitatea produselor</li>
                <li>Pregătește comanda pentru expediere</li>
                <li>Actualizează statusul comenzii în admin</li>
                <li>Adaugă detalii de tracking când expedii</li>
              </ul>
            </div>

            <p style="text-align: center; margin: 30px 0;">
              <a href="${this.env.FRONTEND_URL || 'https://admin.handmadein.ro'}/admin/orders/${order.id}"
                 style="background: #dc3545; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: 500;">
                Vezi comanda în Admin
              </a>
            </p>
          </div>
        </div>
      </body>
      </html>
    `;

    const text = `
COMANDĂ NOUĂ #${order.number || order.display_id} - Handmade in RO

📋 Comandă primită la: ${new Date(order.created_at).toLocaleString('ro-RO', {
  weekday: 'long',
  year: 'numeric',
  month: 'long',
  day: 'numeric',
  hour: '2-digit',
  minute: '2-digit'
})}

👤 INFORMAȚII CLIENT:
• Nume: ${customerInfo.first_name || billingAddress.first_name || ''} ${customerInfo.last_name || billingAddress.last_name || ''}
• Email: ${order.email || customerInfo.email || ''}
• Telefon: ${customerInfo.phone || billingAddress.phone || shippingAddress.phone || 'Nu a fost furnizat'}
${customerInfo.id ? `• ID Client: ${customerInfo.id}` : '• Client fără cont'}

${Object.keys(shippingAddress).length > 0 ? `
🚚 ADRESA DE LIVRARE:
${shippingAddress.first_name || ''} ${shippingAddress.last_name || ''}
${shippingAddress.company ? `${shippingAddress.company}\n` : ''}${shippingAddress.address_1 || ''}
${shippingAddress.address_2 ? `${shippingAddress.address_2}\n` : ''}${shippingAddress.postal_code || ''} ${shippingAddress.city || ''}${shippingAddress.province ? `, ${shippingAddress.province}` : ''}
${shippingAddress.country_code?.toUpperCase() || ''}
${shippingAddress.phone ? `Tel: ${shippingAddress.phone}` : ''}
` : ''}

📦 PRODUSE COMANDATE:
${order.items.map((item: any) => `• ${item.product_title || item.title || 'Product'}${item.variant_title ? ` (${item.variant_title})` : ''} x${item.quantity} - ${(item.unit_price * item.quantity).toFixed(2)} lei${item.sku ? ` [${item.sku}]` : ''}`).join('\n')}

💰 TOTAL COMANDĂ:
• Subtotal: ${subtotal} lei
• Transport: ${shippingTotal} lei
• TVA: ${taxTotal} lei
• TOTAL: ${total} lei

✅ ACȚIUNI NECESARE:
• Verifică disponibilitatea produselor
• Pregătește comanda pentru expediere
• Actualizează statusul comenzii în admin
• Adaugă detalii de tracking când expedii

Vezi comanda în Admin: ${this.env.FRONTEND_URL || 'https://admin.handmadein.ro'}/admin/orders/${order.id}
    `;

    return {
      subject: `🛍️ Comandă Nouă #${order.number || order.display_id} - ${total} lei`,
      html,
      text
    };
  }
}
import { Context, Next } from 'hono';
import { CacheService } from '../services/cache';

export interface CacheMiddlewareOptions {
  ttl?: number;
  keyGenerator?: (c: Context) => string;
  skipCache?: (c: Context) => boolean;
  onCacheHit?: (c: Context, data: any) => void;
  onCacheMiss?: (c: Context) => void;
  asyncCache?: boolean; // PERFORMANCE: Enable async cache operations
  bypassForCritical?: boolean; // PERFORMANCE: Bypass cache for critical operations
}

/**
 * Cache middleware for Hono routes
 * PERFORMANCE: Optimized with async operations and critical path bypass
 * Caches GET requests only and skips cache for authenticated requests unless specified
 */
export function cacheMiddleware(options: CacheMiddlewareOptions = {}) {
  const {
    asyncCache = true,
    bypassForCritical = true,
    ttl = 3600
  } = options;

  return async (c: Context, next: Next) => {
    // Only cache GET requests
    if (c.req.method !== 'GET') {
      return next();
    }

    // PERFORMANCE: Bypass cache for critical operations (checkout, payment, etc.)
    if (bypassForCritical) {
      const url = new URL(c.req.url);
      const criticalPaths = ['/checkout', '/payment', '/cart/complete', '/auth'];
      if (criticalPaths.some(path => url.pathname.includes(path))) {
        return next();
      }
    }

    // Skip cache if specified
    if (options.skipCache && options.skipCache(c)) {
      return next();
    }

    // Skip cache for authenticated requests by default (unless overridden)
    const authHeader = c.req.header('Authorization');
    if (authHeader && !options.skipCache) {
      return next();
    }

    // Generate cache key
    const cacheKey = options.keyGenerator ? 
      options.keyGenerator(c) : 
      generateDefaultCacheKey(c);

    // Initialize cache service
    const cache = new CacheService(c.env as any);

    try {
      // PERFORMANCE: Async cache operations for better performance
      if (asyncCache) {
        // Non-blocking cache check
        const cachePromise = cache.get(cacheKey);

        // Start processing request immediately
        const requestPromise = (async () => {
          await next();
          return c.res.clone();
        })();

        // Check cache result
        const cachedData = await cachePromise;
        if (cachedData) {
          options.onCacheHit && options.onCacheHit(c, cachedData);
          console.log(`Cache HIT: ${cacheKey}`);
          return c.json(cachedData);
        }

        // Wait for request to complete
        const response = await requestPromise;

        // Cache response asynchronously (don't wait)
        if (response.status === 200) {
          response.json().then(responseData => {
            cache.set(cacheKey, responseData, { ttl }).catch(error => {
              console.error('Failed to cache response:', error);
            });
          }).catch(() => {
            // Ignore JSON parsing errors for caching
          });
        }

        return; // Response already sent
      } else {
        // Traditional synchronous cache approach
        const cachedData = await cache.get(cacheKey);

        if (cachedData) {
          options.onCacheHit && options.onCacheHit(c, cachedData);
          console.log(`Cache HIT: ${cacheKey}`);
          return c.json(cachedData);
        }

        // Cache miss - continue with request
        options.onCacheMiss && options.onCacheMiss(c);
        console.log(`Cache MISS: ${cacheKey}`);

        await next();

        // Cache the response if it was successful
        const response = c.res.clone();
        if (response.status === 200) {
          try {
            const responseData = await response.json();
            await cache.set(cacheKey, responseData, { ttl });
            console.log(`Cached response: ${cacheKey}`);
          } catch (error) {
            console.error('Failed to cache response:', error);
          }
        }
      }
    } catch (error) {
      console.error('Cache middleware error:', error);
      // Continue without cache on error
      return next();
    }
  };
}

/**
 * Generate a default cache key based on the request
 */
function generateDefaultCacheKey(c: Context): string {
  const url = new URL(c.req.url);
  const path = url.pathname;
  const searchParams = new URLSearchParams(url.search);
  
  // Sort query parameters for consistent cache keys
  const sortedParams = Array.from(searchParams.entries())
    .sort(([a], [b]) => a.localeCompare(b))
    .map(([key, value]) => `${key}=${value}`)
    .join('&');
  
  return `route:${path}${sortedParams ? `:${sortedParams}` : ''}`;
}

/**
 * Cache invalidation helper
 */
export async function invalidateRouteCache(
  c: Context, 
  patterns: string[]
): Promise<void> {
  const cache = new CacheService(c.env as any);
  
  for (const pattern of patterns) {
    try {
      await cache.invalidatePattern(pattern);
      console.log(`Invalidated cache pattern: ${pattern}`);
    } catch (error) {
      console.error(`Failed to invalidate cache pattern ${pattern}:`, error);
    }
  }
}

/**
 * Specific cache key generators for common patterns
 */
export const cacheKeyGenerators = {
  products: (c: Context) => {
    const page = c.req.query('page') || '1';
    const limit = c.req.query('limit') || '20';
    const search = c.req.query('search') || '';
    const collection_id = c.req.query('collection_id') || '';
    const category_id = c.req.query('category_id') || '';
    const sort = c.req.query('sort') || 'created_at';
    const order = c.req.query('order') || 'desc';
    
    return `products:list:${page}:${limit}:${search}:${collection_id}:${category_id}:${sort}:${order}`;
  },
  
  productDetail: (c: Context) => {
    const id = c.req.param('id');
    const currency = c.req.query('currency_code') || 'RON';
    const region = c.req.query('region_id') || '';
    
    return `product:${id}:${currency}:${region}`;
  },
  
  collections: (c: Context) => {
    const page = c.req.query('page') || '1';
    const limit = c.req.query('limit') || '20';
    
    return `collections:list:${page}:${limit}`;
  },
  
  paymentMethods: (c: Context) => {
    const cartId = c.req.param('cartId');
    return `payment_methods:${cartId || 'default'}`;
  },
  
  shippingMethods: (c: Context) => {
    const region = c.req.query('region_id') || 'default';
    return `shipping_methods:${region}`;
  }
};

# Contact Form and Newsletter Implementation

This document outlines the complete implementation of contact form and newsletter functionality migrated from AWS Lambdas to Cloudflare Workers.

## Overview

This implementation replaces the AWS Lambda functions for:
- Contact form handling (`aws-lambdas/contact-form-handler`)
- Newsletter subscription (`aws-lambdas/newsletter-subscription`)

Both functionalities now run on Cloudflare Workers using the Resend email service for reliable email delivery.

## Features

### Contact Form
- **Email Validation**: Server-side validation with Zod schemas
- **Resend Integration**: Emails sent via Resend API with HTML and text formats
- **Database Storage**: Contact submissions stored in D1 database
- **Rich Email Templates**: Beautiful HTML emails with proper formatting
- **Error Handling**: Comprehensive error handling with user-friendly messages
- **Reply-To Support**: Automatic reply-to setup for easy responses

### Newsletter
- **Resend Audience Management**: Automatic audience subscription/unsubscription
- **Welcome Emails**: Automatic welcome emails with Romanian content
- **Duplicate Prevention**: Prevents duplicate subscriptions
- **Unsubscribe Support**: Built-in unsubscribe functionality
- **Admin Statistics**: Subscriber stats and management endpoints

## API Endpoints

### Contact Form
- **POST** `/contact` - Submit contact form

#### Request Body
```json
{
  "name": "<PERSON>e",
  "email": "<EMAIL>",
  "message": "Your message here",
  "phone": "+40123456789", // optional
  "subject": "Custom subject" // optional
}
```

#### Response
```json
{
  "success": true,
  "message": "Mesajul tău a fost trimis cu succes. Îți vom răspunde în cel mai scurt timp!",
  "id": "resend_email_id"
}
```

### Newsletter
- **POST** `/newsletter/subscribe` - Subscribe to newsletter
- **POST** `/newsletter/unsubscribe` - Unsubscribe from newsletter
- **GET** `/newsletter/stats` - Get subscriber statistics (admin)
- **GET** `/newsletter/subscribers` - Get all subscribers (admin)

#### Subscribe Request
```json
{
  "email": "<EMAIL>",
  "metadata": {} // optional
}
```

#### Subscribe Response
```json
{
  "success": true,
  "message": "Te-ai abonat cu succes la newsletter. Verifică-ți email-ul pentru confirmarea abonării."
}
```

## Database Schema

### Contact Submissions Table
```sql
CREATE TABLE contact_submissions (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL,
  email TEXT NOT NULL,
  phone TEXT,
  subject TEXT,
  message TEXT NOT NULL,
  created_at TEXT NOT NULL DEFAULT (datetime('now', 'utc')),
  updated_at TEXT NOT NULL DEFAULT (datetime('now', 'utc')),
  email_id TEXT, -- Resend email ID for tracking
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'processed', 'replied')),
  replied_at TEXT,
  replied_by TEXT,
  notes TEXT
);
```

## Environment Variables

The following environment variables must be configured in your Cloudflare Workers:

```bash
# Required for both contact and newsletter
RESEND_API_KEY=re_your_resend_api_key

# Required for newsletter
RESEND_AUDIENCE_ID=your_resend_audience_id

# Database
DB=your_d1_database_binding
```

## Email Templates

### Contact Form Email
- **From**: Contact Form <<EMAIL>>
- **To**: <EMAIL> (configurable)
- **Subject**: "Mesaj nou din formularul de contact" (or custom subject)
- **Features**: 
  - Professional HTML design
  - Clickable email and phone links
  - Romanian timezone formatting
  - XSS protection via HTML escaping

### Newsletter Welcome Email
- **From**: Newsletter <<EMAIL>>
- **Subject**: "Bine ai venit la newsletterul Handmade in RO!"
- **Content**: Romanian welcome message
- **Features**:
  - HTML and text versions
  - Automatic unsubscribe links
  - Brand-consistent styling

## Frontend Integration

### Contact Form (fe2)
- **Endpoint**: `POST /api/contact`
- **Proxy**: Routes to Cloudflare Workers `/contact`
- **Error Handling**: Proper HTTP status codes and error messages

### Newsletter (fe2)
- **Subscribe**: `POST /api/newsletter/subscribe`
- **Unsubscribe**: `POST /api/newsletter/unsubscribe`
- **Proxy**: Routes to Cloudflare Workers `/newsletter/*`

## Migration from AWS Lambda

### Changes Made
1. **Contact Form**:
   - Migrated from `aws-lambdas/contact-form-handler/index.js`
   - Enhanced email templates
   - Added database storage
   - Improved error handling

2. **Newsletter**:
   - Already implemented in Cloudflare Workers
   - Enhanced with proper Romanian content
   - Added admin functionality

3. **Frontend**:
   - Updated `fe2/server/api/contact.ts` to use Cloudflare backend
   - Created `fe2/server/api/newsletter/subscribe.post.ts`
   - Created `fe2/server/api/newsletter/unsubscribe.post.ts`

### Configuration Updates Needed
1. Update environment variables in Cloudflare Workers
2. Update frontend environment variable `CLOUDFLARE_BACKEND_URL`
3. Run database migration: `0018_create_contact_submissions_table.sql`
4. Configure Resend domain and audience

## Testing

### Contact Form Testing
```bash
# Test contact form submission
curl -X POST https://backend.handmadein.ro/contact \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test User",
    "email": "<EMAIL>",
    "message": "This is a test message"
  }'
```

### Newsletter Testing
```bash
# Test newsletter subscription
curl -X POST https://backend.handmadein.ro/newsletter/subscribe \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>"
  }'

# Test newsletter unsubscribe
curl -X POST https://backend.handmadein.ro/newsletter/unsubscribe \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>"
  }'
```

## Security Features

1. **Input Validation**: Zod schemas for all inputs
2. **XSS Prevention**: HTML escaping in email templates
3. **CORS Protection**: Configured allowed origins
4. **Email Validation**: Comprehensive email format validation
5. **Rate Limiting**: Can be added via Cloudflare Workers (future enhancement)

## Monitoring and Logging

- All operations are logged to Cloudflare Workers console
- Email IDs are stored for tracking delivery
- Contact submissions are stored in database for admin review
- Error logging with proper error messages

## Future Enhancements

1. **Admin Dashboard**: 
   - Contact form management interface
   - Newsletter subscriber management
   - Email template customization

2. **Advanced Features**:
   - Email templates in multiple languages
   - Automated responses
   - Integration with CRM systems
   - Advanced analytics

3. **Security Enhancements**:
   - Rate limiting
   - Spam protection
   - CAPTCHA integration

## Troubleshooting

### Common Issues

1. **Email not sending**: Check `RESEND_API_KEY` configuration
2. **Newsletter not working**: Verify `RESEND_AUDIENCE_ID` is correct
3. **Database errors**: Ensure D1 database is properly bound
4. **CORS issues**: Check allowed origins in CORS configuration

### Debugging
- Check Cloudflare Workers logs for detailed error information
- Verify environment variables are properly set
- Test API endpoints directly using curl or Postman
- Check Resend dashboard for email delivery status

## Migration Checklist

- [x] Create contact form handler in Cloudflare Workers
- [x] Create database migration for contact submissions
- [x] Update frontend API to use Cloudflare backend
- [x] Create newsletter API endpoints in frontend
- [x] Update environment variable bindings
- [x] Create comprehensive documentation
- [ ] Configure Resend API key and audience ID
- [ ] Run database migration
- [ ] Test contact form functionality
- [ ] Test newsletter functionality
- [ ] Update production environment variables
- [ ] Remove AWS Lambda dependencies 
import e from"node:process";globalThis._importMeta_={url:import.meta.url,env:e.env};import{tmpdir as t}from"node:os";import{Server as o}from"node:http";import{resolve as r,dirname as s,join as n}from"node:path";import a from"node:crypto";import{parentPort as i,threadId as c}from"node:worker_threads";import{defineEventHandler as d,handleCacheHeaders as l,splitCookiesString as u,createEvent as h,fetchWithEvent as m,isEvent as p,eventHandler as f,setHeaders as g,sendRedirect as y,proxyRequest as w,getRequestHeader as b,setResponseHeaders as _,setResponseStatus as v,send as C,getRequestHeaders as x,setResponseHeader as A,getRequestURL as k,getResponseHeader as j,getCookie as U,setCookie as S,getResponseStatus as P,createError as R,getQuery as T,readBody as E,lazyEventHand<PERSON> as $,useBase as O,createApp as N,createRouter as I,toNodeListener as z,getRouterParam as L,deleteCookie as D,parseCookies as B,getHeader as M,getResponseStatusText as H}from"file://C:/Users/<USER>/PhpstormProjects/ro/fe2/node_modules/h3/dist/index.mjs";import{escapeHtml as F}from"file://C:/Users/<USER>/PhpstormProjects/ro/fe2/node_modules/@vue/shared/dist/shared.cjs.js";import{createRenderer as q,getRequestDependencies as G,getPreloadLinks as W,getPrefetchLinks as J}from"file://C:/Users/<USER>/PhpstormProjects/ro/fe2/node_modules/vue-bundle-renderer/dist/runtime.mjs";import{renderToString as Q}from"file://C:/Users/<USER>/PhpstormProjects/ro/fe2/node_modules/vue/server-renderer/index.mjs";import{klona as K}from"file://C:/Users/<USER>/PhpstormProjects/ro/fe2/node_modules/klona/dist/index.mjs";import V,{defuFn as X}from"file://C:/Users/<USER>/PhpstormProjects/ro/fe2/node_modules/defu/dist/defu.mjs";import Y,{destr as Z}from"file://C:/Users/<USER>/PhpstormProjects/ro/fe2/node_modules/destr/dist/index.mjs";import{snakeCase as ee}from"file://C:/Users/<USER>/PhpstormProjects/ro/fe2/node_modules/scule/dist/index.mjs";import{createHead as te,propsToString as oe,renderSSRHead as re}from"file://C:/Users/<USER>/PhpstormProjects/ro/fe2/node_modules/unhead/dist/server.mjs";import{stringify as se,uneval as ne}from"file://C:/Users/<USER>/PhpstormProjects/ro/fe2/node_modules/devalue/index.js";import{isVNode as ae,toValue as ie,isRef as ce}from"file://C:/Users/<USER>/PhpstormProjects/ro/fe2/node_modules/vue/index.mjs";import{DeprecationsPlugin as de,PromisesPlugin as le,TemplateParamsPlugin as ue,AliasSortingPlugin as he}from"file://C:/Users/<USER>/PhpstormProjects/ro/fe2/node_modules/unhead/dist/plugins.mjs";import{createHooks as me}from"file://C:/Users/<USER>/PhpstormProjects/ro/fe2/node_modules/hookable/dist/index.mjs";import{createFetch as pe,Headers as fe}from"file://C:/Users/<USER>/PhpstormProjects/ro/fe2/node_modules/ofetch/dist/node.mjs";import{fetchNodeRequestHandler as ge,callNodeRequestHandler as ye}from"file://C:/Users/<USER>/PhpstormProjects/ro/fe2/node_modules/node-mock-http/dist/index.mjs";import{createStorage as we,prefixStorage as be}from"file://C:/Users/<USER>/PhpstormProjects/ro/fe2/node_modules/unstorage/dist/index.mjs";import _e from"file://C:/Users/<USER>/PhpstormProjects/ro/fe2/node_modules/unstorage/drivers/fs.mjs";import{digest as ve,hash as Ce}from"file://C:/Users/<USER>/PhpstormProjects/ro/fe2/node_modules/ohash/dist/index.mjs";import{toRouteMatcher as xe,createRouter as Ae}from"file://C:/Users/<USER>/PhpstormProjects/ro/fe2/node_modules/radix3/dist/index.mjs";import{readFile as ke}from"node:fs/promises";import je,{consola as Ue}from"file://C:/Users/<USER>/PhpstormProjects/ro/fe2/node_modules/consola/dist/index.mjs";import{ErrorParser as Se}from"file://C:/Users/<USER>/PhpstormProjects/ro/fe2/node_modules/youch-core/build/index.js";import{Youch as Pe}from"file://C:/Users/<USER>/PhpstormProjects/ro/fe2/node_modules/youch/build/index.js";import{SourceMapConsumer as Re}from"file://C:/Users/<USER>/PhpstormProjects/ro/fe2/node_modules/nitropack/node_modules/source-map/source-map.js";import{AsyncLocalStorage as Te}from"node:async_hooks";import{getContext as Ee}from"file://C:/Users/<USER>/PhpstormProjects/ro/fe2/node_modules/unctx/dist/index.mjs";import{captureRawStackTrace as $e,parseRawStackTrace as Oe}from"file://C:/Users/<USER>/PhpstormProjects/ro/fe2/node_modules/errx/dist/index.js";import{basename as Ne,isAbsolute as Ie}from"file://C:/Users/<USER>/PhpstormProjects/ro/fe2/node_modules/pathe/dist/index.mjs";import{getIcons as ze}from"file://C:/Users/<USER>/PhpstormProjects/ro/fe2/node_modules/@iconify/utils/lib/index.mjs";import{collections as Le}from"file://C:/Users/<USER>/PhpstormProjects/ro/fe2/.nuxt/nuxt-icon-server-bundle.mjs";import{walkResolver as De}from"file://C:/Users/<USER>/PhpstormProjects/ro/fe2/node_modules/unhead/dist/utils.mjs";import{fileURLToPath as Be}from"node:url";import{ipxFSStorage as Me,ipxHttpStorage as He,createIPX as Fe,createIPXH3Handler as qe}from"file://C:/Users/<USER>/PhpstormProjects/ro/fe2/node_modules/ipx/dist/index.mjs";const Ge=/#/g,We=/&/g,Je=/\//g,Qe=/=/g,Ke=/\+/g,Ve=/%5e/gi,Xe=/%60/gi,Ye=/%7c/gi,Ze=/%20/gi;function encodeQueryValue(e){return(t="string"==typeof e?e:JSON.stringify(e),encodeURI(""+t).replace(Ye,"|")).replace(Ke,"%2B").replace(Ze,"+").replace(Ge,"%23").replace(We,"%26").replace(Xe,"`").replace(Ve,"^").replace(Je,"%2F");var t}function encodeQueryKey(e){return encodeQueryValue(e).replace(Qe,"%3D")}function decode(e=""){try{return decodeURIComponent(""+e)}catch{return""+e}}function decodeQueryValue(e){return decode(e.replace(Ke," "))}function parseQuery(e=""){const t=Object.create(null);"?"===e[0]&&(e=e.slice(1));for(const o of e.split("&")){const e=o.match(/([^=]+)=?(.*)/)||[];if(e.length<2)continue;const r=decode(e[1].replace(Ke," "));if("__proto__"===r||"constructor"===r)continue;const s=decodeQueryValue(e[2]||"");void 0===t[r]?t[r]=s:Array.isArray(t[r])?t[r].push(s):t[r]=[t[r],s]}return t}function stringifyQuery(e){return Object.keys(e).filter((t=>void 0!==e[t])).map((t=>{return o=t,"number"!=typeof(r=e[t])&&"boolean"!=typeof r||(r=String(r)),r?Array.isArray(r)?r.map((e=>`${encodeQueryKey(o)}=${encodeQueryValue(e)}`)).join("&"):`${encodeQueryKey(o)}=${encodeQueryValue(r)}`:encodeQueryKey(o);var o,r})).filter(Boolean).join("&")}const et=/^[\s\w\0+.-]{2,}:([/\\]{1,2})/,tt=/^[\s\w\0+.-]{2,}:([/\\]{2})?/,ot=/^([/\\]\s*){2,}[^/\\]/,rt=/^\.?\//;function hasProtocol(e,t={}){return"boolean"==typeof t&&(t={acceptRelative:t}),t.strict?et.test(e):tt.test(e)||!!t.acceptRelative&&ot.test(e)}function withTrailingSlash(e="",t){return e.endsWith("/")?e:e+"/"}function withoutBase(e,t){if(!(o=t)||"/"===o)return e;var o;const r=function(e=""){return(function(e=""){return e.endsWith("/")}(e)?e.slice(0,-1):e)||"/"}(t);if(!e.startsWith(r))return e;const s=e.slice(r.length);return"/"===s[0]?s:"/"+s}function withQuery(e,t){const o=parseURL(e),r={...parseQuery(o.search),...t};return o.search=stringifyQuery(r),function(e){const t=e.pathname||"",o=e.search?(e.search.startsWith("?")?"":"?")+e.search:"",r=e.hash||"",s=e.auth?e.auth+"@":"",n=e.host||"",a=e.protocol||e[st]?(e.protocol||"")+"//":"";return a+s+n+t+o+r}(o)}function getQuery(e){return parseQuery(parseURL(e).search)}function joinURL(e,...t){let o=e||"";for(const e of t.filter((e=>function(e){return e&&"/"!==e}(e))))if(o){const t=e.replace(rt,"");o=withTrailingSlash(o)+t}else o=e;return o}function joinRelativeURL(...e){const t=/\/(?!\/)/,o=e.filter(Boolean),r=[];let s=0;for(const e of o)if(e&&"/"!==e)for(const[o,n]of e.split(t).entries())if(n&&"."!==n)if(".."!==n)1===o&&r[r.length-1]?.endsWith(":/")?r[r.length-1]+="/"+n:(r.push(n),s++);else{if(1===r.length&&hasProtocol(r[0]))continue;r.pop(),s--}let n=r.join("/");return s>=0?o[0]?.startsWith("/")&&!n.startsWith("/")?n="/"+n:o[0]?.startsWith("./")&&!n.startsWith("./")&&(n="./"+n):n="../".repeat(-1*s)+n,o[o.length-1]?.endsWith("/")&&!n.endsWith("/")&&(n+="/"),n}const st=Symbol.for("ufo:protocolRelative");function parseURL(e="",t){const o=e.match(/^[\s\0]*(blob:|data:|javascript:|vbscript:)(.*)/i);if(o){const[,e,t=""]=o;return{protocol:e.toLowerCase(),pathname:t,href:e+t,auth:"",host:"",search:"",hash:""}}if(!hasProtocol(e,{acceptRelative:!0}))return parsePath(e);const[,r="",s,n=""]=e.replace(/\\/g,"/").match(/^[\s\0]*([\w+.-]{2,}:)?\/\/([^/@]+@)?(.*)/)||[];let[,a="",i=""]=n.match(/([^#/?]*)(.*)?/)||[];"file:"===r&&(i=i.replace(/\/(?=[A-Za-z]:)/,""));const{pathname:c,search:d,hash:l}=parsePath(i);return{protocol:r.toLowerCase(),auth:s?s.slice(0,Math.max(0,s.length-1)):"",host:a,pathname:c,search:d,hash:l,[st]:!r}}function parsePath(e=""){const[t="",o="",r=""]=(e.match(/([^#?]*)(\?[^#]*)?(#.*)?/)||[]).splice(1);return{pathname:t,search:o,hash:r}}const nt=[{baseName:"server",dir:"C:/Users/<USER>/PhpstormProjects/ro/fe2/server/assets"}],at=we();for(const e of nt)at.mount(e.baseName,_e({base:e.dir,ignore:e?.ignore||[]}));const it=we({});function useStorage(e=""){return e?be(it,e):it}it.mount("/assets",at),it.mount("root",_e({driver:"fs",readOnly:!0,base:"C:/Users/<USER>/PhpstormProjects/ro/fe2",watchOptions:{ignored:[null]}})),it.mount("src",_e({driver:"fs",readOnly:!0,base:"C:/Users/<USER>/PhpstormProjects/ro/fe2/server",watchOptions:{ignored:[null]}})),it.mount("build",_e({driver:"fs",readOnly:!1,base:"C:/Users/<USER>/PhpstormProjects/ro/fe2/.nuxt"})),it.mount("cache",_e({driver:"fs",readOnly:!1,base:"C:/Users/<USER>/PhpstormProjects/ro/fe2/.nuxt/cache"})),it.mount("data",_e({driver:"fs",base:"C:/Users/<USER>/PhpstormProjects/ro/fe2/.data/kv"}));const ct=(()=>{class Hasher2{buff="";#e=new Map;write(e){this.buff+=e}dispatch(e){return this[null===e?"null":typeof e](e)}object(e){if(e&&"function"==typeof e.toJSON)return this.object(e.toJSON());const t=Object.prototype.toString.call(e);let o="";const r=t.length;o=r<10?"unknown:["+t+"]":t.slice(8,r-1),o=o.toLowerCase();let s=null;if(void 0!==(s=this.#e.get(e)))return this.dispatch("[CIRCULAR:"+s+"]");if(this.#e.set(e,this.#e.size),"undefined"!=typeof Buffer&&Buffer.isBuffer&&Buffer.isBuffer(e))return this.write("buffer:"),this.write(e.toString("utf8"));if("object"!==o&&"function"!==o&&"asyncfunction"!==o)this[o]?this[o](e):this.unknown(e,o);else{const t=Object.keys(e).sort(),o=[];this.write("object:"+(t.length+o.length)+":");const dispatchForKey=t=>{this.dispatch(t),this.write(":"),this.dispatch(e[t]),this.write(",")};for(const e of t)dispatchForKey(e);for(const e of o)dispatchForKey(e)}}array(e,t){if(t=void 0!==t&&t,this.write("array:"+e.length+":"),!t||e.length<=1){for(const t of e)this.dispatch(t);return}const o=new Map,r=e.map((e=>{const t=new Hasher2;t.dispatch(e);for(const[e,r]of t.#e)o.set(e,r);return t.toString()}));return this.#e=o,r.sort(),this.array(r,!1)}date(e){return this.write("date:"+e.toJSON())}symbol(e){return this.write("symbol:"+e.toString())}unknown(e,t){if(this.write(t),e)return this.write(":"),e&&"function"==typeof e.entries?this.array([...e.entries()],!0):void 0}error(e){return this.write("error:"+e.toString())}boolean(e){return this.write("bool:"+e)}string(e){this.write("string:"+e.length+":"),this.write(e)}function(e){this.write("fn:"),!function(e){if("function"!=typeof e)return!1;return"[native code] }"===Function.prototype.toString.call(e).slice(-15)}(e)?this.dispatch(e.toString()):this.dispatch("[native]")}number(e){return this.write("number:"+e)}null(){return this.write("Null")}undefined(){return this.write("Undefined")}regexp(e){return this.write("regex:"+e.toString())}arraybuffer(e){return this.write("arraybuffer:"),this.dispatch(new Uint8Array(e))}url(e){return this.write("url:"+e.toString())}map(e){this.write("map:");const t=[...e];return this.array(t,!1)}set(e){this.write("set:");const t=[...e];return this.array(t,!1)}bigint(e){return this.write("bigint:"+e.toString())}}for(const e of["uint8array","uint8clampedarray","unt8array","uint16array","unt16array","uint32array","unt32array","float32array","float64array"])Hasher2.prototype[e]=function(t){return this.write(e+":"),this.array([...t],!1)};return Hasher2})();function hash(e){return ve("string"==typeof e?e:function(e){const t=new ct;return t.dispatch(e),t.buff}(e)).replace(/[-_]/g,"").slice(0,10)}function defineCachedFunction(e,t={}){t={name:"_",base:"/cache",swr:!0,maxAge:1,...t};const o={},r=t.group||"nitro/functions",s=t.name||e.name||"_",n=t.integrity||hash([e,t]),a=t.validate||(e=>void 0!==e.value);return async(...i)=>{if(await(t.shouldBypassCache?.(...i)))return e(...i);const c=await(t.getKey||getKey)(...i),d=await(t.shouldInvalidateCache?.(...i)),l=await async function(e,i,c,d){const l=[t.base,r,s,e+".json"].filter(Boolean).join(":").replace(/:\/$/,":index");let u=await useStorage().getItem(l).catch((e=>{console.error("[cache] Cache read error.",e),useNitroApp().captureError(e,{event:d,tags:["cache"]})}))||{};if("object"!=typeof u){u={};const e=new Error("Malformed data read from cache.");console.error("[cache]",e),useNitroApp().captureError(e,{event:d,tags:["cache"]})}const h=1e3*(t.maxAge??0);h&&(u.expires=Date.now()+h);const m=c||u.integrity!==n||h&&Date.now()-(u.mtime||0)>h||!1===a(u),p=m?(async()=>{const r=o[e];r||(void 0!==u.value&&(t.staleMaxAge||0)>=0&&!1===t.swr&&(u.value=void 0,u.integrity=void 0,u.mtime=void 0,u.expires=void 0),o[e]=Promise.resolve(i()));try{u.value=await o[e]}catch(t){throw r||delete o[e],t}if(!r&&(u.mtime=Date.now(),u.integrity=n,delete o[e],!1!==a(u))){let e;t.maxAge&&!t.swr&&(e={ttl:t.maxAge});const o=useStorage().setItem(l,u,e).catch((e=>{console.error("[cache] Cache write error.",e),useNitroApp().captureError(e,{event:d,tags:["cache"]})}));d?.waitUntil&&d.waitUntil(o)}})():Promise.resolve();return void 0===u.value?await p:m&&d&&d.waitUntil&&d.waitUntil(p),t.swr&&!1!==a(u)?(p.catch((e=>{console.error("[cache] SWR handler error.",e),useNitroApp().captureError(e,{event:d,tags:["cache"]})})),u):p.then((()=>u))}(c,(()=>e(...i)),d,i[0]&&p(i[0])?i[0]:void 0);let u=l.value;return t.transform&&(u=await t.transform(l,...i)||u),u}}function getKey(...e){return e.length>0?hash(e):""}function escapeKey(e){return String(e).replace(/\W/g,"")}function defineCachedEventHandler(e,t={name:"_",base:"/cache",swr:!0,maxAge:1}){const o=(t.varies||[]).filter(Boolean).map((e=>e.toLowerCase())).sort(),r={...t,getKey:async e=>{const r=await(t.getKey?.(e));if(r)return escapeKey(r);const s=e.node.req.originalUrl||e.node.req.url||e.path;let n;try{n=escapeKey(decodeURI(parseURL(s).pathname)).slice(0,16)||"index"}catch{n="-"}return[`${n}.${hash(s)}`,...o.map((t=>[t,e.node.req.headers[t]])).map((([e,t])=>`${escapeKey(e)}.${hash(t)}`))].join(":")},validate:e=>!!e.value&&(!(e.value.code>=400)&&(void 0!==e.value.body&&("undefined"!==e.value.headers.etag&&"undefined"!==e.value.headers["last-modified"]))),group:t.group||"nitro/handlers",integrity:t.integrity||hash([e,t])},s=function(e,t={}){return defineCachedFunction(e,t)}((async s=>{const n={};for(const e of o){const t=s.node.req.headers[e];void 0!==t&&(n[e]=t)}const a=cloneWithProxy(s.node.req,{headers:n}),i={};let c;const d=cloneWithProxy(s.node.res,{statusCode:200,writableEnded:!1,writableFinished:!1,headersSent:!1,closed:!1,getHeader:e=>i[e],setHeader(e,t){return i[e]=t,this},getHeaderNames:()=>Object.keys(i),hasHeader:e=>e in i,removeHeader(e){delete i[e]},getHeaders:()=>i,end(e,t,o){return"string"==typeof e&&(c=e),"function"==typeof t&&t(),"function"==typeof o&&o(),this},write:(e,t,o)=>("string"==typeof e&&(c=e),"function"==typeof t&&t(void 0),"function"==typeof o&&o(),!0),writeHead(e,t){if(this.statusCode=e,t){if(Array.isArray(t)||"string"==typeof t)throw new TypeError("Raw headers  is not supported.");for(const e in t){const o=t[e];void 0!==o&&this.setHeader(e,o)}}return this}}),l=h(a,d);l.fetch=(e,t)=>m(l,e,t,{fetch:useNitroApp().localFetch}),l.$fetch=(e,t)=>m(l,e,t,{fetch:globalThis.$fetch}),l.waitUntil=s.waitUntil,l.context=s.context,l.context.cache={options:r};const u=await e(l)||c,p=l.node.res.getHeaders();p.etag=String(p.Etag||p.etag||`W/"${hash(u)}"`),p["last-modified"]=String(p["Last-Modified"]||p["last-modified"]||(new Date).toUTCString());const f=[];t.swr?(t.maxAge&&f.push(`s-maxage=${t.maxAge}`),t.staleMaxAge?f.push(`stale-while-revalidate=${t.staleMaxAge}`):f.push("stale-while-revalidate")):t.maxAge&&f.push(`max-age=${t.maxAge}`),f.length>0&&(p["cache-control"]=f.join(", "));return{code:l.node.res.statusCode,headers:p,body:u}}),r);return d((async o=>{if(t.headersOnly){if(l(o,{maxAge:t.maxAge}))return;return e(o)}const r=await s(o);if(o.node.res.headersSent||o.node.res.writableEnded)return r.body;if(!l(o,{modifiedTime:new Date(r.headers["last-modified"]),etag:r.headers.etag,maxAge:t.maxAge})){o.node.res.statusCode=r.code;for(const e in r.headers){const t=r.headers[e];"set-cookie"===e?o.node.res.appendHeader(e,u(t)):void 0!==t&&o.node.res.setHeader(e,t)}return r.body}}))}function cloneWithProxy(e,t){return new Proxy(e,{get:(e,o,r)=>o in t?t[o]:Reflect.get(e,o,r),set:(e,o,r,s)=>o in t?(t[o]=r,!0):Reflect.set(e,o,r,s)})}const dt=defineCachedEventHandler,lt=X({name:"Handmade in RO",description:"Descoperă produse handmade autentice din România",version:"2.0.0"},{nuxt:{},icon:{provider:"server",class:"",aliases:{},iconifyApiEndpoint:"https://api.iconify.design",localApiEndpoint:"/api/_nuxt_icon",fallbackToApi:!0,cssSelectorPrefix:"i-",cssWherePseudo:!0,mode:"css",attrs:{"aria-hidden":!0},collections:["academicons","akar-icons","ant-design","arcticons","basil","bi","bitcoin-icons","bpmn","brandico","bx","bxl","bxs","bytesize","carbon","catppuccin","cbi","charm","ci","cib","cif","cil","circle-flags","circum","clarity","codicon","covid","cryptocurrency","cryptocurrency-color","dashicons","devicon","devicon-plain","ei","el","emojione","emojione-monotone","emojione-v1","entypo","entypo-social","eos-icons","ep","et","eva","f7","fa","fa-brands","fa-regular","fa-solid","fa6-brands","fa6-regular","fa6-solid","fad","fe","feather","file-icons","flag","flagpack","flat-color-icons","flat-ui","flowbite","fluent","fluent-emoji","fluent-emoji-flat","fluent-emoji-high-contrast","fluent-mdl2","fontelico","fontisto","formkit","foundation","fxemoji","gala","game-icons","geo","gg","gis","gravity-ui","gridicons","grommet-icons","guidance","healthicons","heroicons","heroicons-outline","heroicons-solid","hugeicons","humbleicons","ic","icomoon-free","icon-park","icon-park-outline","icon-park-solid","icon-park-twotone","iconamoon","iconoir","icons8","il","ion","iwwa","jam","la","lets-icons","line-md","logos","ls","lucide","lucide-lab","mage","majesticons","maki","map","marketeq","material-symbols","material-symbols-light","mdi","mdi-light","medical-icon","memory","meteocons","mi","mingcute","mono-icons","mynaui","nimbus","nonicons","noto","noto-v1","octicon","oi","ooui","openmoji","oui","pajamas","pepicons","pepicons-pencil","pepicons-pop","pepicons-print","ph","pixelarticons","prime","ps","quill","radix-icons","raphael","ri","rivet-icons","si-glyph","simple-icons","simple-line-icons","skill-icons","solar","streamline","streamline-emojis","subway","svg-spinners","system-uicons","tabler","tdesign","teenyicons","token","token-branded","topcoat","twemoji","typcn","uil","uim","uis","uit","uiw","unjs","vaadin","vs","vscode-icons","websymbol","weui","whh","wi","wpf","zmdi","zondicons"],fetchTimeout:1500}});function getEnv(t,o){const r=ee(t).toUpperCase();return Y(e.env[o.prefix+r]??e.env[o.altPrefix+r])}function _isObject(e){return"object"==typeof e&&!Array.isArray(e)}function applyEnv(t,o,r=""){for(const s in t){const n=r?`${r}_${s}`:s,a=getEnv(n,o);_isObject(t[s])?_isObject(a)?(t[s]={...t[s],...a},applyEnv(t[s],o,n)):void 0===a?applyEnv(t[s],o,n):t[s]=a??t[s]:t[s]=a??t[s],o.envExpansion&&"string"==typeof t[s]&&(t[s]=t[s].replace(ut,((t,o)=>e.env[o]||t)))}return t}const ut=/\{\{([^{}]*)\}\}/g;const ht={app:{baseURL:"/",buildId:"dev",buildAssetsDir:"/_nuxt/",cdnURL:""},nitro:{envPrefix:"NUXT_",routeRules:{"/__nuxt_error":{cache:!1},"/_nuxt/builds/meta/**":{headers:{"cache-control":"public, max-age=31536000, immutable"}},"/_nuxt/builds/**":{headers:{"cache-control":"public, max-age=1, immutable"}}}},public:{serverUrl:"http://127.0.0.1:3001",apiUrl:"https://bapi.handmadein.ro",cloudflareApiUrl:"https://bapi.handmadein.ro",stripePublishableKey:"pk_test_51RWduuE29KWOudtqnx565UhywXI7Erip16wIhcA7BLzE3bxeqFzB6C85gDU1jn8lxVZPirgTlWv6gzxUojMdv6zQ00yEwNq9B9",reviewsApiEndpoint:"https://reviews.handmadein.ro",defaultCountry:"RO",defaultLanguage:"ro",gtag:{enabled:!0,initMode:"auto",id:"AW-16546086564",initCommands:[],config:{cookie_prefix:"_ga",cookie_domain:"handmadein.ro",cookie_expires:63072e3,ads_data_redaction:!0,url_passthrough:!0,consent:{ad_storage:"denied",analytics_storage:"denied",functionality_storage:"denied",personalization_storage:"denied",security_storage:"granted",wait_for_update:2e3}},tags:[],loadingStrategy:"defer",url:"https://www.googletagmanager.com/gtag/js"},i18n:{baseUrl:"https://handmadein.ro",defaultLocale:"ro",defaultDirection:"ltr",strategy:"no_prefix",lazy:!0,rootRedirect:"",routesNameSeparator:"___",defaultLocaleRouteNameSuffix:"default",skipSettingLocaleOnNavigate:!1,differentDomains:!1,trailingSlash:!1,locales:[{code:"ro",name:"Română",language:"ro-RO",files:[{path:"C:/Users/<USER>/PhpstormProjects/ro/fe2/locales/ro.json",cache:""}]}],detectBrowserLanguage:{alwaysRedirect:!1,cookieCrossOrigin:!0,cookieDomain:"",cookieKey:"i18n_redirected",cookieSecure:!1,fallbackLocale:"ro",redirectOn:"root",useCookie:!0},experimental:{localeDetector:"",switchLocalePathLinkSSR:!1,autoImportTranslationFunctions:!1,typedPages:!0,typedOptionsAndMessages:!1,generatedLocaleFilePathFormat:"absolute",alternateLinkCanonicalQueries:!1,hmr:!0},multiDomainLocales:!1}},icon:{serverKnownCssClasses:[]},ipx:{baseURL:"/_ipx",alias:{"/youtube":"https://img.youtube.com","/vimeo":"https://i.vimeocdn.com","/cdn":"https://hcdn.handmadein.ro"},fs:{dir:["C:/Users/<USER>/PhpstormProjects/ro/fe2/public"]},http:{domains:["img.youtube.com","i.vimeocdn.com","hcdn.handmadein.ro","handmadein.ro"]}}},mt={prefix:"NITRO_",altPrefix:ht.nitro.envPrefix??e.env.NITRO_ENV_PREFIX??"_",envExpansion:ht.nitro.envExpansion??e.env.NITRO_ENV_EXPANSION??!1},pt=_deepFreeze(applyEnv(K(ht),mt));function useRuntimeConfig(e){if(!e)return pt;if(e.context.nitro.runtimeConfig)return e.context.nitro.runtimeConfig;const t=K(ht);return applyEnv(t,mt),e.context.nitro.runtimeConfig=t,t}const ft=_deepFreeze(K(lt));function _deepFreeze(e){const t=Object.getOwnPropertyNames(e);for(const o of t){const t=e[o];t&&"object"==typeof t&&_deepFreeze(t)}return Object.freeze(e)}new Proxy(Object.create(null),{get:(e,t)=>{console.warn("Please use `useRuntimeConfig()` instead of accessing config directly.");const o=useRuntimeConfig();if(t in o)return o[t]}});const gt=xe(Ae({routes:useRuntimeConfig().nitro.routeRules}));function getRouteRules(e){return e.context._nitro=e.context._nitro||{},e.context._nitro.routeRules||(e.context._nitro.routeRules=getRouteRulesForPath(withoutBase(e.path.split("?")[0],useRuntimeConfig().app.baseURL))),e.context._nitro.routeRules}function getRouteRulesForPath(e){return V({},...gt.matchAll(e).reverse())}function _captureError(e,t){console.error(`[${t}]`,e),useNitroApp().captureError(e,{tags:[t]})}function joinHeaders(e){return Array.isArray(e)?e.join(", "):String(e)}function normalizeCookieHeader(e=""){return u(joinHeaders(e))}function normalizeCookieHeaders(e){const t=new Headers;for(const[o,r]of e)if("set-cookie"===o)for(const e of normalizeCookieHeader(r))t.append("set-cookie",e);else t.set(o,joinHeaders(r));return t}function hasReqHeader(e,t,o){const r=b(e,t);return r&&"string"==typeof r&&r.toLowerCase().includes(o)}async function defaultHandler(t,o,r){const s=t.unhandled||t.fatal,n=t.statusCode||500,a=t.statusMessage||"Server Error",i=k(o,{xForwardedHost:!0,xForwardedProto:!0});if(404===n){const e="/";if(/^\/[^/]/.test(e)&&!i.pathname.startsWith(e)){return{status:302,statusText:"Found",headers:{location:`${e}${i.pathname.slice(1)}${i.search}`},body:"Redirecting..."}}}await loadStackTrace(t).catch(je.error);const c=new Pe;if(s&&!r?.silent){const r=[t.unhandled&&"[unhandled]",t.fatal&&"[fatal]"].filter(Boolean).join(" "),s=await(await c.toANSI(t)).replaceAll(e.cwd(),".");je.error(`[request error] ${r} [${o.method}] ${i}\n\n`,s)}const d=r?.json||!b(o,"accept")?.includes("text/html"),l={"content-type":d?"application/json":"text/html","x-content-type-options":"nosniff","x-frame-options":"DENY","referrer-policy":"no-referrer","content-security-policy":"script-src 'self' 'unsafe-inline'; object-src 'none'; base-uri 'self';"};404!==n&&j(o,"cache-control")||(l["cache-control"]="no-cache");return{status:n,statusText:a,headers:l,body:d?{error:!0,url:i,statusCode:n,statusMessage:a,message:t.message,data:t.data,stack:t.stack?.split("\n").map((e=>e.trim()))}:await c.toHTML(t,{request:{url:i.href,method:o.method,headers:x(o)}})}}async function loadStackTrace(e){if(!(e instanceof Error))return;const t=await(new Se).defineSourceLoader(sourceLoader).parse(e),o=e.message+"\n"+t.frames.map((e=>function(e){if("native"===e.type)return e.raw;const t=`${e.fileName||""}:${e.lineNumber}:${e.columnNumber})`;return e.functionName?`at ${e.functionName} (${t}`:`at ${t}`}(e))).join("\n");Object.defineProperty(e,"stack",{value:o}),e.cause&&await loadStackTrace(e.cause).catch(je.error)}async function sourceLoader(e){if(!e.fileName||"fs"!==e.fileType||"native"===e.type)return;if("app"===e.type){const t=await ke(`${e.fileName}.map`,"utf8").catch((()=>{}));if(t){const o=(await new Re(t)).originalPositionFor({line:e.lineNumber,column:e.columnNumber});o.source&&o.line&&(e.fileName=r(s(e.fileName),o.source),e.lineNumber=o.line,e.columnNumber=o.column||0)}}const t=await ke(e.fileName,"utf8").catch((()=>{}));return t?{contents:t}:void 0}const yt=[async function(e,t,{defaultHandler:o}){if(t.handled||function(e){return!hasReqHeader(e,"accept","text/html")&&(hasReqHeader(e,"accept","application/json")||hasReqHeader(e,"user-agent","curl/")||hasReqHeader(e,"user-agent","httpie/")||hasReqHeader(e,"sec-fetch-mode","cors")||e.path.startsWith("/api/")||e.path.endsWith(".json"))}(t))return;const r=await o(e,t,{json:!0});if(404===(e.statusCode||500)&&302===r.status)return _(t,r.headers),v(t,r.status,r.statusText),C(t,JSON.stringify(r.body,null,2));"string"!=typeof r.body&&Array.isArray(r.body.stack)&&(r.body.stack=r.body.stack.join("\n"));const s=r.body,n=new URL(s.url);s.url=withoutBase(n.pathname,useRuntimeConfig(t).app.baseURL)+n.search+n.hash,s.message||="Server Error",s.data||=e.data,s.statusMessage||=e.statusMessage,delete r.headers["content-type"],delete r.headers["content-security-policy"],_(t,r.headers);const a=x(t),i=t.path.startsWith("/__nuxt_error")||!!a["x-nuxt-error"]?null:await useNitroApp().localFetch(withQuery(joinURL(useRuntimeConfig(t).app.baseURL,"/__nuxt_error"),s),{headers:{...a,"x-nuxt-error":"true"},redirect:"manual"}).catch((()=>null));if(t.handled)return;if(!i){const{template:e}=await Promise.resolve().then((function(){return to}));return s.description=s.message,A(t,"Content-Type","text/html;charset=UTF-8"),C(t,e(s))}const c=await i.text();for(const[e,o]of i.headers.entries())A(t,e,o);return v(t,i.status&&200!==i.status?i.status:r.status,i.statusText||r.statusText),C(t,c)},async function(e,t){const o=await defaultHandler(e,t);return t.node?.res.headersSent||_(t,o.headers),v(t,o.status,o.statusText),C(t,"string"==typeof o.body?o.body:JSON.stringify(o.body,null,2))}];const wt={meta:[{name:"viewport",content:"width=device-width, initial-scale=1"},{charset:"utf-8"}],link:[],style:[],script:[],noscript:[]},bt="div",_t={id:"teleports"},vt="nuxt-app",Ct={VNode:e=>ae(e)?{type:e.type,props:e.props}:void 0,URL:e=>e instanceof URL?e.toString():void 0},xt=Ee("nuxt-dev",{asyncContext:!0,AsyncLocalStorage:Te}),At=/\/node_modules\/(?:.*\/)?(?:nuxt|nuxt-nightly|nuxt-edge|nuxt3|consola|@vue)\/|core\/runtime\/nitro/;const kt=[function(e){e.hooks.hook("render:html",(e=>{e.head.push("<script>\nif (!window.__NUXT_DEVTOOLS_TIME_METRIC__) {\n  Object.defineProperty(window, '__NUXT_DEVTOOLS_TIME_METRIC__', {\n    value: {},\n    enumerable: false,\n    configurable: true,\n  })\n}\nwindow.__NUXT_DEVTOOLS_TIME_METRIC__.appInit = Date.now()\n<\/script>")}))},e=>{const t=e.h3App.handler;var o;e.h3App.handler=e=>xt.callAsync({logs:[],event:e},(()=>t(e))),o=e=>{const t=xt.tryUse();if(!t)return;const o=$e();if(!o||o.includes("runtime/vite-node.mjs"))return;const r=[];let s="";for(const e of Oe(o))e.source!==globalThis._importMeta_.url&&(At.test(e.source)||(s||=e.source.replace(withTrailingSlash("C:/Users/<USER>/PhpstormProjects/ro/fe2"),""),r.push({...e,source:e.source.startsWith("file://")?e.source.replace("file://",""):e.source})));const n={...e,filename:s,stack:r};t.logs.push(n)},Ue.addReporter({log(e){o(e)}}),Ue.wrapConsole(),e.hooks.hook("afterResponse",(()=>{const t=xt.tryUse();if(t)return e.hooks.callHook("dev:ssr-logs",{logs:t.logs,path:t.event.path})})),e.hooks.hook("render:html",(e=>{const t=xt.tryUse();if(t)try{const o=Object.assign(Object.create(null),Ct,t.event.context._payloadReducers);e.bodyAppend.unshift(`<script type="application/json" data-nuxt-logs="${vt}">${se(t.logs,o)}<\/script>`)}catch(e){const t=e instanceof Error&&"toString"in e?` Received \`${e.toString()}\`.`:"";console.warn(`[nuxt] Failed to stringify dev server logs.${t} You can define your own reducer/reviver for rich types following the instructions in https://nuxt.com/docs/api/composables/use-nuxt-app#payload.`)}}))}],jt={};function getOrCreateSession(e){let t=U(e,"sessionId");return t&&jt[t]||(t=Math.random().toString(36).substring(2,15)+Math.random().toString(36).substring(2,15),jt[t]={},useRuntimeConfig(),S(e,"sessionId",t,{httpOnly:!0,path:"/",maxAge:604800,secure:!1,sameSite:"lax"})),jt[t]}const Ut=d((e=>{e.context.session=getOrCreateSession(e)}));const St={},Pt={};function buildAssetsURL(...e){return joinRelativeURL(publicAssetsURL(),useRuntimeConfig().app.buildAssetsDir,...e)}function publicAssetsURL(...e){const t=useRuntimeConfig().app,o=t.cdnURL||t.baseURL;return e.length?joinRelativeURL(o,...e):o}const Rt="handmadein_auth",Tt={maxAge:2592e3,path:"/",secure:!1,httpOnly:!1,sameSite:"lax"};async function getSession(e){try{const t=U(e,Rt);if(!t)return null;try{const e=JSON.parse(t);if(e&&e.isAuthenticated)return{isAuthenticated:!0,userId:e.userId,username:e.username||e.email,email:e.email,name:e.name,given_name:e.given_name,family_name:e.family_name,phone_number:e.phone_number,accessToken:e.accessToken,customer:e.customer}}catch(e){console.error("Error parsing auth cookie:",e)}return null}catch(e){return console.error("Error getting session:",e),null}}const Et=new Set,$t="https://api.iconify.design",Ot=defineCachedEventHandler((async e=>{const t=k(e);if(!t)return R({status:400,message:"Invalid icon request"});const o=ft.icon,r=e.context.params?.collection?.replace(/\.json$/,""),s=r?await(Le[r]?.()):null,n=o.iconifyApiEndpoint||$t,a=t.searchParams.get("icons")?.split(",");if(s){if(a?.length){const e=ze(s,a);return Ue.debug(`[Icon] serving ${(a||[]).map((e=>"`"+r+":"+e+"`")).join(",")} from bundled collection`),e}}else r&&!Et.has(r)&&n===$t&&(Ue.warn([`[Icon] Collection \`${r}\` is not found locally`,`We suggest to install it via \`npm i -D @iconify-json/${r}\` to provide the best end-user experience.`].join("\n")),Et.add(r));if(!0===o.fallbackToApi||"server-only"===o.fallbackToApi){const e=new URL("./"+Ne(t.pathname)+t.search,n);if(Ue.debug(`[Icon] fetching ${(a||[]).map((e=>"`"+r+":"+e+"`")).join(",")} from iconify api`),e.host!==new URL(n).host)return R({status:400,message:"Invalid icon request"});try{return await $fetch(e.href)}catch(e){return Ue.error(e),404===e.status?R({status:404}):R({status:500,message:"Failed to fetch fallback icon"})}}return R({status:404})}),{group:"nuxt",name:"icon",getKey(e){const t=e.context.params?.collection?.replace(/\.json$/,"")||"unknown",o=String(T(e).icons||"");return`${t}_${o.split(",")[0]}_${o.length}_${Ce(o)}`},swr:!0,maxAge:604800}),VueResolver=(e,t)=>ce(t)?ie(t):t;function createHead(e={}){const t=te({...e,propResolvers:[VueResolver]});return t.install=function(e){return{install(t){t.config.globalProperties.$unhead=e,t.config.globalProperties.$head=e,t.provide("usehead",e)}}.install}(t),t}const Nt={disableDefaults:!0,disableCapoSorting:!1,plugins:[de,le,ue,he]};function createSSRContext(e){return{url:e.path,event:e,runtimeConfig:useRuntimeConfig(e),noSSR:e.context.nuxt?.noSSR||!1,head:createHead(Nt),error:!1,nuxt:void 0,payload:{},_payloadReducers:Object.create(null),modules:new Set}}const It=`<${bt}${oe({id:"__nuxt"})}>`,zt=`</${bt}>`,getClientManifest=()=>import("file://C:/Users/<USER>/PhpstormProjects/ro/fe2/.nuxt/dist/server/client.manifest.mjs").then((e=>e.default||e)).then((e=>"function"==typeof e?e():e)),Lt=lazyCachedFunction((async()=>{const t=await getClientManifest();if(!t)throw new Error("client.manifest is not available");const o=await import("file://C:/Users/<USER>/PhpstormProjects/ro/fe2/.nuxt/dist/server/server.mjs").then((e=>e.default||e));if(!o)throw new Error("Server bundle is not available");const r=q(o,{manifest:t,renderToString:async function(t,o){const s=await Q(t,o);e.env.NUXT_VITE_NODE_OPTIONS&&r.rendererContext.updateManifest(await getClientManifest());return It+s+zt},buildAssetsURL:buildAssetsURL});return r})),Dt=lazyCachedFunction((async()=>{const e=await getClientManifest(),t=await Promise.resolve().then((function(){return oo})).then((e=>e.template)).catch((()=>"")).then((e=>It+e+zt)),o=q((()=>()=>{}),{manifest:e,renderToString:()=>t,buildAssetsURL:buildAssetsURL}),r=await o.renderToString({});return{rendererContext:o.rendererContext,renderToString:e=>{const t=useRuntimeConfig(e.event);return e.modules||=new Set,e.payload.serverRendered=!1,e.config={public:t.public,app:t.app},Promise.resolve(r)}}}));function lazyCachedFunction(e){let t=null;return()=>(null===t&&(t=e().catch((e=>{throw t=null,e}))),t)}const Bt=lazyCachedFunction((()=>Promise.resolve().then((function(){return ro})).then((e=>e.default||e))));const Mt=new RegExp(`^<${bt}[^>]*>([\\s\\S]*)<\\/${bt}>$`);function getServerComponentHTML(e){const t=e.match(Mt);return t?.[1]||e}const Ht=/^uid=([^;]*);slot=(.*)$/,Ft=/^uid=([^;]*);client=(.*)$/,qt=/^island-slot=([^;]*);(.*)$/;function getSlotIslandResponse(e){if(!e.islandContext||!Object.keys(e.islandContext.slots).length)return;const t={};for(const[o,r]of Object.entries(e.islandContext.slots))t[o]={...r,fallback:e.teleports?.[`island-fallback=${o}`]};return t}function getClientIslandResponse(e){if(!e.islandContext||!Object.keys(e.islandContext.components).length)return;const t={};for(const[o,r]of Object.entries(e.islandContext.components)){const s=e.teleports?.[o]?.replaceAll("\x3c!--teleport start anchor--\x3e","")||"";t[o]={...r,html:s,slots:getComponentSlotTeleport(o,e.teleports??{})}}return t}function getComponentSlotTeleport(e,t){const o=Object.entries(t),r={};for(const[t,s]of o){const o=t.match(qt);if(o){const[,t,n]=o;if(!n||e!==t)continue;r[n]=s}}return r}function replaceIslandTeleports(e,t){const{teleports:o,islandContext:r}=e;if(r||!o)return t;for(const e in o){const r=e.match(Ft);if(r){const[,s,n]=r;if(!s||!n)continue;t=t.replace(new RegExp(` data-island-uid="${s}" data-island-component="${n}"[^>]*>`),(t=>t+o[e]));continue}const s=e.match(Ht);if(s){const[,r,n]=s;if(!r||!n)continue;t=t.replace(new RegExp(` data-island-uid="${r}" data-island-slot="${n}"[^>]*>`),(t=>t+o[e]))}}return t}const Gt=/\.json(\?.*)?$/,Wt=d((async e=>{const t=useNitroApp();_(e,{"content-type":"application/json;charset=utf-8","x-powered-by":"Nuxt"});const o=await async function(e){let t=e.path||"";const o=t.substring(15).replace(Gt,"").split("_"),r=o.length>1?o.pop():void 0,s=o.join("_"),n="GET"===e.method?T(e):await E(e);return{url:"/",...n,id:r,name:s,props:Z(n.props)||{},slots:{},components:{}}}(e),r={...createSSRContext(e),islandContext:o,noSSR:!1,url:o.url},s=await Lt(),n=await s.renderToString(r).catch((async e=>{throw await(r.nuxt?.hooks.callHook("app:error",e)),e})),a=await async function(e){const t=await Bt(),o=new Set;for(const r of e)if(r in t&&t[r])for(const e of await t[r]())o.add(e);return Array.from(o).map((e=>({innerHTML:e})))}(r.modules??[]);await(r.nuxt?.hooks.callHook("app:rendered",{ssrContext:r,renderResult:n})),a.length&&r.head.push({style:a});{const{styles:e}=G(r,s.rendererContext),t=[];for(const o of Object.values(e))"inline"in getQuery(o.file)||o.file.includes("scoped")&&!o.file.includes("pages/")&&t.push({rel:"stylesheet",href:s.rendererContext.buildAssetsURL(o.file),crossorigin:""});t.length&&r.head.push({link:t},{mode:"server"})}const i={};for(const e of r.head.entries.values())for(const[t,o]of Object.entries((c=e.input,De(c,VueResolver)))){const e=i[t];Array.isArray(e)&&e.push(...o),i[t]=o}var c;i.link||=[],i.style||=[];const d={id:o.id,head:i,html:getServerComponentHTML(n.html),components:getClientIslandResponse(r),slots:getSlotIslandResponse(r)};return await t.hooks.callHook("render:island",d,{event:e,islandContext:o}),d}));const _lazy_mSzCUS=()=>Promise.resolve().then((function(){return sr})),Jt=[{route:"",handler:Ut,lazy:!1,middleware:!0,method:void 0},{route:"/api/auth/**",handler:()=>Promise.resolve().then((function(){return ao})),lazy:!0,middleware:!1,method:void 0},{route:"/api/auth/cookie-fix",handler:()=>Promise.resolve().then((function(){return co})),lazy:!0,middleware:!1,method:void 0},{route:"/api/auth/current-user",handler:()=>Promise.resolve().then((function(){return ho})),lazy:!0,middleware:!1,method:void 0},{route:"/api/auth/debug",handler:()=>Promise.resolve().then((function(){return po})),lazy:!0,middleware:!1,method:void 0},{route:"/api/auth/login",handler:()=>Promise.resolve().then((function(){return go})),lazy:!0,middleware:!1,method:void 0},{route:"/api/auth/signin",handler:()=>Promise.resolve().then((function(){return wo})),lazy:!0,middleware:!1,method:void 0},{route:"/api/auth/signout",handler:()=>Promise.resolve().then((function(){return _o})),lazy:!0,middleware:!1,method:void 0},{route:"/api/auth/sync-medusa",handler:()=>Promise.resolve().then((function(){return Co})),lazy:!0,middleware:!1,method:void 0},{route:"/api/contact",handler:()=>Promise.resolve().then((function(){return Ao})),lazy:!0,middleware:!1,method:void 0},{route:"/api/customers/addresses",handler:()=>Promise.resolve().then((function(){return jo})),lazy:!0,middleware:!1,method:void 0},{route:"/api/customers/addresses/:id",handler:()=>Promise.resolve().then((function(){return So})),lazy:!0,middleware:!1,method:void 0},{route:"/api/customers/me",handler:()=>Promise.resolve().then((function(){return Ro})),lazy:!0,middleware:!1,method:void 0},{route:"/api/customers/password",handler:()=>Promise.resolve().then((function(){return Eo})),lazy:!0,middleware:!1,method:void 0},{route:"/api/newsletter/subscribe",handler:()=>Promise.resolve().then((function(){return Oo})),lazy:!0,middleware:!1,method:"post"},{route:"/api/newsletter/unsubscribe",handler:()=>Promise.resolve().then((function(){return Io})),lazy:!0,middleware:!1,method:"post"},{route:"/api/orders/:id",handler:()=>Promise.resolve().then((function(){return Lo})),lazy:!0,middleware:!1,method:void 0},{route:"/api/orders/:id/update-customer",handler:()=>Promise.resolve().then((function(){return Bo})),lazy:!0,middleware:!1,method:void 0},{route:"/api/orders",handler:()=>Promise.resolve().then((function(){return Ho})),lazy:!0,middleware:!1,method:void 0},{route:"/api/products/:id",handler:()=>Promise.resolve().then((function(){return qo})),lazy:!0,middleware:!1,method:void 0},{route:"/api/products/prices",handler:()=>Promise.resolve().then((function(){return Wo})),lazy:!0,middleware:!1,method:void 0},{route:"/api/reviews/:productId",handler:()=>Promise.resolve().then((function(){return Qo})),lazy:!0,middleware:!1,method:"get"},{route:"/api/reviews/:productId",handler:()=>Promise.resolve().then((function(){return Vo})),lazy:!0,middleware:!1,method:"post"},{route:"/api/reviews/ratings",handler:()=>Promise.resolve().then((function(){return Yo})),lazy:!0,middleware:!1,method:"get"},{route:"/__nuxt_error",handler:_lazy_mSzCUS,lazy:!0,middleware:!1,method:void 0},{route:"/api/_nuxt_icon/:collection",handler:Ot,lazy:!1,middleware:!1,method:void 0},{route:"/__nuxt_island/**",handler:Wt,lazy:!1,middleware:!1,method:void 0},{route:"/_ipx/**",handler:$((()=>{const e=useRuntimeConfig().ipx||{},t=e?.fs?.dir?(Array.isArray(e.fs.dir)?e.fs.dir:[e.fs.dir]).map((e=>Ie(e)?e:Be(new URL(e,globalThis._importMeta_.url)))):void 0,o=e.fs?.dir?Me({...e.fs,dir:t}):void 0,r=e.http?.domains?He({...e.http}):void 0;if(!o&&!r)throw new Error("IPX storage is not configured!");const s={...e,storage:o||r,httpStorage:r},n=Fe(s),a=qe(n);return O(e.baseURL,a)})),lazy:!1,middleware:!1,method:void 0},{route:"/**",handler:_lazy_mSzCUS,lazy:!0,middleware:!1,method:void 0}];const Qt=function(){const e=useRuntimeConfig(),t=me(),captureError=(e,o={})=>{const r=t.callHookParallel("error",e,o).catch((e=>{console.error("Error while capturing another error",e)}));if(o.event&&p(o.event)){const t=o.event.context.nitro?.errors;t&&t.push({error:e,context:o}),o.event.waitUntil&&o.event.waitUntil(r)}},o=N({debug:Y(!0),onError:(e,t)=>(captureError(e,{event:t,tags:["request"]}),async function(e,t){for(const o of yt)try{if(await o(e,t,{defaultHandler:defaultHandler}),t.handled)return}catch(e){console.error(e)}}(e,t)),onRequest:async e=>{e.context.nitro=e.context.nitro||{errors:[]};const t=e.node.req?.__unenv__;t?._platform&&(e.context={_platform:t?._platform,...t._platform,...e.context}),!e.context.waitUntil&&t?.waitUntil&&(e.context.waitUntil=t.waitUntil),e.fetch=(t,o)=>m(e,t,o,{fetch:localFetch}),e.$fetch=(t,o)=>m(e,t,o,{fetch:n}),e.waitUntil=t=>{e.context.nitro._waitUntilPromises||(e.context.nitro._waitUntilPromises=[]),e.context.nitro._waitUntilPromises.push(t),e.context.waitUntil&&e.context.waitUntil(t)},e.captureError=(t,o)=>{captureError(t,{event:e,...o})},await Qt.hooks.callHook("request",e).catch((t=>{captureError(t,{event:e,tags:["request"]})}))},onBeforeResponse:async(e,t)=>{await Qt.hooks.callHook("beforeResponse",e,t).catch((t=>{captureError(t,{event:e,tags:["request","response"]})}))},onAfterResponse:async(e,t)=>{await Qt.hooks.callHook("afterResponse",e,t).catch((t=>{captureError(t,{event:e,tags:["request","response"]})}))}}),r=I({preemptive:!0}),s=z(o),localFetch=(e,t)=>e.toString().startsWith("/")?ge(s,e,t).then((e=>function(e){return e.headers.has("set-cookie")?new Response(e.body,{status:e.status,statusText:e.statusText,headers:normalizeCookieHeaders(e.headers)}):e}(e))):globalThis.fetch(e,t),n=pe({fetch:localFetch,Headers:fe,defaults:{baseURL:e.app.baseURL}});var a;globalThis.$fetch=n,o.use((a={localFetch:localFetch},f((e=>{const t=getRouteRules(e);if(t.headers&&g(e,t.headers),t.redirect){let o=t.redirect.to;if(o.endsWith("/**")){let r=e.path;const s=t.redirect._redirectStripBase;s&&(r=withoutBase(r,s)),o=joinURL(o.slice(0,-3),r)}else e.path.includes("?")&&(o=withQuery(o,getQuery(e.path)));return y(e,o,t.redirect.statusCode)}if(t.proxy){let o=t.proxy.to;if(o.endsWith("/**")){let r=e.path;const s=t.proxy._proxyStripBase;s&&(r=withoutBase(r,s)),o=joinURL(o.slice(0,-3),r)}else e.path.includes("?")&&(o=withQuery(o,getQuery(e.path)));return w(e,o,{fetch:a.localFetch,...t.proxy})}}))));for(const t of Jt){let s=t.lazy?$(t.handler):t.handler;if(t.middleware||!t.route){const r=(e.app.baseURL+(t.route||"/")).replace(/\/+/g,"/");o.use(r,s)}else{const e=getRouteRulesForPath(t.route.replace(/:\w+|\*\*/g,"_"));e.cache&&(s=dt(s,{group:"nitro/routes",...e.cache})),r.use(t.route,s,t.method)}}return o.use(e.app.baseURL,r.handler),{hooks:t,h3App:o,router:r,localCall:e=>ye(s,e),localFetch:localFetch,captureError:captureError}}();function useNitroApp(){return Qt}!function(e){for(const t of kt)try{t(e)}catch(t){throw e.captureError(t,{tags:["plugin"]}),t}}(Qt),globalThis.crypto||(globalThis.crypto=a);const{NITRO_NO_UNIX_SOCKET:Kt,NITRO_DEV_WORKER_ID:Vt}=e.env;e.on("unhandledRejection",(e=>_captureError(e,"unhandledRejection"))),e.on("uncaughtException",(e=>_captureError(e,"uncaughtException"))),i?.on("message",(e=>{e&&"shutdown"===e.event&&shutdown()}));const Xt=useNitroApp(),Yt=new o(z(Xt.h3App));let Zt;function listen(o=Boolean(Kt||e.versions.webcontainer||"Bun"in globalThis&&"win32"===e.platform)){return new Promise(((r,s)=>{try{Zt=Yt.listen(o?0:function(){const o=`nitro-worker-${e.pid}-${c}-${Vt}-${Math.round(1e4*Math.random())}.sock`;if("win32"===e.platform)return n(String.raw`\\.\pipe`,o);if("linux"===e.platform){if(Number.parseInt(e.versions.node.split(".")[0],10)>=20)return`\0${o}`}return n(t(),o)}(),(()=>{const e=Yt.address();i?.postMessage({event:"listen",address:"string"==typeof e?{socketPath:e}:{host:"localhost",port:e?.port}}),r()}))}catch(e){s(e)}}))}async function shutdown(){Yt.closeAllConnections?.(),await Promise.all([new Promise((e=>Zt?.close(e))),Xt.hooks.callHook("close").catch(console.error)]),i?.postMessage({event:"exit"})}listen().catch((()=>listen(!0))).catch((e=>(console.error("Dev worker failed to listen:",e),shutdown()))),Xt.router.get("/_nitro/tasks",d((async e=>{const t=await Promise.all(Object.entries(St).map((async([e,t])=>{const o=await(t.resolve?.());return[e,{description:o?.meta?.description}]})));return{tasks:Object.fromEntries(t),scheduledTasks:false}}))),Xt.router.use("/_nitro/tasks/:name",d((async e=>{const t=L(e,"name"),o={...T(e),...await E(e).then((e=>e?.payload)).catch((()=>({})))};return await async function(e,{payload:t={},context:o={}}={}){if(Pt[e])return Pt[e];if(!(e in St))throw R({message:`Task \`${e}\` is not available!`,statusCode:404});if(!St[e].resolve)throw R({message:`Task \`${e}\` is not implemented!`,statusCode:501});const r=await St[e].resolve(),s={name:e,payload:t,context:o};Pt[e]=r.run(s);try{return await Pt[e]}finally{delete Pt[e]}}(t,{payload:o})})));const eo={appName:"Nuxt",version:"",statusCode:500,statusMessage:"Server error",description:"An error occurred in the application and the page could not be served. If you are the application owner, check your server logs for details.",stack:""},to=Object.freeze({__proto__:null,template:e=>(e={...eo,...e},'<!DOCTYPE html><html lang="en"><head><title>'+F(e.statusCode)+" - "+F(e.statusMessage||"Internal Server Error")+'</title><meta charset="utf-8"><meta content="width=device-width,initial-scale=1.0,minimum-scale=1.0" name="viewport"><style>.spotlight{background:linear-gradient(45deg,#00dc82,#36e4da 50%,#0047e1);bottom:-40vh;filter:blur(30vh);height:60vh;opacity:.8}*,:after,:before{border-color:var(--un-default-border-color,#e5e7eb);border-style:solid;border-width:0;box-sizing:border-box}:after,:before{--un-content:""}html{line-height:1.5;-webkit-text-size-adjust:100%;font-family:ui-sans-serif,system-ui,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;font-feature-settings:normal;font-variation-settings:normal;-moz-tab-size:4;tab-size:4;-webkit-tap-highlight-color:transparent}body{line-height:inherit;margin:0}h1{font-size:inherit;font-weight:inherit}h1,p{margin:0}*,:after,:before{--un-rotate:0;--un-rotate-x:0;--un-rotate-y:0;--un-rotate-z:0;--un-scale-x:1;--un-scale-y:1;--un-scale-z:1;--un-skew-x:0;--un-skew-y:0;--un-translate-x:0;--un-translate-y:0;--un-translate-z:0;--un-pan-x: ;--un-pan-y: ;--un-pinch-zoom: ;--un-scroll-snap-strictness:proximity;--un-ordinal: ;--un-slashed-zero: ;--un-numeric-figure: ;--un-numeric-spacing: ;--un-numeric-fraction: ;--un-border-spacing-x:0;--un-border-spacing-y:0;--un-ring-offset-shadow:0 0 transparent;--un-ring-shadow:0 0 transparent;--un-shadow-inset: ;--un-shadow:0 0 transparent;--un-ring-inset: ;--un-ring-offset-width:0px;--un-ring-offset-color:#fff;--un-ring-width:0px;--un-ring-color:rgba(147,197,253,.5);--un-blur: ;--un-brightness: ;--un-contrast: ;--un-drop-shadow: ;--un-grayscale: ;--un-hue-rotate: ;--un-invert: ;--un-saturate: ;--un-sepia: ;--un-backdrop-blur: ;--un-backdrop-brightness: ;--un-backdrop-contrast: ;--un-backdrop-grayscale: ;--un-backdrop-hue-rotate: ;--un-backdrop-invert: ;--un-backdrop-opacity: ;--un-backdrop-saturate: ;--un-backdrop-sepia: }.pointer-events-none{pointer-events:none}.fixed{position:fixed}.left-0{left:0}.right-0{right:0}.z-10{z-index:10}.mb-6{margin-bottom:1.5rem}.mb-8{margin-bottom:2rem}.h-auto{height:auto}.min-h-screen{min-height:100vh}.flex{display:flex}.flex-1{flex:1 1 0%}.flex-col{flex-direction:column}.overflow-y-auto{overflow-y:auto}.rounded-t-md{border-top-left-radius:.375rem;border-top-right-radius:.375rem}.bg-black\\/5{background-color:#0000000d}.bg-white{--un-bg-opacity:1;background-color:rgb(255 255 255/var(--un-bg-opacity))}.p-8{padding:2rem}.px-10{padding-left:2.5rem;padding-right:2.5rem}.pt-14{padding-top:3.5rem}.text-6xl{font-size:3.75rem;line-height:1}.text-xl{font-size:1.25rem;line-height:1.75rem}.text-black{--un-text-opacity:1;color:rgb(0 0 0/var(--un-text-opacity))}.font-light{font-weight:300}.font-medium{font-weight:500}.leading-tight{line-height:1.25}.font-sans{font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji}.antialiased{-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}@media (prefers-color-scheme:dark){.dark\\:bg-black{--un-bg-opacity:1;background-color:rgb(0 0 0/var(--un-bg-opacity))}.dark\\:bg-white\\/10{background-color:#ffffff1a}.dark\\:text-white{--un-text-opacity:1;color:rgb(255 255 255/var(--un-text-opacity))}}@media (min-width:640px){.sm\\:text-2xl{font-size:1.5rem;line-height:2rem}.sm\\:text-8xl{font-size:6rem;line-height:1}}</style><script>!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll(\'link[rel="modulepreload"]\'))r(e);new MutationObserver((e=>{for(const o of e)if("childList"===o.type)for(const e of o.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&r(e)})).observe(document,{childList:!0,subtree:!0})}function r(e){if(e.ep)return;e.ep=!0;const r=function(e){const r={};return e.integrity&&(r.integrity=e.integrity),e.referrerPolicy&&(r.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?r.credentials="include":"anonymous"===e.crossOrigin?r.credentials="omit":r.credentials="same-origin",r}(e);fetch(e.href,r)}}();<\/script></head><body class="antialiased bg-white dark:bg-black dark:text-white flex flex-col font-sans min-h-screen pt-14 px-10 text-black"><div class="fixed left-0 pointer-events-none right-0 spotlight"></div><h1 class="font-medium mb-6 sm:text-8xl text-6xl">'+F(e.statusCode)+'</h1><p class="font-light leading-tight mb-8 sm:text-2xl text-xl">'+F(e.description)+'</p><div class="bg-black/5 bg-white dark:bg-white/10 flex-1 h-auto overflow-y-auto rounded-t-md"><div class="font-light leading-tight p-8 text-xl z-10">'+F(e.stack)+"</div></div></body></html>")}),oo=Object.freeze({__proto__:null,template:""}),ro=Object.freeze({__proto__:null,default:{}}),so=e.env.CLOUDFLARE_API_URL||"https://bapi.handmadein.ro",cloudflareApiFetch$1=async(e,t={})=>{const o={"Content-Type":"application/json","Accept-Language":"ro","x-locale":"ro",...t.headers},r=e.startsWith("http")?e:`${so}${e}`;try{const e=await fetch(r,{...t,headers:o});if(!e.ok){const t=await e.json().catch((()=>({message:"An unknown error occurred"})));throw new Error(t.message||"Request failed")}return await e.json()}catch(e){throw console.error("Cloudflare API fetch error:",e),e}},logCookieOperation=(e,t,o)=>{console.log(`Cookie ${e}: ${t}`,o?"[SET]":"[CLEARED]")},setAuthCookie=(e,t,o)=>{S(e,t,o,Tt),logCookieOperation("SET",t,o)},clearAuthCookie=(e,t)=>{D(e,t,{...Tt,maxAge:0,expires:new Date(0)}),logCookieOperation("CLEAR",t,null)},no=d((async e=>{const t=e.node.req.method,o=L(e,"auth")||"";try{switch(o){case"signUp":if("POST"===t)return await(async e=>{try{const t=await E(e),{email:o,password:r,firstName:s,lastName:n,phone:a,acceptsMarketing:i}=t,c=await cloudflareApiFetch$1("/auth/customer/register",{method:"POST",body:JSON.stringify({email:o,password:r,first_name:s,last_name:n,phone:a,accepts_marketing:i})});if(c.success&&c.data){const{customer:t,token:o}=c.data,r={isAuthenticated:!0,userId:t.id,username:t.email,email:t.email,name:`${t.first_name} ${t.last_name}`,given_name:t.first_name,family_name:t.last_name,phone_number:t.phone,accessToken:o,customer:t};return setAuthCookie(e,Rt,JSON.stringify(r)),{isSignUpComplete:!0,signUpStep:"DONE",userId:t.id,username:t.email,email:t.email,nextStep:null,isSignedIn:!0,authenticationFlowType:"USER_SRP_AUTH"}}throw new Error(c.error||"Registration failed")}catch(e){throw console.error("Registration error:",e),R({statusCode:400,statusMessage:e instanceof Error?e.message:"Registration failed"})}})(e);break;case"signIn":if("POST"===t)return await(async e=>{try{const t=await E(e),{username:o,password:r}=t,s=await cloudflareApiFetch$1("/auth/customer/login",{method:"POST",body:JSON.stringify({email:o,password:r})});if(s.success&&s.data){const{customer:t,token:o}=s.data,r={isAuthenticated:!0,userId:t.id,username:t.email,email:t.email,name:`${t.first_name} ${t.last_name}`,given_name:t.first_name,family_name:t.last_name,phone_number:t.phone,accessToken:o,customer:t};return setAuthCookie(e,Rt,JSON.stringify(r)),{isSignedIn:!0,nextStep:null,userId:t.id,username:t.email,email:t.email,authenticationFlowType:"USER_SRP_AUTH",challengeName:null,challengeParameters:{},signInDetails:{loginId:t.email,authFlowType:"USER_SRP_AUTH"}}}throw new Error(s.error||"Invalid credentials")}catch(e){throw console.error("Sign in error:",e),R({statusCode:401,statusMessage:e instanceof Error?e.message:"Authentication failed"})}})(e);break;case"signOut":if("POST"===t)return await(async e=>{try{clearAuthCookie(e,Rt);try{await cloudflareApiFetch$1("/auth/logout",{method:"POST"})}catch(e){console.warn("Backend logout failed:",e)}return{isSignedOut:!0,nextStep:null}}catch(e){throw console.error("Sign out error:",e),R({statusCode:500,statusMessage:"Failed to sign out"})}})(e);break;case"getCurrentUser":if("GET"===t)return await(async e=>{try{T(e).forceRefresh;const t=U(e,Rt);if(!t)return{isSignedIn:!1,nextStep:null,userId:null,username:null,signInDetails:null};try{const o=JSON.parse(t);if(!o.accessToken)throw new Error("No access token in cookie");const r=await cloudflareApiFetch$1("/auth/me",{method:"GET",headers:{Authorization:`Bearer ${o.accessToken}`}});if(r.success&&r.data){const{customer:t}=r.data,s={isAuthenticated:!0,userId:t.id,username:t.email,email:t.email,name:`${t.first_name} ${t.last_name}`,given_name:t.first_name,family_name:t.last_name,phone_number:t.phone,accessToken:o.accessToken,customer:t};return setAuthCookie(e,Rt,JSON.stringify(s)),{isSignedIn:!0,nextStep:null,userId:t.id,username:t.email,email:t.email,userAttributes:{email:t.email,given_name:t.first_name,family_name:t.last_name,phone_number:t.phone},signInDetails:{loginId:t.email,authFlowType:"USER_SRP_AUTH"}}}}catch(t){console.error("Token validation failed:",t),clearAuthCookie(e,Rt)}return{isSignedIn:!1,nextStep:null,userId:null,username:null,signInDetails:null}}catch(e){return console.error("Get current user error:",e),{isSignedIn:!1,nextStep:null,userId:null,username:null,signInDetails:null}}})(e);break;case"confirmSignUp":if("POST"===t)return await(async e=>{const t=await E(e),{username:o}=t;return{isSignUpComplete:!0,nextStep:null,userId:o,username:o}})(e);break;case"resetPassword":if("POST"===t)return await(async e=>{const t=await E(e),{username:o}=t;return{isPasswordReset:!0,nextStep:{resetPasswordStep:"CONFIRM_RESET_PASSWORD_WITH_CODE",additionalInfo:{deliveryMedium:"EMAIL",destination:o}}}})(e);break;case"confirmResetPassword":if("POST"===t)return await(async()=>({isPasswordReset:!0,nextStep:null}))();break;case"resendCode":if("POST"===t)return await(async e=>{const t=await E(e),{username:o}=t;return{isCodeSent:!0,destination:o,deliveryMedium:"EMAIL"}})(e);break;case"updateUser":if("POST"===t)return await(async()=>({isUpdateComplete:!0,nextStep:null}))();break;case"changePassword":if("POST"===t)return await(async()=>({isPasswordChanged:!0,nextStep:null}))();break;default:throw R({statusCode:404,statusMessage:`Auth endpoint not found: ${o}`})}throw R({statusCode:405,statusMessage:`Method ${t} not allowed for ${o}`})}catch(e){if(console.error(`Auth API error for ${o}:`,e),e.statusCode)throw e;throw R({statusCode:500,statusMessage:e instanceof Error?e.message:"Internal server error"})}})),ao=Object.freeze({__proto__:null,default:no}),io=d((async e=>{try{const t=await E(e),o=x(e);o.origin||o.referer;return!0===t.clear?(console.log("AUTH DEBUG: Clearing auth cookie via API endpoint"),S(e,Rt,"",{httpOnly:!1,path:"/",maxAge:0,sameSite:"lax"}),{success:!0,message:"Auth cookie cleared successfully"}):(console.log("AUTH DEBUG: Attempting to fix cookie via API endpoint"),t.userData?(S(e,Rt,t.userData,{httpOnly:!1,path:"/",sameSite:"lax",secure:!1,maxAge:2592e3}),S(e,"cookie_test","works",{httpOnly:!1,path:"/",sameSite:"lax",maxAge:86400}),console.log("AUTH DEBUG: Server set auth cookie via API endpoint"),{success:!0,message:"Auth cookie set successfully"}):(console.error("AUTH DEBUG: No userData provided to cookie-fix endpoint"),{success:!1,message:"No auth data provided"}))}catch(e){return console.error("AUTH DEBUG: Error setting cookie via API:",e),{success:!1,message:"Failed to set auth cookie",error:e.message}}})),co=Object.freeze({__proto__:null,default:io}),lo=e.env.CLOUDFLARE_API_URL||"https://bapi.handmadein.ro",uo=d((async e=>{console.log("AUTH DEBUG: /api/auth/current-user endpoint called");try{const t=U(e,"handmadein_auth");if(console.log("AUTH DEBUG: handmadein_auth cookie exists:",!!t),!t)return console.log("AUTH DEBUG: No auth cookie found, returning unauthenticated"),{isAuthenticated:!1,userId:null,username:null,email:null};try{const e=JSON.parse(t);if(console.log("AUTH DEBUG: Cookie parsed successfully, cookie data:",e),e&&e.isAuthenticated&&e.accessToken){console.log("AUTH DEBUG: Cookie indicates user is authenticated, verifying with backend");try{const t=await(async(e,t={})=>{const o={"Content-Type":"application/json","Accept-Language":"ro","x-locale":"ro",...t.headers},r=e.startsWith("http")?e:`${lo}${e}`;try{const e=await fetch(r,{...t,headers:o});if(!e.ok){const t=await e.json().catch((()=>({message:"An unknown error occurred"})));throw new Error(t.message||"Request failed")}return await e.json()}catch(e){throw console.error("Cloudflare API fetch error:",e),e}})("/auth/me",{method:"GET",headers:{Authorization:`Bearer ${e.accessToken}`}});if(t.success&&t.data){const{customer:e}=t.data;return console.log("AUTH DEBUG: Backend verification successful"),{isAuthenticated:!0,userId:e.id,username:e.email,email:e.email,name:`${e.first_name} ${e.last_name}`,given_name:e.first_name,family_name:e.last_name,phone_number:e.phone,customer:e}}}catch(e){return console.error("AUTH DEBUG: Backend verification failed:",e),{isAuthenticated:!1,userId:null,username:null,email:null}}}}catch(e){console.error("AUTH DEBUG: Error parsing auth cookie:",e)}return console.log("AUTH DEBUG: No valid authentication found"),{isAuthenticated:!1,userId:null,username:null,email:null}}catch(e){return console.error("AUTH DEBUG: Error in current-user endpoint:",e),{isAuthenticated:!1,userId:null,username:null,email:null}}})),ho=Object.freeze({__proto__:null,default:uo}),mo=d((e=>{try{const t=useRuntimeConfig();return{cognitoRegion:t.cognitoRegion?"configured":"missing",cognitoUserPoolId:t.cognitoUserPoolId?"configured":"missing",cognitoClientId:t.cognitoClientId?"configured":"missing",cognitoClientSecret:t.cognitoClientSecret?"configured":"missing",cloudflareApiUrl:t.public.cloudflareApiUrl?"configured":"missing",apiUrl:t.public.apiUrl?"configured":"missing",session:e.context.session?{hasSession:!0,keys:Object.keys(e.context.session),generatedUsername:e.context.session.generatedUsername?"present":"missing",userEmail:e.context.session.userEmail?"present":"missing"}:{hasSession:!1},allConfigKeys:Object.keys(t),publicConfigKeys:Object.keys(t.public||{})}}catch(e){throw console.error("Error in debug endpoint:",e),R({statusCode:500,message:e instanceof Error?e.message:"Unknown error"})}})),po=Object.freeze({__proto__:null,default:mo}),fo=d((async e=>{try{const t=await E(e),{email:o,password:r}=t;if(!o||!r)throw R({statusCode:400,message:"Email and password are required"});const s="https://bapi.handmadein.ro";console.log(`Using Cloudflare backend for authentication: ${s}`);const n=await fetch(`${s}/auth/customer/login`,{method:"POST",headers:{"Content-Type":"application/json","Accept-Language":"ro","x-locale":"ro"},body:JSON.stringify({email:o,password:r})});if(!n.ok){const e=await n.json().catch((()=>({})));throw R({statusCode:n.status,message:e.message||e.error||"Authentication failed"})}const a=await n.json();if(a.success&&a.data)return{success:!0,data:a.data,message:a.message||"Login successful"};throw R({statusCode:400,message:a.error||a.message||"Authentication failed"})}catch(e){if(console.error("Login error:",e),e.statusCode)throw e;throw R({statusCode:500,message:e.message||"Authentication failed"})}})),go=Object.freeze({__proto__:null,default:fo}),yo=d((async e=>{try{const{default:t}=await Promise.resolve().then((function(){return ao})),o=await E(e);e.context.body=o,e.context.params={_:"signin"};return await t(e)}catch(e){throw console.error("Signin error:",e),R({statusCode:e.statusCode||500,message:e.message||"Authentication failed"})}})),wo=Object.freeze({__proto__:null,default:yo}),bo=d((async e=>{try{return D(e,Rt,{path:"/",httpOnly:!1,secure:!1,sameSite:"lax"}),{success:!0,message:"Signed out successfully"}}catch(e){return console.error("Error during signout:",e),{success:!1,error:"Failed to sign out"}}})),_o=Object.freeze({__proto__:null,default:bo}),vo=d((async e=>{try{const t=await E(e),{email:o,name:r}=t;if(!o)throw R({statusCode:400,message:"Email is required"});useRuntimeConfig().medusaUrl;const s=await async function(){return console.warn("syncCognitoToMedusaDirectly is deprecated - using new Cloudflare backend authentication"),null}(0);return s?{success:!0,token:s}:{success:!1,message:"Failed to sync with Medusa"}}catch(e){throw console.error("Error syncing with Medusa:",e),R({statusCode:e.statusCode||500,message:e.message||"An error occurred while syncing with Medusa"})}})),Co=Object.freeze({__proto__:null,default:vo}),xo=d((async t=>{let o=null;try{o=await E(t);const r=e.env.CLOUDFLARE_BACKEND_URL||"https://bapi.handmadein.ro";return await $fetch(`${r}/contact`,{method:"POST",headers:{"Content-Type":"application/json"},body:o})}catch(e){if(console.error("Error submitting contact form:",e),e&&"object"==typeof e){if("statusCode"in e){const t=e.statusCode||500,r=e.statusMessage||e.message||"Failed to submit contact form";throw 400===t&&(console.error("Contact form validation error:",r),console.error("Request body was:",o)),R({statusCode:t,statusMessage:r})}if("data"in e&&e.data&&"object"==typeof e.data){const t=e.data,r=t.statusCode||500,s=t.error||t.message||"Failed to submit contact form";throw 400===r&&(console.error("Contact form validation error:",s),console.error("Request body was:",o)),R({statusCode:r,statusMessage:s})}}throw R({statusCode:500,statusMessage:"Failed to submit contact form"})}})),Ao=Object.freeze({__proto__:null,default:xo}),ko=d((async e=>{var t,o;try{const r=B(e),s=r[Rt]||r.auth;if(!s)throw R({statusCode:401,message:"Authentication required: No auth cookie found"});let n,a;try{if(n=JSON.parse(s),a=n.accessToken,!n.isAuthenticated||!a)throw new Error("No valid access token")}catch(e){throw console.error("Error parsing auth cookie:",e),R({statusCode:401,message:"Authentication required: Invalid auth cookie"})}useRuntimeConfig();const i="https://bapi.handmadein.ro";if(console.log("Using Cloudflare API URL:",i),"GET"===e.method){console.log("Handling GET request for addresses");const e=await fetch(`${i}/store/customers/me/addresses`,{method:"GET",headers:{Authorization:`Bearer ${a}`,"Content-Type":"application/json"}});if(!e.ok){const t=await e.text();throw console.error("Error fetching addresses:",t),R({statusCode:e.status,message:`Failed to fetch addresses: ${t}`})}const t=await e.json();return console.log("Addresses fetched successfully"),{shipping_addresses:t.data||[]}}if("POST"===e.method){console.log("Handling POST request for address creation");const r=await E(e);if(!r)throw R({statusCode:400,message:"Address data is required"});console.log("[DEBUG] Incoming address data:",JSON.stringify(r,null,2));const s=await fetch(`${i}/store/customers/me/addresses`,{method:"GET",headers:{Authorization:`Bearer ${a}`,"Content-Type":"application/json"}});let n=[];if(s.ok){n=(await s.json()).data||[],console.log(`[DEBUG] Found ${n.length} existing addresses`)}else console.log("[DEBUG] Failed to fetch existing addresses or no addresses found");const isDuplicateAddress=(e,t)=>{const normalizeField=e=>(e||"").toString().toLowerCase().trim();return["first_name","last_name","company","address_line_1","address_1","address_line_2","address_2","city","postal_code","country_code","phone"].every((o=>{let r=e[o]||e["address_line_1"===o?"address_1":"address_line_2"===o?"address_2":o]||"",s=t[o]||t["address_line_1"===o?"address_1":"address_line_2"===o?"address_2":o]||"";return r=normalizeField(r),s=normalizeField(s),r===s}))},c=n.find((e=>isDuplicateAddress(e,r)));if(c){console.log("[DEBUG] Duplicate address found:",c.id);const e=(null==(t=r.metadata)?void 0:t.address_type)||r.type||"shipping",o=c.type||"shipping";if(e!==o&&"both"!==o){console.log("[DEBUG] Updating existing address to support both types");const e={first_name:r.first_name||"",last_name:r.last_name||"",company:r.company||"",address_line_1:r.address_1||r.address_line_1||"",address_line_2:r.address_2||r.address_line_2||"",city:r.city||"",state:r.province||r.state||"",postal_code:r.postal_code||"",country_code:r.country_code||"RO",phone:r.phone||"",type:"both",is_default:c.is_default||!1};console.log("[DEBUG] Update data being sent:",JSON.stringify(e,null,2));const t=await fetch(`${i}/store/customers/me/addresses/${c.id}`,{method:"PUT",headers:{Authorization:`Bearer ${a}`,"Content-Type":"application/json"},body:JSON.stringify(e)});if(t.ok){const e=await t.json();return console.log("[DEBUG] Successfully updated existing address with new data"),{success:!0,data:e.data||e,message:"Address updated with new data to support both shipping and billing"}}{const e=await t.text();console.error("[DEBUG] Failed to update existing address:",e)}}console.log("[DEBUG] Same type but user explicitly wants to save - creating new address")}const d={first_name:r.first_name||"",last_name:r.last_name||"",company:r.company||"",address_line_1:r.address_1||r.address_line_1||"",address_line_2:r.address_2||r.address_line_2||"",city:r.city||"",state:r.province||r.state||"",postal_code:r.postal_code||"",country_code:r.country_code||"RO",phone:r.phone||"",type:(null==(o=r.metadata)?void 0:o.address_type)||r.type||"shipping",is_default:!1};console.log("[DEBUG] Creating new address with data:",JSON.stringify(d,null,2));const l=await fetch(`${i}/store/customers/me/addresses`,{method:"POST",headers:{Authorization:`Bearer ${a}`,"Content-Type":"application/json"},body:JSON.stringify(d)});if(!l.ok){const e=await l.text();throw console.error("Error creating address:",e),console.error("Request payload was:",JSON.stringify(d,null,2)),R({statusCode:l.status,message:`Failed to create address: ${e}`})}const u=await l.json();console.log("New address created successfully:",u);const h=await fetch(`${i}/store/customers/me/addresses`,{method:"GET",headers:{Authorization:`Bearer ${a}`,"Content-Type":"application/json"}});if(h.ok){const e=(await h.json()).data||[];console.log(`[DEBUG] Total addresses after creation: ${e.length}`),console.log("[DEBUG] All addresses after creation:",e.map((e=>({id:e.id,address_1:e.address_line_1||e.address_1,type:e.type,is_default:e.is_default}))));const t=e.find((e=>{var t;return e.id===((null==(t=u.data)?void 0:t.id)||u.id)||(e.address_line_1||e.address_1)===d.address_line_1}));t?console.log("[DEBUG] New address confirmed in address list:",t.id):console.error("[DEBUG] WARNING: New address was not found in the list after creation!");const o=n.length,r=e.length;r<=o&&console.warn(`[DEBUG] WARNING: Address count did not increase as expected. Before: ${o}, After: ${r}`)}return{success:!0,data:u.data||u,message:"Address created successfully"}}throw R({statusCode:405,message:"Method not allowed"})}catch(e){throw console.error("Error in addresses endpoint:",e.message),R({statusCode:e.statusCode||500,message:e.message||"An unknown error occurred"})}})),jo=Object.freeze({__proto__:null,default:ko}),Uo=d((async e=>{try{const t=L(e,"id");if(console.log("Address ID from URL:",t),!t)throw R({statusCode:400,message:"Address ID is required"});const o=B(e),r=o[Rt]||o.auth;if(!r)throw R({statusCode:401,message:"Authentication required: No auth cookie found"});let s,n;try{if(s=JSON.parse(r),n=s.accessToken,!s.isAuthenticated||!n)throw new Error("No valid access token")}catch(e){throw console.error("Error parsing auth cookie:",e),R({statusCode:401,message:"Authentication required: Invalid auth cookie"})}useRuntimeConfig();const a="https://bapi.handmadein.ro";if(console.log("Using Cloudflare API URL:",a),"PUT"===e.method||"POST"===e.method){console.log("Handling PUT/POST request for address update");const o=await E(e);if(!o)throw R({statusCode:400,message:"Address data is required"});console.log(`Updating address ${t} with data:`,o);const r=await fetch(`${a}/store/customers/me/addresses/${t}`,{method:"PUT",headers:{"Content-Type":"application/json",Authorization:`Bearer ${n}`},body:JSON.stringify(o)});if(!r.ok){const e=await r.text();throw console.error("Error updating address:",e),R({statusCode:r.status,message:`Failed to update address: ${e}`})}const s=await r.json();return console.log("Address updated successfully"),s}if("DELETE"===e.method){console.log(`Deleting address ${t}`);const e=await fetch(`${a}/store/customers/me/addresses/${t}`,{method:"DELETE",headers:{Authorization:`Bearer ${n}`}});if(!e.ok){const t=await e.text();throw console.error("Error deleting address:",t),R({statusCode:e.status,message:`Failed to delete address: ${t}`})}return console.log("Address deleted successfully"),{success:!0}}throw R({statusCode:405,message:"Method not allowed"})}catch(e){throw console.error("Error in address ID endpoint:",e.message),R({statusCode:e.statusCode||500,message:e.message||"An unknown error occurred"})}})),So=Object.freeze({__proto__:null,default:Uo}),Po=d((async e=>{try{const t=B(e),o=t[Rt]||t.auth;if(!o)throw R({statusCode:401,message:"Authentication required: No auth cookie found"});let r,s;try{if(r=JSON.parse(o),s=r.accessToken,!r.isAuthenticated||!s)throw new Error("No valid access token")}catch(e){throw console.error("Error parsing auth cookie:",e),R({statusCode:401,message:"Authentication required: Invalid auth cookie"})}useRuntimeConfig();const n="https://bapi.handmadein.ro";if(console.log("Using Cloudflare API URL:",n),"GET"===e.method){console.log("Handling GET request for customer details");const e=await fetch(`${n}/store/customers/me`,{method:"GET",headers:{Authorization:`Bearer ${s}`,"Content-Type":"application/json"}});if(!e.ok){const t=await e.text();throw console.error("Error fetching customer details:",t),R({statusCode:e.status,message:`Failed to fetch customer details: ${t}`})}const t=await e.json();return console.log("Customer details fetched successfully"),t}if("POST"===e.method){console.log("Handling POST request for customer update");const t=await E(e);if(!t)throw R({statusCode:400,message:"Customer data is required"});console.log("Updating customer with data:",t);const o=await fetch(`${n}/store/customers/me`,{method:"PUT",headers:{"Content-Type":"application/json",Authorization:`Bearer ${s}`},body:JSON.stringify(t)});if(!o.ok){const e=await o.text();throw console.error("Error updating customer:",e),R({statusCode:o.status,message:`Failed to update customer: ${e}`})}const r=await o.json();return console.log("Customer updated successfully"),r}throw R({statusCode:405,message:"Method not allowed"})}catch(e){throw console.error("Error in customer/me endpoint:",e.message),R({statusCode:e.statusCode||500,message:e.message||"An unknown error occurred"})}})),Ro=Object.freeze({__proto__:null,default:Po}),To=d((async e=>{const t=await getSession(e);if(!t||!t.isAuthenticated)throw R({statusCode:401,message:"Unauthorized"});if("POST"!==e.node.req.method)throw R({statusCode:405,message:"Method not allowed"});try{const{current_password:o,new_password:r}=await E(e);if(!o||!r)throw R({statusCode:400,message:"Both current and new password are required"});const s="https://bapi.handmadein.ro",n=await fetch(`${s}/store/customers/me/password`,{method:"PUT",headers:{"Content-Type":"application/json","Accept-Language":"ro","x-locale":"ro",Authorization:`Bearer ${t.accessToken}`},body:JSON.stringify({current_password:o,new_password:r})});if(!n.ok){const e=await n.json().catch((()=>({error:"Request failed"})));throw R({statusCode:n.status,message:e.error||e.message||"Failed to update password"})}return{success:!0,message:(await n.json()).message||"Password updated successfully"}}catch(e){if(e.statusCode)throw e;throw R({statusCode:500,message:e.message||"Failed to update password"})}})),Eo=Object.freeze({__proto__:null,default:To}),$o=d((async t=>{let o=null;try{if(o=await E(t),!o.email||"string"!=typeof o.email)throw R({statusCode:400,statusMessage:"Email is required"});const r=e.env.CLOUDFLARE_BACKEND_URL||"https://bapi.handmadein.ro",s={email:o.email,metadata:o.metadata||{}};console.log("Newsletter subscription request body:",s);return await $fetch(`${r}/newsletter/subscribe`,{method:"POST",headers:{"Content-Type":"application/json"},body:s})}catch(e){if(console.error("Error subscribing to newsletter:",e),e&&"object"==typeof e&&"statusCode"in e&&400===e.statusCode&&o&&console.error("400 Bad Request - Request body was:",{email:o.email,metadata:o.metadata||{}}),e&&"object"==typeof e){if("statusCode"in e){const t=e;throw 400===t.statusCode&&t.data&&console.error("400 Bad Request details:",t.data),R({statusCode:t.statusCode||500,statusMessage:t.statusMessage||t.message||"Failed to subscribe to newsletter"})}if("data"in e&&e.data&&"object"==typeof e.data){const t=e.data;throw 400===t.statusCode&&console.error("400 Bad Request details:",t),R({statusCode:t.statusCode||500,statusMessage:t.error||t.message||"Failed to subscribe to newsletter"})}}throw R({statusCode:500,statusMessage:"Failed to subscribe to newsletter"})}})),Oo=Object.freeze({__proto__:null,default:$o}),No=d((async t=>{try{const o=await E(t);if(!o.email||"string"!=typeof o.email)throw R({statusCode:400,statusMessage:"Email is required"});const r=e.env.CLOUDFLARE_BACKEND_URL||"https://bapi.handmadein.ro";return await $fetch(`${r}/newsletter/unsubscribe`,{method:"POST",headers:{"Content-Type":"application/json"},body:{email:o.email}})}catch(e){if(console.error("Error unsubscribing from newsletter:",e),e&&"object"==typeof e){if("statusCode"in e)throw R({statusCode:e.statusCode||500,statusMessage:e.statusMessage||e.message||"Failed to unsubscribe from newsletter"});if("data"in e&&e.data&&"object"==typeof e.data){const t=e.data;throw R({statusCode:t.statusCode||500,statusMessage:t.error||t.message||"Failed to unsubscribe from newsletter"})}}throw R({statusCode:500,statusMessage:"Failed to unsubscribe from newsletter"})}})),Io=Object.freeze({__proto__:null,default:No}),zo=d((async e=>{var t;try{const o=null==(t=e.context.params)?void 0:t.id;if(!o)throw R({statusCode:400,message:"Order ID is required"});console.log("Fetching order with ID:",o);const r=B(e),s=r[Rt]||r.auth;if(!s)throw R({statusCode:401,message:"Authentication required"});const n=useRuntimeConfig().public.cloudflareApiUrl||"https://bapi.handmadein.ro";let a;try{a=JSON.parse(s)}catch(e){throw R({statusCode:401,message:"Invalid authentication data"})}const i=await fetch(`${n}/store/orders/${o}`,{method:"GET",headers:{Authorization:`Bearer ${a.accessToken}`,"Content-Type":"application/json"}});if(!i.ok){const e=await i.json().catch((()=>({})));throw console.error("Error fetching order details:",e),R({statusCode:i.status,message:`Failed to fetch order details: ${e.message||i.statusText}`})}const c=await i.json();console.log("Order details retrieved successfully");return{order:c.success?c.data:c.order}}catch(e){throw console.error("Error in order details endpoint:",e.message),R({statusCode:e.statusCode||500,message:e.message||"An unknown error occurred"})}})),Lo=Object.freeze({__proto__:null,default:zo}),Do=d((async e=>{var t;try{const o=useRuntimeConfig(),r=o.medusaUrl||"https://api.handmadein.ro",s=o.medusaAdminToken;if(!s)throw console.error("Admin token not found in configuration"),R({statusCode:500,message:"Server configuration error"});k(e);const n=null==(t=e.context.params)?void 0:t.id;if(!n)throw R({statusCode:400,message:"Order ID is required"});console.log(`Processing order update for order ID: ${n}`);const a=(await E(e)).customerId;if(!a)throw R({statusCode:400,message:"Customer ID is required"});console.log(`Updating order ${n} to associate with customer ${a}`);const i=await getSession(e);if(!i||!i.isAuthenticated||!i.email)throw R({statusCode:401,message:"Authentication required"});const c=await fetch(`${r}/admin/orders/${n}`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${s}`},body:JSON.stringify({customer_id:a})});if(!c.ok){const e=await c.json().catch((()=>({})));throw console.error("Error updating order customer association:",e),R({statusCode:c.status,message:`Failed to update order: ${e.message||c.statusText}`})}const d=await c.json();return console.log(`Successfully updated order ${n} with customer ${a}`),{success:!0,order:d.order}}catch(e){throw console.error("Error in update-customer endpoint:",e.message),R({statusCode:e.statusCode||500,message:e.message||"An unknown error occurred"})}})),Bo=Object.freeze({__proto__:null,default:Do});const Mo=d((async e=>{var t,o,r,s;try{const n=B(e);console.log("Cookies received:",Object.keys(n));const a=n[Rt]||n.auth;if(console.log("Auth cookie found:",a?"Yes":"No"),!a)throw R({statusCode:401,message:"Authentication required"});const i=useRuntimeConfig().medusaUrl||"https://api.handmadein.ro";console.log("Using Medusa URL:",i);let c=null,d=null;try{console.log("Parsing auth cookie"),c=JSON.parse(a),c.isAuthenticated&&c.email&&(d=c.email,console.log("Customer email found:",d))}catch(e){console.error("Error parsing auth cookie:",e);try{c=JSON.parse(decodeURIComponent(a)),c.isAuthenticated&&c.email&&(d=c.email,console.log("Customer email found after decoding:",d))}catch(e){console.error("Error decoding and parsing auth cookie:",e)}}if(!d&&(null==(t=null==c?void 0:c.user)?void 0:t.email)&&(d=c.user.email,console.log("Customer email found in user object:",d)),!d&&(null==(r=null==(o=null==c?void 0:c.medusa)?void 0:o.customer)?void 0:r.email)&&(d=c.medusa.customer.email,console.log("Customer email found in medusa.customer object:",d)),!d)throw console.error("No customer email found in auth cookie"),R({statusCode:401,message:"Authentication required - customer email not found"});console.log("Getting admin token...");const l=await async function(e){try{console.log("Obtaining admin token");const t=await fetch(`${e}/auth/user/emailpass`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:"<EMAIL>",password:"Business95!"})});if(!t.ok){const e=await t.json().catch((()=>({})));throw console.error("Admin login failed:",e),new Error(`Admin login failed: ${e.message||t.statusText}`)}const o=await t.json();if(o.token)return console.log("Admin login successful, received JWT token"),o.token;throw new Error("Admin login response did not contain a token")}catch(e){throw console.error("Error getting admin token:",e.message),e}}(i);console.log("Admin token obtained successfully"),console.log("Looking up customer...");const u=await async function(e,t,o){try{console.log(`Looking up customer with email: ${e}`);const r=await fetch(`${t}/admin/customers?q=${encodeURIComponent(e)}`,{method:"GET",headers:{"Content-Type":"application/json",Authorization:`Bearer ${o}`}});if(!r.ok){const e=await r.json().catch((()=>({})));throw console.error("Customer search failed:",e),new Error(`Customer search failed: ${e.message||r.statusText}`)}const s=await r.json();if(s.customers&&s.customers.length>0){if(console.log(`Found ${s.customers.length} customer records with this email`),s.customers.forEach(((e,t)=>{var o;console.log(`Customer ${t+1}: ID=${e.id}, \n          Has orders: ${Boolean(null==(o=e.orders)?void 0:o.length)}, \n          Has metadata: ${Boolean(e.metadata&&Object.keys(e.metadata).length)},\n          Created at: ${e.created_at}`)})),1===s.customers.length){const e=s.customers[0];return console.log(`Single customer found with ID: ${e.id}`),e}const e=s.customers.find((e=>e.orders&&e.orders.length>0));if(e)return console.log(`Selected customer with orders, ID: ${e.id}`),e;const t=s.customers.find((e=>!0===e.has_account||e.metadata&&!0===e.metadata.has_account));if(t)return console.log(`Selected customer with account flag, ID: ${t.id}`),t;const o=s.customers.filter((e=>e.metadata&&Object.keys(e.metadata).length>0)).sort(((e,t)=>Object.keys(t.metadata||{}).length-Object.keys(e.metadata||{}).length));if(o.length)return console.log(`Selected customer with most metadata, ID: ${o[0].id}`),o[0];const r=[...s.customers].sort(((e,t)=>new Date(e.created_at).getTime()-new Date(t.created_at).getTime()));return console.log(`Selected oldest customer record, ID: ${r[0].id}`),r[0]}return null}catch(e){throw console.error("Error in findCustomerByEmail:",e),e}}(d,i,l);if(!u)throw R({statusCode:404,message:"Customer not found"});console.log("Customer found:",u.id),console.log(`Fetching orders for customer: ${u.id}`);const h=await fetch(`${i}/admin/orders?customer_id=${u.id}`,{method:"GET",headers:{Authorization:`Bearer ${l}`,"Content-Type":"application/json"}});if(!h.ok){const e=await h.json().catch((()=>({})));throw console.error("Error fetching customer orders:",e),R({statusCode:h.status,message:`Failed to fetch customer orders: ${e.message||h.statusText}`})}const m=await h.json();return console.log(`Retrieved ${(null==(s=m.orders)?void 0:s.length)||0} orders successfully`),{orders:m.orders||[],count:m.count||0,offset:m.offset||0,limit:m.limit||20}}catch(e){throw console.error("Error in orders endpoint:",e.message),R({statusCode:e.statusCode||500,message:e.message||"An unknown error occurred"})}})),Ho=Object.freeze({__proto__:null,default:Mo}),Fo=d((async e=>{var t;const o="https://bapi.handmadein.ro",r=null==(t=e.context.params)?void 0:t.id;if(!r)throw R({statusCode:400,message:"Product ID is required"});try{console.log(`Fetching product with ID: ${r} from ${o}/store/products/${r}`);const e=await fetch(`${o}/store/products/${r}`,{headers:{"x-publishable-api-key":publishableKey,Accept:"application/json"}});if(!e.ok){console.error(`Error response from Medusa: ${e.status} ${e.statusText}`);let t="";try{const o=await e.json();t=JSON.stringify(o)}catch(e){t="Could not parse error response"}throw console.error(`Error details: ${t}`),R({statusCode:e.status,statusMessage:e.statusText,message:`Failed to fetch product data: ${e.statusText}`})}const t=await e.json();if(!t||"object"!=typeof t)throw console.error(`Invalid response data format: ${JSON.stringify(t)}`),R({statusCode:500,message:"Invalid response data format from Medusa API"});const s=t.product;if(!s)throw console.error(`Product not found in response: ${JSON.stringify(t)}`),R({statusCode:404,message:"Product not found"});let n=0,a="USD";if(s.variants&&s.variants.length>0){const e=s.variants[0];e.calculated_price?(n=e.calculated_price.calculated_amount,a=e.calculated_price.currency_code):e.prices&&e.prices.length>0&&(n=e.prices[0].amount,a=e.prices[0].currency_code)}return{id:s.id,title:s.title,handle:s.handle,description:s.description,thumbnail:s.thumbnail,price:n,currency:a,images:s.images||[],variants:s.variants||[]}}catch(e){console.error(`Failed to fetch product ${r}:`,e);let t=500,o="Failed to fetch product data",s="";throw e instanceof Error&&(o=e.message,"object"==typeof e&&null!==e&&("statusCode"in e&&(t=e.statusCode),"statusMessage"in e&&(s=e.statusMessage))),R({statusCode:t,statusMessage:s,message:o})}})),qo=Object.freeze({__proto__:null,default:Fo}),Go=d((async e=>{var t,o;const r="https://bapi.handmadein.ro";try{const s=(await E(e)).ids;if(!s||!Array.isArray(s)||0===s.length)throw R({statusCode:400,message:"Product IDs array is required"});const n=s.map((e=>{if("string"==typeof e){if(e.includes(":"))return e.split(":")[0];if(e.includes("_variant_"))return e.split("_variant_")[0]}return e})),a=new URLSearchParams;a.append("ids",n.join(","));const i=null==(t=e.context.query)?void 0:t.region_id;i&&a.append("region_id",i);const c=(null==(o=e.context.query)?void 0:o.currency_code)||"RON";console.log(`Fetching products from: ${r}/store/products/batch`),console.log(`Request details - Product IDs: ${n.join(", ")}, Currency: ${c}`);const d=await fetch(`${r}/store/products/batch`,{method:"POST",headers:{"Content-Type":"application/json","Accept-Language":"ro","x-locale":"ro"},body:JSON.stringify({ids:n,currency_code:c})});if(console.log(`Batch products response status: ${d.status} ${d.statusText}`),!d.ok){404===d.status&&console.error("404 error when fetching products. Request details:",{url:`${r}/store/products/batch`,productIds:n});const e=await d.text();throw console.error("Error response:",e),R({statusCode:d.status,message:`Failed to fetch products: ${d.statusText||d.status}`})}const l=await d.json();if(!l.success)throw R({statusCode:500,message:l.error||"Failed to fetch products from backend"});const u=l.data||l.products;if(!u||!Array.isArray(u))throw R({statusCode:500,message:"Invalid response format from backend"});const h={},m={};return u.forEach((e=>{if(e.variants&&e.variants.length>0){const t=e.variants[0];let o=0,r="USD";t.calculated_price?(o=t.calculated_price.calculated_amount,r=t.calculated_price.currency_code):t.prices&&t.prices.length>0&&(o=t.prices[0].amount,r=t.prices[0].currency_code),h[e.id]={id:e.id,price:o,currency:r},e.variants.forEach((e=>{let t=0,o="USD";e.calculated_price?(t=e.calculated_price.calculated_amount,o=e.calculated_price.currency_code):e.prices&&e.prices.length>0&&(t=e.prices[0].amount,o=e.prices[0].currency_code),m[e.id]={price:t,currency:o}})),s.forEach((t=>{if("string"==typeof t)if(t.includes(":")){const[s,n]=t.split(":");s===e.id&&(n&&m[n]?h[t]={id:t,price:m[n].price,currency:m[n].currency}:h[t]={id:t,price:o,currency:r})}else if(t.includes("_variant_")){const[s,n]=t.split("_variant_");if(s===e.id){const e="variant_"+n;m[e]?h[t]={id:t,price:m[e].price,currency:m[e].currency}:m[n]?h[t]={id:t,price:m[n].price,currency:m[n].currency}:h[t]={id:t,price:o,currency:r}}}else m[t]&&(h[t]={id:t,price:m[t].price,currency:m[t].currency})}))}})),h}catch(e){throw console.error("Failed to fetch product prices:",e),R({statusCode:e instanceof Error&&"statusCode"in e?e.statusCode:500,message:e instanceof Error?e.message:"Failed to fetch product prices"})}})),Wo=Object.freeze({__proto__:null,default:Go}),Jo=d((async e=>{const t=useRuntimeConfig(),o=L(e,"productId");if(!o)throw R({statusCode:400,statusMessage:"Product ID is required"});try{const e=t.public.cloudflareApiUrl||t.public.apiUrl||"https://bapi.handmadein.ro";return await $fetch(`${e}/store/reviews/product/${o}`)}catch(e){throw R({statusCode:(null==e?void 0:e.status)||500,statusMessage:(null==e?void 0:e.message)||"Failed to fetch product reviews"})}})),Qo=Object.freeze({__proto__:null,default:Jo}),Ko=d((async e=>{const t=useRuntimeConfig(),o=L(e,"productId");if(!o)throw R({statusCode:400,statusMessage:"Product ID is required"});const r=await E(e);if(!(r.rating&&r.comment&&r.customer_name&&r.customer_email))throw R({statusCode:400,statusMessage:"Rating, comment, customer name, and customer email are required"});try{const s=t.public.cloudflareApiUrl||t.public.apiUrl||"https://bapi.handmadein.ro",n=M(e,"authorization"),a={"Content-Type":"application/json"};n&&(a.Authorization=n);return await $fetch(`${s}/store/reviews/product/${o}`,{method:"POST",headers:a,body:{rating:parseInt(r.rating),comment:r.comment.trim(),customer_name:r.customer_name.trim(),customer_email:r.customer_email.trim()}})}catch(e){throw R({statusCode:(null==e?void 0:e.status)||500,statusMessage:(null==e?void 0:e.message)||"Failed to submit review"})}})),Vo=Object.freeze({__proto__:null,default:Ko}),Xo=d((async e=>{const t=useRuntimeConfig(),o=T(e).productIds;if(!o)throw R({statusCode:400,statusMessage:"Product IDs are required"});try{const e=t.public.cloudflareApiUrl||t.public.apiUrl||"https://bapi.handmadein.ro";return await $fetch(`${e}/store/reviews/ratings?productIds=${o}`)}catch(e){throw R({statusCode:(null==e?void 0:e.status)||500,statusMessage:(null==e?void 0:e.message)||"Failed to fetch product ratings"})}})),Yo=Object.freeze({__proto__:null,default:Xo});function renderPayloadJsonScript(e){const t={type:"application/json",innerHTML:e.data?se(e.data,e.ssrContext._payloadReducers):"","data-nuxt-data":vt,"data-ssr":!e.ssrContext.noSSR,id:"__NUXT_DATA__"};e.src&&(t["data-src"]=e.src);return[t,{innerHTML:`window.__NUXT__={};window.__NUXT__.config=${ne(e.ssrContext.config)}`}]}const Zo={omitLineBreaks:!1};globalThis.__buildAssetsURL=buildAssetsURL,globalThis.__publicAssetsURL=publicAssetsURL;const er=!!_t.id,tr=er?`<div${oe(_t)}>`:"",or=er?"</div>":"",rr=function(e){const t=useRuntimeConfig();return f((async o=>{const r=useNitroApp(),s={event:o,render:e,response:void 0};if(await r.hooks.callHook("render:before",s),!s.response){if(o.path===`${t.app.baseURL}favicon.ico`)return A(o,"Content-Type","image/x-icon"),C(o,"data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7");if(s.response=await s.render(o),!s.response){const e=P(o);return v(o,200===e?500:e),C(o,"No response returned from render handler: "+o.path)}}return await r.hooks.callHook("render:response",s.response,s),s.response.headers&&_(o,s.response.headers),(s.response.statusCode||s.response.statusMessage)&&v(o,s.response.statusCode,s.response.statusMessage),s.response.body}))}((async e=>{const t=useNitroApp(),o=e.path.startsWith("/__nuxt_error")?T(e):null;if(o&&!("__unenv__"in e.node.req))throw R({statusCode:404,statusMessage:"Page Not Found: /__nuxt_error"});const r=createSSRContext(e),s={mode:"server"};r.head.push(wt,s),o&&(o.statusCode&&=Number.parseInt(o.statusCode),function(e,t){e.error=!0,e.payload={error:t},e.url=t.url}(r,o));const n=getRouteRules(e);!1===n.ssr&&(r.noSSR=!0);const a=await function(e){return e.noSSR?Dt():Lt()}(r),i=await a.renderToString(r).catch((async e=>{if(r._renderResponse&&"skipping render"===e.message)return{};const t=!o&&r.payload?.error||e;throw await(r.nuxt?.hooks.callHook("app:error",t)),t})),c=[];if(await(r.nuxt?.hooks.callHook("app:rendered",{ssrContext:r,renderResult:i})),r._renderResponse)return r._renderResponse;if(r.payload?.error&&!o)throw r.payload.error;const d=n.noScripts,{styles:l,scripts:u}=G(r,a.rendererContext);r._preloadManifest&&!d&&r.head.push({link:[{rel:"preload",as:"fetch",fetchpriority:"low",crossorigin:"anonymous",href:buildAssetsURL(`builds/meta/${r.runtimeConfig.app.buildId}.json`)}]},{...s,tagPriority:"low"}),c.length&&r.head.push({style:c});const h=[];for(const e of Object.values(l))"inline"in getQuery(e.file)||h.push({rel:"stylesheet",href:a.rendererContext.buildAssetsURL(e.file),crossorigin:""});h.length&&r.head.push({link:h},s),d||(r.head.push({link:W(r,a.rendererContext)},s),r.head.push({link:J(r,a.rendererContext)},s),r.head.push({script:renderPayloadJsonScript({ssrContext:r,data:r.payload})},{...s,tagPosition:"bodyClose",tagPriority:"high"})),n.noScripts||r.head.push({script:Object.values(u).map((e=>({type:e.module?"module":null,src:a.rendererContext.buildAssetsURL(e.file),defer:!e.module||null,tagPosition:"head",crossorigin:""})))},s);const{headTags:m,bodyTags:p,bodyTagsOpen:f,htmlAttrs:g,bodyAttrs:y}=await re(r.head,Zo),w={htmlAttrs:g?[g]:[],head:normalizeChunks([m]),bodyAttrs:y?[y]:[],bodyPrepend:normalizeChunks([f,r.teleports?.body]),body:[replaceIslandTeleports(r,i.html),tr+(er?joinTags([r.teleports?.[`#${_t.id}`]]):"")+or],bodyAppend:[p]};return await t.hooks.callHook("render:html",w,{event:e}),{body:(b=w,`<!DOCTYPE html><html${joinAttrs(b.htmlAttrs)}><head>${joinTags(b.head)}</head><body${joinAttrs(b.bodyAttrs)}>${joinTags(b.bodyPrepend)}${joinTags(b.body)}${joinTags(b.bodyAppend)}</body></html>`),statusCode:P(e),statusMessage:H(e),headers:{"content-type":"text/html;charset=utf-8","x-powered-by":"Nuxt"}};var b}));function normalizeChunks(e){return e.filter(Boolean).map((e=>e.trim()))}function joinTags(e){return e.join("")}function joinAttrs(e){return 0===e.length?"":" "+e.join(" ")}const sr=Object.freeze({__proto__:null,default:rr});
//# sourceMappingURL=index.mjs.map

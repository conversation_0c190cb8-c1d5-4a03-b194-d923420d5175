import { Context } from 'hono';

/**
 * Cache warming service to preload frequently accessed data
 */
export class CacheWarmingService {
  private env: any;
  private db: any;

  constructor(env: any) {
    this.env = env;
    this.db = env.DB;
  }

  /**
   * Warm up payment methods cache
   */
  async warmPaymentMethods(): Promise<void> {
    try {
      console.log('Warming payment methods cache...');
      
      const paymentMethods = [];
      
      // Always include Cash on Delivery
      paymentMethods.push({
        id: 'cash_on_delivery',
        name: 'Plata ramburs',
        description: 'Plătiți prin ramburs la livrare',
        type: 'manual',
        enabled: true,
        metadata: {
          instructions: 'Veți plăti curierului la primirea comenzii.'
        }
      });
      
      // Check if Stripe is configured
      const isTestMode = this.env.STRIPE_ENVIRONMENT === 'test';
      const secretKey = isTestMode ? this.env.STRIPE_SECRET_KEY_TEST : this.env.STRIPE_SECRET_KEY_LIVE;
      
      if (secretKey && secretKey !== 'your-stripe-secret-key') {
        paymentMethods.push({
          id: 'stripe',
          name: 'Card bancar',
          description: 'Plat<PERSON> securizată cu cardul bancar',
          type: 'card',
          enabled: true,
          metadata: {
            accepts: ['visa', 'mastercard'],
            test_mode: isTestMode
          }
        });
      }
      
      // Add manual/wire transfer option
      paymentMethods.push({
        id: 'bank_transfer',
        name: 'Transfer bancar',
        description: 'Transfer manual în contul bancar',
        type: 'manual',
        enabled: true,
        metadata: {
          instructions: 'Veți primi detaliile de plată prin email după confirmarea comenzii.'
        }
      });
      
      await this.env.CACHE.put('payment_methods:all', JSON.stringify(paymentMethods), {
        expirationTtl: 1800
      });
      
      console.log(`Warmed payment methods cache with ${paymentMethods.length} methods`);
    } catch (error) {
      console.error('Failed to warm payment methods cache:', error);
    }
  }

  /**
   * Warm up popular products cache
   */
  async warmPopularProducts(): Promise<void> {
    try {
      console.log('Warming popular products cache...');
      
      const query = `
        SELECT 
          p.id, p.handle, p.status, p.thumbnail,
          COALESCE(pt_ro.title, pt_en.title, p.handle) as title,
          COALESCE(pt_ro.subtitle, pt_en.subtitle) as subtitle,
          COALESCE(SUM(os.total_orders), 0) as total_orders,
          COALESCE(SUM(os.total_quantity), 0) as total_quantity,
          COALESCE(AVG(os.popularity_score), 0) as popularity_score
        FROM products p
        LEFT JOIN product_translations pt_ro ON p.id = pt_ro.product_id AND pt_ro.language_code = 'ro'
        LEFT JOIN product_translations pt_en ON p.id = pt_en.product_id AND pt_en.language_code = 'en'
        LEFT JOIN order_statistics os ON p.id = os.product_id
        WHERE p.deleted_at IS NULL AND p.status = 'published'
        GROUP BY p.id
        ORDER BY popularity_score DESC, total_orders DESC
        LIMIT 20
      `;
      
      const result = await this.db.prepare(query).all();
      const popularProducts = result.results || [];
      
      // Cache different limits
      for (const limit of [5, 10, 15, 20]) {
        const limitedProducts = popularProducts.slice(0, limit);
        await this.env.CACHE.put(`popular_products:${limit}`, JSON.stringify(limitedProducts), {
          expirationTtl: 3600
        });
      }
      
      console.log(`Warmed popular products cache with ${popularProducts.length} products`);
    } catch (error) {
      console.error('Failed to warm popular products cache:', error);
    }
  }

  /**
   * Warm up featured collections cache
   */
  async warmFeaturedCollections(): Promise<void> {
    try {
      console.log('Warming featured collections cache...');
      
      const query = `
        SELECT 
          c.id, c.handle, c.is_active, c.sort_order, c.image_url,
          COALESCE(ct_ro.title, ct_en.title) as title,
          COALESCE(ct_ro.description, ct_en.description) as description,
          COUNT(DISTINCT p.id) as product_count
        FROM collections c
        LEFT JOIN collection_translations ct_ro ON c.id = ct_ro.collection_id AND ct_ro.language_code = 'ro'
        LEFT JOIN collection_translations ct_en ON c.id = ct_en.collection_id AND ct_en.language_code = 'en'
        LEFT JOIN products p ON c.id = p.collection_id AND p.deleted_at IS NULL AND p.status = 'published'
        WHERE c.deleted_at IS NULL AND c.is_active = 1 AND c.sort_order IS NOT NULL
        GROUP BY c.id
        ORDER BY c.sort_order ASC
        LIMIT 10
      `;
      
      const result = await this.db.prepare(query).all();
      const featuredCollections = result.results || [];
      
      await this.env.CACHE.put('featured_collections', JSON.stringify(featuredCollections), {
        expirationTtl: 3600
      });
      
      console.log(`Warmed featured collections cache with ${featuredCollections.length} collections`);
    } catch (error) {
      console.error('Failed to warm featured collections cache:', error);
    }
  }

  /**
   * Warm up shipping methods cache
   */
  async warmShippingMethods(): Promise<void> {
    try {
      console.log('Warming shipping methods cache...');
      
      const query = `
        SELECT 
          sm.id, sm.name, sm.description, sm.type, sm.price, 
          sm.min_delivery_time, sm.max_delivery_time, sm.is_active,
          sm.metadata, r.code as region_code
        FROM shipping_methods sm
        LEFT JOIN regions r ON sm.region_id = r.id
        WHERE sm.is_active = 1
        ORDER BY sm.price ASC
      `;
      
      const result = await this.db.prepare(query).all();
      const shippingMethods = result.results || [];
      
      // Group by region
      const methodsByRegion: Record<string, any[]> = {};
      const allMethods: any[] = [];
      
      for (const method of shippingMethods) {
        const regionCode = method.region_code || 'default';
        
        if (!methodsByRegion[regionCode]) {
          methodsByRegion[regionCode] = [];
        }
        
        methodsByRegion[regionCode].push(method);
        allMethods.push(method);
      }
      
      // Cache all methods
      await this.env.CACHE.put('shipping_methods:all', JSON.stringify(allMethods), {
        expirationTtl: 3600
      });
      
      // Cache by region
      for (const [regionCode, methods] of Object.entries(methodsByRegion)) {
        await this.env.CACHE.put(`shipping_methods:${regionCode}`, JSON.stringify(methods), {
          expirationTtl: 3600
        });
      }
      
      console.log(`Warmed shipping methods cache with ${allMethods.length} methods across ${Object.keys(methodsByRegion).length} regions`);
    } catch (error) {
      console.error('Failed to warm shipping methods cache:', error);
    }
  }

  /**
   * Warm up homepage data
   */
  async warmHomepageData(): Promise<void> {
    try {
      console.log('Warming homepage data cache...');
      
      // Get featured collections from cache or database
      let featuredCollections = await this.env.CACHE.get('featured_collections', { type: 'json' });
      if (!featuredCollections) {
        await this.warmFeaturedCollections();
        featuredCollections = await this.env.CACHE.get('featured_collections', { type: 'json' });
      }
      
      // Get popular products from cache or database
      let popularProducts = await this.env.CACHE.get('popular_products:10', { type: 'json' });
      if (!popularProducts) {
        await this.warmPopularProducts();
        popularProducts = await this.env.CACHE.get('popular_products:10', { type: 'json' });
      }
      
      // Get latest products
      const latestProductsQuery = `
        SELECT 
          p.id, p.handle, p.thumbnail,
          COALESCE(pt_ro.title, pt_en.title, p.handle) as title,
          COALESCE(pt_ro.subtitle, pt_en.subtitle) as subtitle,
          p.created_at
        FROM products p
        LEFT JOIN product_translations pt_ro ON p.id = pt_ro.product_id AND pt_ro.language_code = 'ro'
        LEFT JOIN product_translations pt_en ON p.id = pt_en.product_id AND pt_en.language_code = 'en'
        WHERE p.deleted_at IS NULL AND p.status = 'published'
        ORDER BY p.created_at DESC
        LIMIT 8
      `;
      
      const latestResult = await this.db.prepare(latestProductsQuery).all();
      const latestProducts = latestResult.results || [];
      
      const homepageData = {
        featured_collections: featuredCollections || [],
        popular_products: popularProducts || [],
        latest_products: latestProducts,
        updated_at: new Date().toISOString()
      };
      
      await this.env.CACHE.put('homepage_data', JSON.stringify(homepageData), {
        expirationTtl: 1800
      });
      
      console.log('Warmed homepage data cache');
    } catch (error) {
      console.error('Failed to warm homepage data cache:', error);
    }
  }

  /**
   * Warm all critical caches
   */
  async warmAllCaches(): Promise<void> {
    console.log('Starting comprehensive cache warming...');
    const startTime = Date.now();
    
    await Promise.all([
      this.warmPaymentMethods(),
      this.warmPopularProducts(),
      this.warmFeaturedCollections(),
      this.warmShippingMethods(),
      this.warmHomepageData()
    ]);
    
    const duration = Date.now() - startTime;
    console.log(`Cache warming completed in ${duration}ms`);
  }

  /**
   * Get cache warming status
   */
  async getCacheWarmingStatus(): Promise<{
    payment_methods: boolean;
    popular_products: boolean;
    featured_collections: boolean;
    shipping_methods: boolean;
    homepage_data: boolean;
  }> {
    try {
      const checks = await Promise.all([
        this.env.CACHE.get('payment_methods:all'),
        this.env.CACHE.get('popular_products:10'),
        this.env.CACHE.get('featured_collections'),
        this.env.CACHE.get('shipping_methods:all'),
        this.env.CACHE.get('homepage_data')
      ]);
      
      return {
        payment_methods: !!checks[0],
        popular_products: !!checks[1],
        featured_collections: !!checks[2],
        shipping_methods: !!checks[3],
        homepage_data: !!checks[4]
      };
    } catch (error) {
      console.error('Failed to get cache warming status:', error);
      return {
        payment_methods: false,
        popular_products: false,
        featured_collections: false,
        shipping_methods: false,
        homepage_data: false
      };
    }
  }
}

import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { Context } from 'hono';
import { decode } from 'hono/jwt';
import <PERSON><PERSON> from 'stripe';
import { EmailService } from '../../services/email';
import { AnalyticsService } from '../../services/analytics';
import { getSessionFromContext, getSessionServiceFromContext } from '../../middleware/session';
import { smartSessionMiddleware } from '../../middleware/performance-session';

type Bindings = {
  DB: D1Database;
  JWT_SECRET: string;
  FRONTEND_URL: string;
  ADMIN_URL: string;
  STRIPE_ENVIRONMENT: string;
  STRIPE_SECRET_KEY_TEST: string;
  STRIPE_SECRET_KEY_LIVE: string;
  STRIPE_WEBHOOK_SECRET: string;
  RESEND_API_KEY: string;
  CACHE: KVNamespace;
  SESSIONS: KVNamespace;
};

const app = new Hono<{ Bindings: Bindings }>();

// CORS middleware
app.use('/*', cors({
  origin: ['http://localhost:3000', 'http://localhost:3001'],
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization', 'X-Session-ID'],
  credentials: true,
}));

// PERFORMANCE: Smart session middleware - automatically optimizes based on route
app.use('*', smartSessionMiddleware());

// Utility functions
function handleError(c: Context, error: any, message = 'Internal server error') {
  console.error(error);
  return c.json({ success: false, error: message, details: error.message }, 500);
}

function safeParseJson(value: any): any {
  if (!value) return null;
  if (typeof value === 'string') {
    try {
      return JSON.parse(value);
    } catch {
      return value;
    }
  }
  return value;
}

// Helper function to extract customer info from JWT
function getCustomerFromToken(c: Context): { customerId?: string; email?: string } {
  try {
    const authHeader = c.req.header('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return {};
    }

    const token = authHeader.substring(7);
    // In a real implementation, you would verify and decode the JWT token
    // For now, we'll extract basic info if available
    
    return {}; // Return empty for guest users
  } catch (error) {
    console.error('Error parsing auth token:', error);
    return {};
  }
}

// Initialize Stripe with the appropriate key based on environment
function getStripeInstance(c: Context): Stripe {
  const isTestMode = c.env.STRIPE_ENVIRONMENT === 'test';
  const secretKey = isTestMode ? c.env.STRIPE_SECRET_KEY_TEST : c.env.STRIPE_SECRET_KEY_LIVE;
  
  return new Stripe(secretKey, {
    apiVersion: '2023-10-16',
  });
}

function getDefaultProviderName(providerId: string): string {
  const defaultNames: Record<string, string> = {
    stripe: 'Credit Card (Stripe)',
    paypal: 'PayPal',
    square: 'Square',
    manual: 'Manual Payment',
    cash_on_delivery: 'Cash on Delivery',
  };
  return defaultNames[providerId.toLowerCase()] || providerId;
}

// Helper function to extract and structure easybox information from metadata
function extractEasyboxInfo(metadata: any): any | null {
  if (!metadata || !metadata.easybox_locker_id) {
    return null;
  }
  
  return {
    locker_id: metadata.easybox_locker_id,
    locker_name: metadata.easybox_locker_name,
    locker_address: metadata.easybox_locker_address,
    locker_city: metadata.easybox_locker_city,
    locker_county: metadata.easybox_locker_county,
    full_address: `${metadata.easybox_locker_name}, ${metadata.easybox_locker_address}, ${metadata.easybox_locker_city}, ${metadata.easybox_locker_county}`
  };
}

// Helper function to check if delivery method is easybox
function isEasyboxDelivery(metadata: any): boolean {
  return !!(metadata && metadata.easybox_locker_id);
}

// Helper function to enrich cart with related data
async function enrichCart(cart: any, db: D1Database): Promise<any> {
  const enriched = { ...cart };

  // Parse metadata
  enriched.metadata = safeParseJson(enriched.metadata) || {};

  // Get line items with product and variant details
  const itemsQuery = `
    SELECT 
      cli.id, cli.cart_id, cli.variant_id, cli.quantity, cli.unit_price, cli.total_price, cli.metadata,
      pv.id as variant_id, pv.title as variant_title, pv.sku, pv.barcode,
      pv.option_values, pv.manage_inventory, pv.allow_backorder, pv.inventory_quantity,
      p.id as product_id, p.handle as product_handle, p.thumbnail as product_thumbnail,
      pt.title as product_title, pt.subtitle as product_subtitle, pt.description as product_description
    FROM cart_items cli
    LEFT JOIN product_variants pv ON cli.variant_id = pv.id
    LEFT JOIN products p ON pv.product_id = p.id
    LEFT JOIN product_translations pt ON p.id = pt.product_id AND pt.language_code = 'ro'
    WHERE cli.cart_id = ?
    ORDER BY cli.created_at ASC
  `;

  const items = await db.prepare(itemsQuery).bind(cart.id).all();

  enriched.items = (items.results || []).map((item: any) => {
    const enrichedItem = { ...item };
    
    // Parse metadata and option values
    enrichedItem.metadata = safeParseJson(enrichedItem.metadata) || {};
    enrichedItem.option_values = safeParseJson(enrichedItem.option_values) || [];
    
    // Structure variant data for frontend compatibility
    enrichedItem.variant = {
      id: item.variant_id,
      title: item.variant_title,
      sku: item.sku,
      barcode: item.barcode,
      option_values: enrichedItem.option_values,
      manage_inventory: item.manage_inventory,
      allow_backorder: item.allow_backorder,
      inventory_quantity: item.inventory_quantity,
      product: {
        id: item.product_id,
        handle: item.product_handle,
        title: item.product_title,
        subtitle: item.product_subtitle,
        description: item.product_description,
        thumbnail: item.product_thumbnail
      }
    };

    return enrichedItem;
  });

  // Get billing address if exists
  if (cart.billing_address) {
    enriched.billing_address = safeParseJson(cart.billing_address);
  }

  // Get shipping address if exists
  if (cart.shipping_address) {
    enriched.shipping_address = safeParseJson(cart.shipping_address);
  }

  // Get region details
  if (cart.region_id) {
    const regionQuery = `SELECT * FROM regions WHERE id = ?`;
    const region = await db.prepare(regionQuery).bind(cart.region_id).first();
    if (region) {
      enriched.region = region;
    }
  }

  // Calculate totals
  enriched.subtotal = enriched.items.reduce((sum: number, item: any) => {
    return sum + (item.unit_price * item.quantity);
  }, 0);

  // Calculate shipping total from metadata if shipping method is selected
  const metadata = safeParseJson(enriched.metadata) || {};
  enriched.shipping_total = metadata.shipping_method?.price || 0;
  enriched.tax_total = 0; // TODO: Calculate tax

  // Calculate discount total from metadata
  enriched.discount_total = metadata.discount_total || 0;
  enriched.applied_discounts = metadata.applied_discounts || [];

  enriched.total = enriched.subtotal + enriched.shipping_total + enriched.tax_total - enriched.discount_total;

  // Get payment sessions
  const paymentSessionsQuery = `
    SELECT * FROM payment_sessions WHERE cart_id = ? ORDER BY created_at ASC
  `;
  const paymentSessions = await db.prepare(paymentSessionsQuery).bind(cart.id).all();
  
  enriched.payment_sessions = (paymentSessions.results || []).map((session: any) => {
    // Handle is_selected more robustly - SQLite can return 0/1 as numbers or strings
    const isSelected = session.is_selected === 1 || session.is_selected === '1' || session.is_selected === true;
    const isInitiated = session.is_initiated === 1 || session.is_initiated === '1' || session.is_initiated === true;

    return {
      ...session,
      data: safeParseJson(session.data) || {},
      is_selected: isSelected,
      is_initiated: isInitiated
    };
  });

  // Add easybox information if present
  enriched.easybox_delivery = extractEasyboxInfo(metadata);
  enriched.is_easybox_delivery = isEasyboxDelivery(metadata);

  return enriched;
}

// POST /store/carts - Create a new cart
app.post('/', async (c) => {
  try {
    const body = await c.req.json().catch(() => ({}));
    const { region_id, customer_id, email, currency_code = 'RON', metadata } = body;

    // Get session for cart association
    const session = getSessionFromContext(c);
    const sessionService = getSessionServiceFromContext(c);

    // Normalize currency code to uppercase
    const normalizedCurrencyCode = currency_code.toUpperCase();

    // Verify currency exists
    const currencyExists = await c.env.DB.prepare(`
      SELECT code FROM currencies WHERE UPPER(code) = ? AND is_active = 1
    `).bind(normalizedCurrencyCode).first();

    if (!currencyExists) {
      return c.json({
        success: false,
        error: `Currency ${normalizedCurrencyCode} is not supported`,
      }, 400);
    }

    // Check if session already has a cart and it exists in DB
    let existingCartId = session?.cart_id;
    if (existingCartId) {
      const existingCart = await c.env.DB.prepare(`SELECT * FROM carts WHERE id = ?`).bind(existingCartId).first();
      if (existingCart) {
        // Return existing cart instead of creating new one
        const enrichedCart = await enrichCart(existingCart, c.env.DB);
        return c.json({
          success: true,
          data: enrichedCart,
          message: 'Returned existing cart from session'
        });
      }
    }

    // Get default region if none provided, prioritizing Romanian region for RON currency
    let effectiveRegionId = region_id;
    if (!effectiveRegionId) {
      let regionsQuery;
      if (normalizedCurrencyCode === 'RON') {
        regionsQuery = `SELECT id FROM regions WHERE UPPER(currency_code) = 'RON' AND is_active = 1 ORDER BY created_at ASC LIMIT 1`;
      } else {
        regionsQuery = `SELECT id FROM regions WHERE is_active = 1 ORDER BY created_at ASC LIMIT 1`;
      }
      const defaultRegion = await c.env.DB.prepare(regionsQuery).first();
      if (defaultRegion) {
        effectiveRegionId = defaultRegion.id;
      }
    }

    if (!effectiveRegionId) {
      return c.json({
        success: false,
        error: 'No region available',
      }, 400);
    }

    // Verify region exists
    const regionExists = await c.env.DB.prepare(`
      SELECT id FROM regions WHERE id = ? AND is_active = 1
    `).bind(effectiveRegionId).first();

    if (!regionExists) {
      return c.json({
        success: false,
        error: 'Invalid region specified',
      }, 400);
    }

    // Create cart
    const cartId = crypto.randomUUID();
    const now = new Date().toISOString();
    
    const createQuery = `
      INSERT INTO carts (
        id, region_id, customer_id, email, currency_code, metadata, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `;

    await c.env.DB.prepare(createQuery).bind(
      cartId,
      effectiveRegionId,
      customer_id || null,
      email || null,
      normalizedCurrencyCode,
      metadata ? JSON.stringify(metadata) : null,
      now,
      now
    ).run();

    // PERFORMANCE: Simplified cart tracking - only store cart ID in session, not full cart data
    if (session && sessionService) {
      await sessionService.updateSession(session.id, {
        cart_id: cartId
      });
    }

    // Get created cart
    const cart = await c.env.DB.prepare(`SELECT * FROM carts WHERE id = ?`).bind(cartId).first();
    
    // Enrich cart with related data
    const enrichedCart = await enrichCart(cart, c.env.DB);

    return c.json({
      success: true,
      data: enrichedCart,
    });

  } catch (error) {
    return handleError(c, error, 'Failed to create cart');
  }
});

// GET /store/carts/:id - Get a specific cart
app.get('/:id', async (c) => {
  try {
    const cartId = c.req.param('id');

    // Get cart from database
    const cart = await c.env.DB.prepare(`SELECT * FROM carts WHERE id = ?`).bind(cartId).first();
    
    if (!cart) {
      return c.json({
        success: false,
        error: 'Cart not found',
      }, 404);
    }

    const enrichedCart = await enrichCart(cart, c.env.DB);

    return c.json({
      success: true,
      data: enrichedCart,
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch cart');
  }
});

// POST /store/carts/:id/line-items - Add item to cart
app.post('/:id/line-items', async (c) => {
  try {
    const cartId = c.req.param('id');
    const body = await c.req.json();
    const { variant_id, quantity = 1, metadata } = body;

    if (!variant_id) {
      return c.json({ success: false, error: 'Variant ID is required' }, 400);
    }

    // Verify cart exists
    const cart = await c.env.DB.prepare(`SELECT * FROM carts WHERE id = ?`).bind(cartId).first();
    if (!cart) {
      return c.json({ success: false, error: 'Cart not found' }, 404);
    }

    // Verify variant exists and get product details
    const variantQuery = `
      SELECT
        pv.id, pv.product_id, pv.title, pv.sku, pv.manage_inventory, pv.inventory_quantity,
        pt.title as product_title, p.thumbnail, p.status,
        vp.price, vp.currency_code
      FROM product_variants pv
      LEFT JOIN products p ON pv.product_id = p.id
      LEFT JOIN product_translations pt ON p.id = pt.product_id AND pt.language_code = 'ro'
      LEFT JOIN variant_prices vp ON pv.id = vp.variant_id AND vp.currency_code = ?
      WHERE pv.id = ? AND p.status = 'published'
    `;

    const variant = await c.env.DB.prepare(variantQuery).bind(cart.currency_code || 'RON', variant_id).first();

    if (!variant) {
      return c.json({ success: false, error: 'Product variant not found or not available' }, 404);
    }

    // Check inventory if managed
    if (variant.manage_inventory && (variant.inventory_quantity as number) < quantity) {
      return c.json({ success: false, error: 'Insufficient inventory' }, 400);
    }

    const unitPrice = (variant.price as number) || 0;
    const totalPrice = unitPrice * quantity;

    // Check if item already exists in cart
    const existingItemQuery = `
      SELECT * FROM cart_items
      WHERE cart_id = ? AND variant_id = ?
    `;
    const existingItem = await c.env.DB.prepare(existingItemQuery).bind(cartId, variant_id).first();

    const now = new Date().toISOString();

    if (existingItem) {
      // Update existing item
      const newQuantity = (existingItem.quantity as number) + quantity;
      const newTotalPrice = unitPrice * newQuantity;

      // Check inventory again for new total
      if (variant.manage_inventory && (variant.inventory_quantity as number) < newQuantity) {
        return c.json({ success: false, error: 'Insufficient inventory' }, 400);
      }

      const updateQuery = `
        UPDATE cart_items
        SET quantity = ?, total_price = ?, metadata = ?, updated_at = ?
        WHERE id = ?
      `;

      await c.env.DB.prepare(updateQuery).bind(
        newQuantity,
        newTotalPrice,
        metadata ? JSON.stringify(metadata) : existingItem.metadata,
        now,
        existingItem.id
      ).run();
    } else {
      // Create new line item
      const lineItemId = crypto.randomUUID();

      const insertQuery = `
        INSERT INTO cart_items (
          id, cart_id, variant_id, quantity, unit_price, total_price,
          metadata, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      await c.env.DB.prepare(insertQuery).bind(
        lineItemId,
        cartId,
        variant_id,
        quantity,
        unitPrice,
        totalPrice,
        metadata ? JSON.stringify(metadata) : null,
        now,
        now
      ).run();
    }

    // Update cart timestamp
    await c.env.DB.prepare(`UPDATE carts SET updated_at = ? WHERE id = ?`).bind(now, cartId).run();

    // Get updated cart
    const updatedCart = await c.env.DB.prepare(`SELECT * FROM carts WHERE id = ?`).bind(cartId).first();
    const enrichedCart = await enrichCart(updatedCart, c.env.DB);

    return c.json({
      success: true,
      data: enrichedCart,
    });

  } catch (error) {
    return handleError(c, error, 'Failed to add item to cart');
  }
});

// POST /store/carts/:id/line-items/:line_id - Update line item
app.post('/:id/line-items/:line_id', async (c) => {
  try {
    const cartId = c.req.param('id');
    const lineId = c.req.param('line_id');
    const body = await c.req.json();
    const { quantity, metadata } = body;

    if (quantity === undefined || quantity < 0) {
      return c.json({ success: false, error: 'Valid quantity is required' }, 400);
    }

    // Verify cart exists
    const cart = await c.env.DB.prepare(`SELECT * FROM carts WHERE id = ?`).bind(cartId).first();
    if (!cart) {
      return c.json({ success: false, error: 'Cart not found' }, 404);
    }

    // Verify line item exists and belongs to cart
    const lineItem = await c.env.DB.prepare(`
      SELECT * FROM cart_items
      WHERE id = ? AND cart_id = ?
    `).bind(lineId, cartId).first();

    if (!lineItem) {
      return c.json({ success: false, error: 'Line item not found' }, 404);
    }

    const now = new Date().toISOString();

    if (quantity === 0) {
      // Delete the item
      await c.env.DB.prepare(`
        DELETE FROM cart_items WHERE id = ?
      `).bind(lineId).run();
    } else {
      // Update the item
      const newTotalPrice = (lineItem.unit_price as number) * quantity;

      const updateQuery = `
        UPDATE cart_items
        SET quantity = ?, total_price = ?, metadata = ?, updated_at = ?
        WHERE id = ?
      `;

      await c.env.DB.prepare(updateQuery).bind(
        quantity,
        newTotalPrice,
        metadata ? JSON.stringify(metadata) : lineItem.metadata,
        now,
        lineId
      ).run();
    }

    // Update cart timestamp
    await c.env.DB.prepare(`UPDATE carts SET updated_at = ? WHERE id = ?`).bind(now, cartId).run();

    // Get updated cart
    const updatedCart = await c.env.DB.prepare(`SELECT * FROM carts WHERE id = ?`).bind(cartId).first();
    const enrichedCart = await enrichCart(updatedCart, c.env.DB);

    return c.json({
      success: true,
      data: enrichedCart,
    });

  } catch (error) {
    return handleError(c, error, 'Failed to update line item');
  }
});

// DELETE /store/carts/:id/line-items/:line_id - Remove line item
app.delete('/:id/line-items/:line_id', async (c) => {
  try {
    const cartId = c.req.param('id');
    const lineId = c.req.param('line_id');

    // Verify cart exists
    const cart = await c.env.DB.prepare(`SELECT * FROM carts WHERE id = ?`).bind(cartId).first();
    if (!cart) {
      return c.json({ success: false, error: 'Cart not found' }, 404);
    }

    // Verify line item exists and belongs to cart
    const lineItem = await c.env.DB.prepare(`
      SELECT * FROM cart_items
      WHERE id = ? AND cart_id = ?
    `).bind(lineId, cartId).first();

    if (!lineItem) {
      return c.json({ success: false, error: 'Line item not found' }, 404);
    }

    const now = new Date().toISOString();

    // Delete the item
    await c.env.DB.prepare(`
      DELETE FROM cart_items WHERE id = ?
    `).bind(lineId).run();

    // Update cart timestamp
    await c.env.DB.prepare(`UPDATE carts SET updated_at = ? WHERE id = ?`).bind(now, cartId).run();

    // Get updated cart
    const updatedCart = await c.env.DB.prepare(`SELECT * FROM carts WHERE id = ?`).bind(cartId).first();
    const enrichedCart = await enrichCart(updatedCart, c.env.DB);

    return c.json({
      success: true,
      data: enrichedCart,
    });

  } catch (error) {
    return handleError(c, error, 'Failed to remove line item');
  }
});

// POST /store/carts/:id - Update cart
app.post('/:id', async (c) => {
  try {
    const cartId = c.req.param('id');
    const body = await c.req.json();
    const { email, billing_address, shipping_address, metadata, customer_id } = body;

    // Extract customer info from JWT token
    const tokenCustomer = getCustomerFromToken(c);

    // Verify cart exists
    const cart = await c.env.DB.prepare(`SELECT * FROM carts WHERE id = ?`).bind(cartId).first();
    if (!cart) {
      return c.json({ success: false, error: 'Cart not found' }, 404);
    }

    const now = new Date().toISOString();
    const updateData: any = { updated_at: now };

    // Determine customer_id in priority order:
    // 1. customer_id from JWT token (authenticated user)
    // 2. customer_id from request body 
    // 3. existing cart customer_id
    // 4. find/create customer from email (JWT email or request email)
    let customerId = tokenCustomer.customerId || customer_id || cart.customer_id;
    let customerEmail = email || tokenCustomer.email;
    
    // If still no customer_id but we have an email, create or find customer
    if (!customerId && customerEmail) {
      // Check if customer already exists
      const existingCustomer = await c.env.DB.prepare(`
        SELECT id FROM customers WHERE email = ?
      `).bind(customerEmail).first();

      if (existingCustomer) {
        customerId = existingCustomer.id;
      } else {
        // Create new guest customer
        customerId = crypto.randomUUID();
        const createCustomerQuery = `
          INSERT INTO customers (
            id, email, accepts_marketing, created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?)
        `;
        await c.env.DB.prepare(createCustomerQuery).bind(
          customerId,
          customerEmail,
          false, // Guest customers default to no marketing
          now,
          now
        ).run();
      }
    }
    
    // Update cart with customer_id if we have one and it's different
    if (customerId && customerId !== cart.customer_id) {
      updateData.customer_id = customerId;
    }

    // Update email (prefer email from request, fallback to JWT email)
    if (customerEmail) {
      updateData.email = customerEmail;
    }

    // Update metadata
    if (metadata) {
      updateData.metadata = JSON.stringify(metadata);
    }

    // Handle billing address
    if (billing_address) {
      if (!customerId) {
        return c.json({ 
          success: false, 
          error: 'Customer authentication required to save addresses. Please provide email or login.' 
        }, 400);
      }

      // Store address as JSON in cart (as per schema design)
      updateData.billing_address = JSON.stringify(billing_address);

      // DISABLED: Do not automatically update customer address book from cart
      // This was causing billing addresses to be overwritten with shipping data
      // Let users manually save addresses to their address book instead
      /*
      // Also create/update customer address record for logged-in users (for their address book)
      if (customerId) {
        try {
          // Check if customer already has a billing address we can update
          const existingAddress = await c.env.DB.prepare(`
            SELECT id FROM customer_addresses 
            WHERE customer_id = ? AND type IN ('billing', 'both')
            ORDER BY updated_at DESC LIMIT 1
          `).bind(customerId).first();

          if (existingAddress) {
            // Update existing address
            const updateAddressQuery = `
              UPDATE customer_addresses
              SET first_name = ?, last_name = ?, address_line_1 = ?, address_line_2 = ?,
                  city = ?, country_code = ?, state = ?, postal_code = ?,
                  phone = ?, company = ?, 
                  type = CASE 
                           WHEN type = 'shipping' THEN 'both' 
                           ELSE 'billing' 
                         END, 
                  updated_at = ?
              WHERE id = ?
            `;
            await c.env.DB.prepare(updateAddressQuery).bind(
              billing_address.first_name, billing_address.last_name,
              billing_address.address_1, billing_address.address_2,
              billing_address.city, billing_address.country_code,
              billing_address.province, billing_address.postal_code,
              billing_address.phone, billing_address.company,
              now, existingAddress.id
            ).run();
          } else {
            // Create new address for customer's address book
            const addressId = crypto.randomUUID();
            const createAddressQuery = `
              INSERT INTO customer_addresses (
                id, customer_id, type, first_name, last_name, address_line_1, address_line_2, city,
                country_code, state, postal_code, phone, company, created_at, updated_at
              ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `;
            await c.env.DB.prepare(createAddressQuery).bind(
              addressId, customerId, 'billing', billing_address.first_name, billing_address.last_name,
              billing_address.address_1, billing_address.address_2, billing_address.city,
              billing_address.country_code, billing_address.province, billing_address.postal_code,
              billing_address.phone, billing_address.company, now, now
            ).run();
          }
        } catch (addressError) {
          console.log('Failed to save customer address:', addressError);
          // Continue with cart update even if address save fails
        }
      }
      */
    }

    // Handle shipping address
    if (shipping_address) {
      if (!customerId) {
        return c.json({ 
          success: false, 
          error: 'Customer authentication required to save addresses. Please provide email or login.' 
        }, 400);
      }

      // Store address as JSON in cart (as per schema design)
      updateData.shipping_address = JSON.stringify(shipping_address);

      // DISABLED: Do not automatically update customer address book from cart
      // This was causing shipping addresses to be overwritten with billing data
      // Let users manually save addresses to their address book instead
      /*
      // Also create/update customer address record for logged-in users (for their address book)
      if (customerId) {
        try {
          // Check if customer already has a shipping address we can update
          const existingAddress = await c.env.DB.prepare(`
            SELECT id FROM customer_addresses 
            WHERE customer_id = ? AND type IN ('shipping', 'both')
            ORDER BY updated_at DESC LIMIT 1
          `).bind(customerId).first();

          if (existingAddress) {
            // Update existing address
            const updateAddressQuery = `
              UPDATE customer_addresses
              SET first_name = ?, last_name = ?, address_line_1 = ?, address_line_2 = ?,
                  city = ?, country_code = ?, state = ?, postal_code = ?,
                  phone = ?, company = ?, 
                  type = CASE 
                           WHEN type = 'billing' THEN 'both' 
                           ELSE 'shipping' 
                         END, 
                  updated_at = ?
              WHERE id = ?
            `;
            await c.env.DB.prepare(updateAddressQuery).bind(
              shipping_address.first_name, shipping_address.last_name,
              shipping_address.address_1, shipping_address.address_2,
              shipping_address.city, shipping_address.country_code,
              shipping_address.province, shipping_address.postal_code,
              shipping_address.phone, shipping_address.company,
              now, existingAddress.id
            ).run();
          } else {
            // Create new address for customer's address book
            const addressId = crypto.randomUUID();
            const createAddressQuery = `
              INSERT INTO customer_addresses (
                id, customer_id, type, first_name, last_name, address_line_1, address_line_2, city,
                country_code, state, postal_code, phone, company, created_at, updated_at
              ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `;
            await c.env.DB.prepare(createAddressQuery).bind(
              addressId, customerId, 'shipping', shipping_address.first_name, shipping_address.last_name,
              shipping_address.address_1, shipping_address.address_2, shipping_address.city,
              shipping_address.country_code, shipping_address.province, shipping_address.postal_code,
              shipping_address.phone, shipping_address.company, now, now
            ).run();
          }
        } catch (addressError) {
          console.log('Failed to save customer address:', addressError);
          // Continue with cart update even if address save fails
        }
      }
      */
    }

    // Update cart
    const updateFields = Object.keys(updateData).map(key => `${key} = ?`).join(', ');
    const updateValues = Object.values(updateData);

    const updateQuery = `UPDATE carts SET ${updateFields} WHERE id = ?`;
    await c.env.DB.prepare(updateQuery).bind(...updateValues, cartId).run();

    // Get updated cart
    const updatedCart = await c.env.DB.prepare(`SELECT * FROM carts WHERE id = ?`).bind(cartId).first();
    const enrichedCart = await enrichCart(updatedCart, c.env.DB);

    return c.json({
      success: true,
      data: enrichedCart,
    });

  } catch (error) {
    return handleError(c, error, 'Failed to update cart');
  }
});

// GET /store/carts/:id/shipping-options - Get shipping options for cart
app.get('/:id/shipping-options', async (c) => {
  try {
    const cartId = c.req.param('id');

    // Verify cart exists
    const cart = await c.env.DB.prepare(`SELECT * FROM carts WHERE id = ?`).bind(cartId).first();
    if (!cart) {
      return c.json({ success: false, error: 'Cart not found' }, 404);
    }

    // Get cart items with product details
    const cartItemsQuery = `
      SELECT 
        ci.quantity, ci.unit_price,
        pv.id as variant_id, pv.weight as variant_weight,
        p.id as product_id, p.type_id as product_type_id, p.weight as product_weight,
        pc.category_id
      FROM cart_items ci
      LEFT JOIN product_variants pv ON ci.variant_id = pv.id
      LEFT JOIN products p ON pv.product_id = p.id
      LEFT JOIN product_categories pc ON p.id = pc.product_id
      WHERE ci.cart_id = ?
    `;

    const cartItems = await c.env.DB.prepare(cartItemsQuery).bind(cartId).all();
    
    if (!cartItems.results || cartItems.results.length === 0) {
      return c.json({ success: true, data: [] });
    }

    // Calculate cart totals
    const cartSubtotal = cartItems.results.reduce((sum: number, item: any) => {
      return sum + (item.unit_price * item.quantity);
    }, 0);

    const cartWeight = cartItems.results.reduce((sum: number, item: any) => {
      const itemWeight = item.variant_weight || item.product_weight || 0;
      return sum + (itemWeight * item.quantity);
    }, 0);

    // Get unique product IDs, category IDs, and product type IDs from cart
    const productIds = [...new Set(cartItems.results.map((item: any) => item.product_id))];
    const categoryIds = [...new Set(cartItems.results.map((item: any) => item.category_id).filter(Boolean))];
    const productTypeIds = [...new Set(cartItems.results.map((item: any) => item.product_type_id).filter(Boolean))];
    
    // Also get ALL product type IDs (including null) for proper filtering
    const allProductTypeIds = cartItems.results.map((item: any) => item.product_type_id);

    // Get shipping address from cart to determine country
    let shippingCountry = null;
    if (cart.shipping_address) {
      const shippingAddress = safeParseJson(cart.shipping_address);
      shippingCountry = shippingAddress?.country_code?.toUpperCase();
    }

    // Get shipping methods for available zones
    const shippingMethodsQuery = `
      SELECT
        sm.id, sm.name, sm.description, sm.provider, sm.price_type, sm.price,
        sm.currency_code, sm.min_order_amount, sm.max_order_amount, 
        sm.estimated_delivery_days, sm.applies_to, sm.applies_to_ids, sm.excludes_ids,
        sm.min_weight, sm.max_weight, sm.metadata, sm.created_at, sm.updated_at,
        sz.name as zone_name, sz.countries as zone_countries
      FROM shipping_methods sm
      LEFT JOIN shipping_zones sz ON sm.zone_id = sz.id
      WHERE sm.is_active = 1 
        AND sz.is_active = 1
        AND (sm.currency_code = ? OR sm.currency_code IS NULL)
        AND (sm.min_order_amount IS NULL OR sm.min_order_amount <= ?)
        AND (sm.max_order_amount IS NULL OR sm.max_order_amount > ?)
        AND (sm.min_weight IS NULL OR sm.min_weight <= ?)
        AND (sm.max_weight IS NULL OR sm.max_weight >= ?)
      ORDER BY sm.price ASC, sm.created_at ASC
    `;

    const shippingMethods = await c.env.DB.prepare(shippingMethodsQuery)
      .bind(cart.currency_code || 'RON', cartSubtotal, cartSubtotal, cartWeight, cartWeight)
      .all();

    const processedMethods = (shippingMethods.results || []).filter((method: any) => {
      // Parse zone countries
      const zoneCountries = safeParseJson(method.zone_countries) || [];
      
      // Check country eligibility
      if (shippingCountry) {
        if (zoneCountries.length > 0 && !zoneCountries.some((country: string) => 
          country.toUpperCase() === shippingCountry
        )) {
          return false;
        }
      }

      // Check product restrictions
      const appliesTo = method.applies_to || 'all';
      const appliesToIds = safeParseJson(method.applies_to_ids) || [];
      const excludesIds = safeParseJson(method.excludes_ids) || [];

      // Check exclusions first
      if (excludesIds.length > 0) {
        const hasExcludedProduct = productIds.some(id => excludesIds.includes(id));
        const hasExcludedCategory = categoryIds.some(id => excludesIds.includes(id));
        const hasExcludedType = productTypeIds.some(id => excludesIds.includes(id));
        
        if (hasExcludedProduct || hasExcludedCategory || hasExcludedType) {
          return false;
        }
      }

      // Check positive restrictions
      switch (appliesTo) {
        case 'all':
          return true;
          
        case 'products':
          if (appliesToIds.length === 0) return true;
          // ALL products in cart must be in the allowed list
          return productIds.every(id => appliesToIds.includes(id));
          
        case 'categories':
          if (appliesToIds.length === 0) return true;
          // ALL products in cart must have categories in the allowed list
          return productIds.every(productId => {
            const productCategories = cartItems.results
              .filter((item: any) => item.product_id === productId)
              .map((item: any) => item.category_id)
              .filter(Boolean);
            
            return productCategories.some(catId => appliesToIds.includes(catId));
          });
          
        case 'product_types':
          if (appliesToIds.length === 0) return true;
          // ALL products in cart must have types in the allowed list
          // This includes products without a type (null) - they should be excluded if method is type-specific
          return allProductTypeIds.every(typeId => {
            // If the product has no type (null), and this method is restricted to specific types, exclude it
            if (typeId === null || typeId === undefined) {
              return false;
            }
            // Check if this product's type is in the allowed list
            return appliesToIds.includes(typeId);
          });
          
        default:
          return true;
      }
    }).map((method: any) => {
      const processedMethod = { ...method };
      processedMethod.metadata = safeParseJson(processedMethod.metadata) || {};
      processedMethod.price = method.price || 0;
      
      // Remove internal data from response
      delete processedMethod.zone_countries;
      delete processedMethod.applies_to_ids;
      delete processedMethod.excludes_ids;
      
      return processedMethod;
    });

    return c.json({
      success: true,
      data: processedMethods,
      debug: {
        cart_subtotal: cartSubtotal,
        cart_weight: cartWeight,
        product_ids: productIds,
        category_ids: categoryIds,
        product_type_ids: productTypeIds,
        all_product_type_ids: allProductTypeIds,
        shipping_country: shippingCountry
      }
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch shipping options');
  }
});

// POST /store/carts/:id/shipping-methods - Add shipping method to cart
app.post('/:id/shipping-methods', async (c) => {
  try {
    const cartId = c.req.param('id');
    const body = await c.req.json();
    const { option_id, data } = body;

    if (!option_id) {
      return c.json({ success: false, error: 'Shipping method ID is required' }, 400);
    }

    // Verify cart exists
    const cart = await c.env.DB.prepare(`SELECT * FROM carts WHERE id = ?`).bind(cartId).first();
    if (!cart) {
      return c.json({ success: false, error: 'Cart not found' }, 404);
    }

    // Verify shipping method exists
    const shippingMethod = await c.env.DB.prepare(`
      SELECT sm.*, sz.name as zone_name
      FROM shipping_methods sm
      LEFT JOIN shipping_zones sz ON sm.zone_id = sz.id
      WHERE sm.id = ? AND sm.is_active = 1 AND sz.is_active = 1
    `).bind(option_id).first();

    if (!shippingMethod) {
      return c.json({ success: false, error: 'Shipping method not found' }, 404);
    }

    const now = new Date().toISOString();

    // Store shipping method selection in cart metadata
    const currentMetadata = safeParseJson(cart.metadata) || {};
    currentMetadata.shipping_method = {
      id: option_id,
      name: shippingMethod.name,
      price: shippingMethod.price || 0,
      currency_code: shippingMethod.currency_code,
      estimated_delivery_days: shippingMethod.estimated_delivery_days,
      provider: shippingMethod.provider,
      data: data || null,
      selected_at: now
    };
    
    // Also store shipping_total for backward compatibility
    currentMetadata.shipping_total = shippingMethod.price || 0;

    // Update cart with shipping method info
    await c.env.DB.prepare(`
      UPDATE carts 
      SET metadata = ?, updated_at = ? 
      WHERE id = ?
    `).bind(JSON.stringify(currentMetadata), now, cartId).run();

    // Get updated cart
    const updatedCart = await c.env.DB.prepare(`SELECT * FROM carts WHERE id = ?`).bind(cartId).first();
    const enrichedCart = await enrichCart(updatedCart, c.env.DB);

    return c.json({
      success: true,
      data: enrichedCart,
    });

  } catch (error) {
    return handleError(c, error, 'Failed to add shipping method');
  }
});

// POST /store/carts/:id/promotions - Apply promo codes to cart
app.post('/:id/promotions', async (c) => {
  try {
    const cartId = c.req.param('id');
    const body = await c.req.json();
    const { promo_codes } = body;

    if (!promo_codes || !Array.isArray(promo_codes) || promo_codes.length === 0) {
      return c.json({ success: false, error: 'At least one promo code is required' }, 400);
    }

    // Verify cart exists
    const cart = await c.env.DB.prepare(`SELECT * FROM carts WHERE id = ?`).bind(cartId).first();
    if (!cart) {
      return c.json({ success: false, error: 'Cart not found' }, 404);
    }

    // Get cart items for discount calculations
    const cartItemsQuery = `
      SELECT 
        ci.quantity, ci.unit_price, ci.total_price,
        pv.id as variant_id, p.id as product_id, p.collection_id
      FROM cart_items ci
      LEFT JOIN product_variants pv ON ci.variant_id = pv.id
      LEFT JOIN products p ON pv.product_id = p.id
      WHERE ci.cart_id = ?
    `;

    const cartItems = await c.env.DB.prepare(cartItemsQuery).bind(cartId).all();
    
    if (!cartItems.results || cartItems.results.length === 0) {
      return c.json({ success: false, error: 'Cart is empty' }, 400);
    }

    // Calculate cart subtotal
    const cartSubtotal = cartItems.results.reduce((sum: number, item: any) => {
      return sum + item.total_price;
    }, 0);

    const productIds = [...new Set(cartItems.results.map((item: any) => item.product_id))];
    const collectionIds = [...new Set(cartItems.results.map((item: any) => item.collection_id).filter(Boolean))];

    const now = new Date().toISOString();
    const appliedDiscounts: any[] = [];
    const invalidCodes: string[] = [];
    let totalDiscountAmount = 0;

    // Process each promo code
    for (const code of promo_codes) {
      if (!code || typeof code !== 'string') {
        invalidCodes.push(code);
        continue;
      }

      // Find the discount code
      const discountQuery = `
        SELECT *
        FROM discount_codes
        WHERE UPPER(code) = UPPER(?) 
          AND is_active = 1
          AND (starts_at IS NULL OR starts_at <= ?)
          AND (ends_at IS NULL OR ends_at > ?)
      `;

      const discount = await c.env.DB.prepare(discountQuery).bind(code.trim(), now, now).first();

      if (!discount) {
        invalidCodes.push(code);
        continue;
      }

      // Check usage limits
      if (discount.usage_limit && (discount.usage_count || 0) >= discount.usage_limit) {
        invalidCodes.push(code);
        continue;
      }

      // Check minimum order amount
      if (discount.min_order_amount && cartSubtotal < (discount.min_order_amount as number)) {
        invalidCodes.push(code);
        continue;
      }

      // Check customer usage limit (for logged-in customers)
      if (cart.customer_id && discount.customer_usage_limit) {
        const customerUsageQuery = `
          SELECT COUNT(*) as usage_count
          FROM discount_usage
          WHERE discount_id = ? AND customer_id = ?
        `;
        const customerUsage = await c.env.DB.prepare(customerUsageQuery).bind(discount.id, cart.customer_id).first();
        
        if (customerUsage && ((customerUsage.usage_count as number) || 0) >= (discount.customer_usage_limit as number)) {
          invalidCodes.push(code);
          continue;
        }
      }

      // Check if discount applies to cart contents
      const appliesTo = discount.applies_to || 'all';
      const appliesToIds = safeParseJson(discount.applies_to_ids) || [];

      let discountApplies = true;
      switch (appliesTo) {
        case 'all':
          discountApplies = true;
          break;
        case 'specific_products':
          if (appliesToIds.length > 0) {
            discountApplies = productIds.some(id => appliesToIds.includes(id));
          }
          break;
        case 'specific_collections':
          if (appliesToIds.length > 0) {
            discountApplies = collectionIds.some(id => appliesToIds.includes(id));
          }
          break;
        default:
          discountApplies = true;
      }

      if (!discountApplies) {
        invalidCodes.push(code);
        continue;
      }

      // Calculate discount amount
      let discountAmount = 0;
      const discountValue = (discount.value as number) || 0;
      const maxDiscountAmount = (discount.max_discount_amount as number) || null;
      
      switch (discount.type) {
        case 'percentage':
          discountAmount = (cartSubtotal * discountValue) / 100;
          if (maxDiscountAmount && discountAmount > maxDiscountAmount) {
            discountAmount = maxDiscountAmount;
          }
          break;
        case 'fixed_amount':
          discountAmount = Math.min(discountValue, cartSubtotal);
          break;
        case 'free_shipping':
          // For free shipping, we'll mark it but calculate actual savings later when shipping is selected
          discountAmount = 0;
          break;
        default:
          discountAmount = 0;
      }

      // Add to applied discounts
      appliedDiscounts.push({
        id: discount.id,
        code: code.trim(),
        type: discount.type,
        value: discountValue,
        amount: discountAmount,
        description: `${discount.type === 'percentage' ? discountValue + '%' : 'Fixed'} discount`
      });

      totalDiscountAmount += discountAmount;
    }

    // Update cart metadata with applied discounts
    const currentMetadata = safeParseJson(cart.metadata) || {};
    currentMetadata.applied_discounts = appliedDiscounts;
    currentMetadata.discount_total = totalDiscountAmount;

    // Update cart
    await c.env.DB.prepare(`
      UPDATE carts 
      SET metadata = ?, updated_at = ? 
      WHERE id = ?
    `).bind(JSON.stringify(currentMetadata), now, cartId).run();

    // Get updated cart
    const updatedCart = await c.env.DB.prepare(`SELECT * FROM carts WHERE id = ?`).bind(cartId).first();
    const enrichedCart = await enrichCart(updatedCart, c.env.DB);

    // Prepare response
    const response: any = {
      success: true,
      data: enrichedCart,
      applied_discounts: appliedDiscounts,
      total_discount: totalDiscountAmount
    };

    if (invalidCodes.length > 0) {
      response.invalid_codes = invalidCodes;
      response.message = `Some promo codes were invalid: ${invalidCodes.join(', ')}`;
    }

    return c.json(response);

  } catch (error) {
    return handleError(c, error, 'Failed to apply promo codes');
  }
});

// DELETE /store/carts/:id/promotions - Remove promo codes from cart
app.delete('/:id/promotions', async (c) => {
  try {
    const cartId = c.req.param('id');
    const { promo_codes } = await c.req.json().catch(() => ({}));

    // Verify cart exists
    const cart = await c.env.DB.prepare(`SELECT * FROM carts WHERE id = ?`).bind(cartId).first();
    if (!cart) {
      return c.json({ success: false, error: 'Cart not found' }, 404);
    }

    const now = new Date().toISOString();
    const currentMetadata = safeParseJson(cart.metadata) || {};
    
    if (promo_codes && Array.isArray(promo_codes) && promo_codes.length > 0) {
      // Remove specific promo codes
      const existingDiscounts = currentMetadata.applied_discounts || [];
      const updatedDiscounts = existingDiscounts.filter((discount: any) => 
        !promo_codes.some((code: string) => code.toUpperCase() === discount.code.toUpperCase())
      );
      
      currentMetadata.applied_discounts = updatedDiscounts;
      currentMetadata.discount_total = updatedDiscounts.reduce((sum: number, discount: any) => sum + (discount.amount || 0), 0);
    } else {
      // Remove all promo codes
      currentMetadata.applied_discounts = [];
      currentMetadata.discount_total = 0;
    }

    // Update cart
    await c.env.DB.prepare(`
      UPDATE carts 
      SET metadata = ?, updated_at = ? 
      WHERE id = ?
    `).bind(JSON.stringify(currentMetadata), now, cartId).run();

    // Get updated cart
    const updatedCart = await c.env.DB.prepare(`SELECT * FROM carts WHERE id = ?`).bind(cartId).first();
    const enrichedCart = await enrichCart(updatedCart, c.env.DB);

    return c.json({
      success: true,
      data: enrichedCart,
      message: 'Promo codes removed successfully'
    });

  } catch (error) {
    return handleError(c, error, 'Failed to remove promo codes');
  }
});

// GET /store/carts/:id/payment-sessions - Get payment sessions for cart
app.get('/:id/payment-sessions', async (c) => {
  try {
    const cartId = c.req.param('id');

    // Verify cart exists
    const cart = await c.env.DB.prepare(`SELECT * FROM carts WHERE id = ?`).bind(cartId).first();
    if (!cart) {
      return c.json({ success: false, error: 'Cart not found' }, 404);
    }

    // Get existing payment sessions for this cart with provider information
    const sessions = await c.env.DB.prepare(`
      SELECT 
        ps.*,
        pp.metadata as provider_metadata
      FROM payment_sessions ps
      LEFT JOIN payment_provider pp ON ps.provider_id = pp.id
      WHERE ps.cart_id = ? 
      ORDER BY ps.created_at ASC
    `).bind(cartId).all();

    // If no sessions exist, automatically initialize them
    if (!sessions.results || sessions.results.length === 0) {
      console.log(`No payment sessions found for cart ${cartId}, initializing...`);
      
      // Get available payment providers
      const providers = await c.env.DB.prepare(`
        SELECT * FROM payment_provider WHERE is_enabled = 1 ORDER BY id
      `).all();

      if (!providers.results || providers.results.length === 0) {
        return c.json({ success: false, error: 'No payment providers available' }, 400);
      }

      const now = new Date().toISOString();
      const createdSessions: any[] = [];
      
      for (const provider of providers.results) {
        const sessionId = crypto.randomUUID();
        
        let sessionData: any = {
          provider_id: provider.id,
          status: 'pending'
        };

        // For Stripe, create a PaymentIntent
        if (provider.id === 'stripe') {
          try {
            // Get cart total for Stripe PaymentIntent including shipping
            const cartItems = await c.env.DB.prepare(`
              SELECT SUM(total_price) as total FROM cart_items WHERE cart_id = ?
            `).bind(cartId).first();
            
            const cartMetadata = safeParseJson(cart.metadata) || {};
            const discountTotal = cartMetadata.discount_total || 0;
            const shippingTotal = cartMetadata.shipping_method?.price || 0;
            const taxTotal = cartMetadata.tax_total || 0;
            const subtotal = (cartItems?.total as number) || 0;
            
            // Calculate total with shipping, tax, and discounts
            const total = Math.max(0, subtotal + shippingTotal + taxTotal - discountTotal);
            
            // Convert to cents for Stripe
            const amountInCents = Math.round(total * 100);
            
            if (amountInCents > 0) {
              try {
                // Create Stripe PaymentIntent using SDK
                const stripe = getStripeInstance(c);
                
                const paymentIntent = await stripe.paymentIntents.create({
                  amount: amountInCents,
                  currency: ((cart.currency_code as string) || 'RON').toLowerCase(),
                  capture_method: 'manual', // Authorize funds but don't capture immediately
                  automatic_payment_methods: {
                    enabled: true,
                  },
                  metadata: {
                    cart_id: cartId,
                    customer_id: String(cart.customer_id || 'guest')
                  }
                });

                sessionData = {
                  provider_id: provider.id,
                  status: 'pending',
                  client_secret: paymentIntent.client_secret,
                  payment_intent_id: paymentIntent.id,
                  amount: amountInCents
                };
              } catch (stripeError) {
                console.error('Failed to create Stripe PaymentIntent:', stripeError);
                sessionData.error = 'Failed to initialize Stripe payment';
              }
            } else {
              sessionData.error = 'Cart total is 0 or invalid';
            }
          } catch (stripeError) {
            console.error('Stripe initialization error:', stripeError);
            sessionData.error = 'Failed to initialize Stripe payment';
          }
        }

        // Parse provider metadata for name and description
        const providerMetadata = safeParseJson(provider.metadata) || {};
        const providerName = providerMetadata.name || getDefaultProviderName(String(provider.id));
        const providerDescription = providerMetadata.description || '';

        // Create payment session
        await c.env.DB.prepare(`
          INSERT INTO payment_sessions (
            id, cart_id, provider_id, is_selected, is_initiated, status, data, amount, created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `).bind(
          sessionId,
          cartId,
          provider.id,
          0, // is_selected: false by default
          0, // is_initiated: false by default
          sessionData.status,
          JSON.stringify(sessionData),
          sessionData.amount || 0,
          now,
          now
        ).run();

        createdSessions.push({
          id: sessionId,
          provider_id: provider.id,
          provider_name: providerName,
          provider_description: providerDescription,
          is_selected: false,
          is_initiated: false,
          status: sessionData.status,
          data: sessionData,
          amount: sessionData.amount || 0
        });
      }

      return c.json({
        success: true,
        payment_sessions: createdSessions
      });
    }

    // Format existing sessions for response with provider information
    const formattedSessions = sessions.results.map((session: any) => {
      const providerMetadata = safeParseJson(session.provider_metadata) || {};
      const providerName = providerMetadata.name || getDefaultProviderName(String(session.provider_id));
      const providerDescription = providerMetadata.description || '';

      return {
        id: session.id,
        provider_id: session.provider_id,
        provider_name: providerName,
        provider_description: providerDescription,
        is_selected: Boolean(session.is_selected),
        is_initiated: Boolean(session.is_initiated),
        status: session.status,
        data: safeParseJson(session.data),
        amount: session.amount || 0
      };
    });

    return c.json({
      success: true,
      payment_sessions: formattedSessions
    });

  } catch (error) {
    return handleError(c, error, 'Failed to get payment sessions');
  }
  });

// POST /store/carts/:id/payment-sessions - Initialize payment sessions
app.post('/:id/payment-sessions', async (c) => {
  try {
    const cartId = c.req.param('id');

    // Verify cart exists
    const cart = await c.env.DB.prepare(`SELECT * FROM carts WHERE id = ?`).bind(cartId).first();
    if (!cart) {
      return c.json({ success: false, error: 'Cart not found' }, 404);
    }

    // Get available payment providers
    const providers = await c.env.DB.prepare(`
      SELECT * FROM payment_provider WHERE is_enabled = 1 ORDER BY id
    `).all();

    if (!providers.results || providers.results.length === 0) {
      return c.json({ success: false, error: 'No payment providers available' }, 400);
    }

    const now = new Date().toISOString();

    // Remove existing payment sessions for this cart
    await c.env.DB.prepare(`
      DELETE FROM payment_sessions WHERE cart_id = ?
    `).bind(cartId).run();

    // Create payment sessions for each provider
    const createdSessions: any[] = [];
    
    for (const provider of providers.results) {
      const sessionId = crypto.randomUUID();
      
      let sessionData: any = {
        provider_id: provider.id,
        status: 'pending'
      };

      // For Stripe, create a PaymentIntent
      if (provider.id === 'stripe') {
        try {
          // Get cart total for Stripe PaymentIntent including shipping
          const cartItems = await c.env.DB.prepare(`
            SELECT SUM(total_price) as total FROM cart_items WHERE cart_id = ?
          `).bind(cartId).first();
          
          const cartMetadata = safeParseJson(cart.metadata) || {};
          const discountTotal = cartMetadata.discount_total || 0;
          const shippingTotal = cartMetadata.shipping_method?.price || 0;
          const taxTotal = cartMetadata.tax_total || 0;
          const subtotal = (cartItems?.total as number) || 0;
          
          // Calculate total with shipping, tax, and discounts
          const total = Math.max(0, subtotal + shippingTotal + taxTotal - discountTotal);
          
          // Convert to cents for Stripe
          const amountInCents = Math.round(total * 100);
          
          if (amountInCents > 0) {
            try {
              // Create Stripe PaymentIntent using SDK
              const stripe = getStripeInstance(c);
              
              const paymentIntent = await stripe.paymentIntents.create({
                amount: amountInCents,
                currency: ((cart.currency_code as string) || 'RON').toLowerCase(),
                capture_method: 'manual', // Authorize funds but don't capture immediately
                automatic_payment_methods: {
                  enabled: true,
                },
                metadata: {
                  cart_id: cartId,
                  customer_id: String(cart.customer_id || 'guest')
                }
              });

              sessionData = {
                provider_id: provider.id,
                status: 'pending',
                client_secret: paymentIntent.client_secret,
                payment_intent_id: paymentIntent.id,
                amount: amountInCents
              };
            } catch (stripeError) {
              console.error('Failed to create Stripe PaymentIntent:', stripeError);
              sessionData.error = 'Failed to initialize Stripe payment';
            }
          } else {
            sessionData.error = 'Cart total is 0 or invalid';
          }
        } catch (stripeError) {
          console.error('Stripe initialization error:', stripeError);
          sessionData.error = 'Failed to initialize Stripe payment';
        }
      }

      // Create payment session
      await c.env.DB.prepare(`
        INSERT INTO payment_sessions (
          id, cart_id, provider_id, is_selected, is_initiated, status, data, amount, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).bind(
        sessionId,
        cartId,
        provider.id,
        0, // is_selected: false by default
        0, // is_initiated: false by default
        sessionData.status,
        JSON.stringify(sessionData),
        sessionData.amount || 0,
        now,
        now
      ).run();

      createdSessions.push({
        id: sessionId,
        provider_id: provider.id,
        is_selected: false,
        is_initiated: false,
        status: sessionData.status,
        data: sessionData,
        amount: sessionData.amount || 0
      });
    }

    return c.json({
      success: true,
      data: {
        cart_id: cartId,
        payment_sessions: createdSessions
      }
    });

  } catch (error) {
    return handleError(c, error, 'Failed to initialize payment sessions');
  }
});

// POST /store/carts/:id/payment-sessions/:provider_id - Select payment provider  
app.post('/:id/payment-sessions/:provider_id', async (c) => {
  try {
    const cartId = c.req.param('id');
    const providerId = c.req.param('provider_id');

    console.log('[Payment Selection] Attempting to select payment provider:', JSON.stringify({
      cartId,
      providerId,
      providerId_type: typeof providerId
    }));

    // Verify cart exists
    const cart = await c.env.DB.prepare(`SELECT * FROM carts WHERE id = ?`).bind(cartId).first();
    if (!cart) {
      console.log('[Payment Selection] Cart not found:', cartId);
      return c.json({ success: false, error: 'Cart not found' }, 404);
    }

    // Check if payment sessions exist for this cart
    const existingSessions = await c.env.DB.prepare(`
      SELECT COUNT(*) as count FROM payment_sessions WHERE cart_id = ?
    `).bind(cartId).first();

    console.log('[Payment Selection] Existing sessions count:', existingSessions?.count);

    // Check if the requested provider exists in current payment sessions
    let needsInitialization = false;
    if (!existingSessions || (existingSessions.count as number) === 0) {
      needsInitialization = true;
      console.log('[Payment Selection] No payment sessions exist, need to initialize');
    } else {
      // Check if the requested provider has a payment session
      const providerSession = await c.env.DB.prepare(`
        SELECT * FROM payment_sessions WHERE cart_id = ? AND provider_id = ?
      `).bind(cartId, providerId).first();
      
      if (!providerSession) {
        console.log(`[Payment Selection] Requested provider ${providerId} has no payment session, need to initialize`);
        needsInitialization = true;
      }
    }

    // If no payment sessions exist OR the requested provider doesn't have a session, initialize them
    if (needsInitialization) {
      console.log(`Payment sessions need initialization for cart ${cartId} (missing provider: ${providerId})...`);
      
      // Clear existing payment sessions to reinitialize all providers
      if (existingSessions && (existingSessions.count as number) > 0) {
        console.log('[Payment Selection] Clearing existing payment sessions before reinitializing');
        await c.env.DB.prepare(`DELETE FROM payment_sessions WHERE cart_id = ?`).bind(cartId).run();
      }
      
      // Get available payment providers
      const providers = await c.env.DB.prepare(`
        SELECT * FROM payment_provider WHERE is_enabled = 1 ORDER BY id
      `).all();

      if (!providers.results || providers.results.length === 0) {
        return c.json({ success: false, error: 'No payment providers available' }, 400);
      }

      // Verify the requested provider exists
      const requestedProvider = providers.results.find((p: any) => p.id === providerId);
      if (!requestedProvider) {
        return c.json({ success: false, error: 'Payment provider not available' }, 400);
      }

      const now = new Date().toISOString();
      
      // Create payment sessions for all providers
      for (const provider of providers.results) {
        const sessionId = crypto.randomUUID();
        
        let sessionData: any = {
          provider_id: provider.id,
          status: 'pending'
        };

        // For Stripe, create a PaymentIntent
        if (provider.id === 'stripe') {
          try {
            // Get cart total for Stripe PaymentIntent including shipping
            const cartItems = await c.env.DB.prepare(`
              SELECT SUM(total_price) as total FROM cart_items WHERE cart_id = ?
            `).bind(cartId).first();
            
            const cartMetadata = safeParseJson(cart.metadata) || {};
            const discountTotal = cartMetadata.discount_total || 0;
            const shippingTotal = cartMetadata.shipping_method?.price || 0;
            const taxTotal = cartMetadata.tax_total || 0;
            const subtotal = (cartItems?.total as number) || 0;
            
            // Calculate total with shipping, tax, and discounts
            const total = Math.max(0, subtotal + shippingTotal + taxTotal - discountTotal);
            
            // Convert to cents for Stripe
            const amountInCents = Math.round(total * 100);
            
            if (amountInCents > 0) {
              // Create Stripe PaymentIntent using SDK
              const stripe = getStripeInstance(c);
              
              const paymentIntent = await stripe.paymentIntents.create({
                amount: amountInCents,
                currency: ((cart.currency_code as string) || 'RON').toLowerCase(),
                capture_method: 'manual', // Authorize funds but don't capture immediately
                automatic_payment_methods: {
                  enabled: true,
                },
                metadata: {
                  cart_id: cartId,
                  customer_id: String(cart.customer_id || 'guest')
                }
              });

              sessionData = {
                provider_id: provider.id,
                status: 'pending',
                client_secret: paymentIntent.client_secret,
                payment_intent_id: paymentIntent.id,
                amount: amountInCents
              };
            }
          } catch (stripeError) {
            console.error('Stripe initialization error:', stripeError);
            sessionData.error = 'Failed to initialize Stripe payment';
          }
        }

        // Create payment session - select the requested provider by default
        const isSelected = provider.id === providerId ? 1 : 0;
        
        await c.env.DB.prepare(`
          INSERT INTO payment_sessions (
            id, cart_id, provider_id, is_selected, is_initiated, status, data, amount, created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `).bind(
          sessionId,
          cartId,
          provider.id,
          isSelected,
          0, // is_initiated: false by default
          sessionData.status,
          JSON.stringify(sessionData),
          sessionData.amount || 0,
          now,
          now
        ).run();
      }
    } else {
      // Payment sessions exist, just update selection
      const now = new Date().toISOString();

      // First, let's get all payment sessions to debug
      const allSessions = await c.env.DB.prepare(`
        SELECT * FROM payment_sessions WHERE cart_id = ?
      `).bind(cartId).all();
      
      console.log('[Payment Selection] All existing sessions:', allSessions.results?.map((s: any) => ({
        id: s.id,
        provider_id: s.provider_id,
        provider_id_type: typeof s.provider_id,
        is_selected: s.is_selected
      })));

      // Find the payment session for the requested provider
      // Try exact match first
      let session = await c.env.DB.prepare(`
        SELECT * FROM payment_sessions WHERE cart_id = ? AND provider_id = ?
      `).bind(cartId, providerId).first();

      // If no exact match, try to find a session that includes the provider name
      // This handles cases where frontend might send "stripe" but DB has "pp_stripe_stripe" etc.
      if (!session) {
        const fuzzySession = await c.env.DB.prepare(`
          SELECT * FROM payment_sessions WHERE cart_id = ? AND provider_id LIKE ?
        `).bind(cartId, `%${providerId}%`).first();
        
        if (fuzzySession) {
          console.log(`[Payment Selection] Found fuzzy match: ${fuzzySession.provider_id} for requested ${providerId}`);
          session = fuzzySession;
        }
      }

      console.log('[Payment Selection] Found session for provider:', JSON.stringify({
        providerId,
        session: session ? {
          id: session.id,
          provider_id: session.provider_id,
          provider_id_type: typeof session.provider_id
        } : null
      }));

      if (!session) {
        console.error('[Payment Selection] Session not found. Available providers:', 
          allSessions.results?.map((s: any) => s.provider_id));
        return c.json({ success: false, error: 'Payment session not found for provider' }, 404);
      }

      // Deselect all other payment sessions for this cart
      const deselectResult = await c.env.DB.prepare(`
        UPDATE payment_sessions SET is_selected = 0, updated_at = ? WHERE cart_id = ?
      `).bind(now, cartId).run();

      console.log('[Payment Selection] Deselect all result:', deselectResult);

      // Select this payment session
      const updateResult = await c.env.DB.prepare(`
        UPDATE payment_sessions SET is_selected = 1, updated_at = ? WHERE id = ?
      `).bind(now, session.id).run();

      console.log('[Payment Selection] Update result:', updateResult);
      console.log('[Payment Selection] Selected session ID:', session.id, 'for provider:', providerId);
      
      // Verify the update worked by checking the database
      const verifySession = await c.env.DB.prepare(`
        SELECT id, provider_id, is_selected FROM payment_sessions WHERE cart_id = ?
      `).bind(cartId).all();
      
      console.log('[Payment Selection] All sessions after update:', verifySession.results?.map((s: any) => ({
        id: s.id,
        provider_id: s.provider_id,
        is_selected: s.is_selected,
        is_selected_type: typeof s.is_selected
      })));
    }

    // Get the selected session to return
    const selectedSession = await c.env.DB.prepare(`
      SELECT * FROM payment_sessions WHERE cart_id = ? AND provider_id = ?
    `).bind(cartId, providerId).first();

    if (!selectedSession) {
      return c.json({ success: false, error: 'Failed to select payment session' }, 500);
    }

    console.log('[Payment Selection] Final selected session:', JSON.stringify({
      id: selectedSession.id,
      provider_id: selectedSession.provider_id,
      is_selected: selectedSession.is_selected,
      is_selected_type: typeof selectedSession.is_selected
    }));

    return c.json({
      success: true,
      message: 'Payment provider selected successfully',
      data: {
        ...selectedSession,
        data: safeParseJson(selectedSession.data)
      }
    });

  } catch (error) {
    return handleError(c, error, 'Failed to select payment provider');
  }
});

// GET /store/regions - Get available regions
app.get('/regions', async (c) => {
  try {
    const query = `
      SELECT id, name, code, currency_code, language_code, tax_rate, countries, metadata, created_at, updated_at
      FROM regions 
      WHERE is_active = 1
      ORDER BY created_at ASC
    `;

    const regions = await c.env.DB.prepare(query).all();

    const processedRegions = (regions.results || []).map((region: any) => {
      // Parse JSON fields safely
      const metadata = safeParseJson(region.metadata) || {};
      const countries = safeParseJson(region.countries) || [];
      
      return {
        id: region.id,
        name: region.name,
        code: region.code,
        currency_code: region.currency_code,
        language_code: region.language_code,
        tax_rate: region.tax_rate || 0,
        countries: countries,
        metadata: metadata,
        created_at: region.created_at,
        updated_at: region.updated_at
      };
    });

    return c.json({
      success: true,
      data: processedRegions
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch regions');
  }
});

// POST /store/carts/:id/complete - Complete cart and create order
app.post('/:id/complete', async (c) => {
  try {
    const cartId = c.req.param('id');

    // Verify cart exists (remove completed_at and deleted_at checks as those columns don't exist)
    const cart = await c.env.DB.prepare(`
      SELECT * FROM carts WHERE id = ?
    `).bind(cartId).first();

    if (!cart) {
      return c.json({
        success: false,
        error: 'Cart not found',
      }, 404);
    }

    // Get enriched cart with all data
    const enrichedCart = await enrichCart(cart, c.env.DB);

    // Also do a direct database query to double-check payment sessions
    const directPaymentSessions = await c.env.DB.prepare(`
      SELECT * FROM payment_sessions WHERE cart_id = ? ORDER BY created_at ASC
    `).bind(cartId).all();

    console.log('[Cart Complete] Direct DB payment sessions query:', directPaymentSessions.results?.map(s => ({
      id: s.id,
      provider_id: s.provider_id,
      is_selected: s.is_selected,
      is_selected_type: typeof s.is_selected
    })));

    // Validate cart has required data
    if (!enrichedCart.items || enrichedCart.items.length === 0) {
      return c.json({
        success: false,
        error: 'Cart is empty',
      }, 400);
    }

    if (!enrichedCart.email) {
      return c.json({
        success: false,
        error: 'Email is required',
      }, 400);
    }

    if (!enrichedCart.shipping_address) {
      return c.json({
        success: false,
        error: 'Shipping address is required',
      }, 400);
    }

    // Check if payment is authorized
    console.log('[Cart Complete] Payment sessions found:', enrichedCart.payment_sessions.map((s: any) => ({
      id: s.id,
      provider_id: s.provider_id,
      is_selected: s.is_selected,
      is_selected_type: typeof s.is_selected,
      raw_is_selected: s.is_selected
    })));

    // Try multiple ways to find the selected payment session
    let selectedPaymentSession = enrichedCart.payment_sessions.find((s: any) => s.is_selected);

    // Fallback: if no session found with boolean true, try finding one with is_selected = 1
    if (!selectedPaymentSession) {
      selectedPaymentSession = enrichedCart.payment_sessions.find((s: any) => s.is_selected === 1 || s.is_selected === '1');
    }

    // Fallback: if still no session found, check the direct DB query results
    if (!selectedPaymentSession && directPaymentSessions.results) {
      const directSelected = directPaymentSessions.results.find((s: any) => s.is_selected === 1 || s.is_selected === '1');
      if (directSelected) {
        selectedPaymentSession = {
          ...directSelected,
          data: safeParseJson(directSelected.data) || {},
          is_selected: true,
          is_initiated: Boolean(directSelected.is_initiated)
        };
        console.log('[Cart Complete] Found selected session via direct DB query:', selectedPaymentSession);
      }
    }

    console.log('[Cart Complete] Final selected payment session:', selectedPaymentSession);

    if (!selectedPaymentSession) {
      console.error('[Cart Complete] No payment method selected. Available sessions:', enrichedCart.payment_sessions);
      console.error('[Cart Complete] Direct DB sessions:', directPaymentSessions.results);
      
      // If no payment session is selected, try to auto-select a fallback payment method
      // Priority: cash_on_delivery > manual > any available method
      let fallbackSession = enrichedCart.payment_sessions.find((s: any) => s.provider_id === 'cash_on_delivery');
      if (!fallbackSession) {
        fallbackSession = enrichedCart.payment_sessions.find((s: any) => s.provider_id === 'manual');
      }
      if (!fallbackSession && enrichedCart.payment_sessions.length > 0) {
        fallbackSession = enrichedCart.payment_sessions[0]; // Use any available session
      }
      
      if (fallbackSession) {
        console.log(`[Cart Complete] Auto-selecting ${fallbackSession.provider_id} payment method as fallback`);
        
        // Update the database to select the fallback method
        const now = new Date().toISOString();
        await c.env.DB.prepare(`
          UPDATE payment_sessions SET is_selected = 0, updated_at = ? WHERE cart_id = ?
        `).bind(now, cartId).run();
        
        await c.env.DB.prepare(`
          UPDATE payment_sessions SET is_selected = 1, updated_at = ? WHERE id = ?
        `).bind(now, fallbackSession.id).run();
        
        selectedPaymentSession = {
          ...fallbackSession,
          is_selected: true
        };
        
        console.log(`[Cart Complete] Auto-selected ${fallbackSession.provider_id} session:`, selectedPaymentSession);
      } else {
        return c.json({
          success: false,
          error: 'No payment method selected',
        }, 400);
      }
    }

    // Handle Stripe payment confirmation
    if (selectedPaymentSession.provider_id === 'stripe') {
      try {
        const stripe = getStripeInstance(c);
        const sessionData = selectedPaymentSession.data || {};
        
        if (!sessionData.payment_intent_id) {
          return c.json({
            success: false,
            error: 'Stripe payment intent not found. Please refresh the payment session and try again.',
          }, 400);
        }

        // Retrieve the PaymentIntent from Stripe to verify its status
        console.log(`[Cart Complete] Verifying Stripe PaymentIntent: ${sessionData.payment_intent_id}`);
        const paymentIntent = await stripe.paymentIntents.retrieve(sessionData.payment_intent_id);
        
        console.log(`[Cart Complete] PaymentIntent status: ${paymentIntent.status}`);
        
        // Check if payment is confirmed/succeeded
        if (paymentIntent.status !== 'succeeded' && paymentIntent.status !== 'requires_capture') {
          return c.json({
            success: false,
            error: `Payment not completed. Status: ${paymentIntent.status}. Please complete the payment first.`,
          }, 400);
        }

        // Update payment session with confirmed status
        await c.env.DB.prepare(`
          UPDATE payment_sessions 
          SET status = 'completed', updated_at = datetime('now') 
          WHERE id = ?
        `).bind(selectedPaymentSession.id).run();
        
        console.log(`[Cart Complete] Stripe payment confirmed for PaymentIntent: ${sessionData.payment_intent_id}`);
        
      } catch (stripeError) {
        console.error('[Cart Complete] Stripe verification failed:', stripeError);
        return c.json({
          success: false,
          error: 'Failed to verify payment with Stripe. Please try again.',
        }, 500);
      }
    }

    // Generate order number (human-readable order number)
    const orderCountResult = await c.env.DB.prepare(`
      SELECT COUNT(*) as count FROM orders
    `).first();
    const orderCount = (orderCountResult?.count as number) || 0;
    const orderNumber = (orderCount + 1).toString().padStart(6, '0'); // e.g., "000001"

    const now = new Date().toISOString();

    // Create order with correct field names matching the actual schema
    const orderId = crypto.randomUUID();
    
    // Process cart metadata and extract easybox information if present
    const cartMetadata = enrichedCart.metadata || {};
    const orderMetadata = { ...cartMetadata };
    
    // Extract and structure easybox information if present
    let easyboxInfo = extractEasyboxInfo(cartMetadata);
    
    // Add structured easybox info to order metadata for easy access
    if (easyboxInfo) {
      orderMetadata.easybox_delivery = easyboxInfo;
      console.log(`Order ${orderId}: easybox delivery selected - ${easyboxInfo.full_address}`);
    }
    
    const orderData = {
      id: orderId,
      number: orderNumber,
      status: 'pending',
      fulfillment_status: 'unfulfilled',
      financial_status: selectedPaymentSession.provider_id === 'cash_on_delivery' ? 'pending' : 'pending',
      customer_id: cart.customer_id,
      email: cart.email,
      billing_address: enrichedCart.billing_address ? JSON.stringify(enrichedCart.billing_address) : JSON.stringify(enrichedCart.shipping_address), // Use shipping as billing if no billing provided
      shipping_address: JSON.stringify(enrichedCart.shipping_address),
      region_id: cart.region_id,
      currency_code: enrichedCart.region?.currency_code || 'RON',
      subtotal: enrichedCart.subtotal,
      shipping_amount: enrichedCart.shipping_total,
      tax_amount: enrichedCart.tax_total,
      discount_amount: enrichedCart.discount_total,
      total_amount: enrichedCart.total,
      metadata: JSON.stringify(orderMetadata),
      created_at: now,
      updated_at: now
    };

    // Insert order with correct field names
    await c.env.DB.prepare(`
      INSERT INTO orders (
        id, number, status, fulfillment_status, financial_status, customer_id, email,
        billing_address, shipping_address, region_id, currency_code,
        subtotal, tax_amount, shipping_amount, discount_amount, total_amount, metadata, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `).bind(
      orderId, orderData.number, orderData.status, orderData.fulfillment_status, orderData.financial_status,
      orderData.customer_id, orderData.email, orderData.billing_address, orderData.shipping_address, 
      orderData.region_id, orderData.currency_code, orderData.subtotal, orderData.tax_amount, 
      orderData.shipping_amount, orderData.discount_amount, orderData.total_amount,
      orderData.metadata, orderData.created_at, orderData.updated_at
    ).run();

    // Move cart items to order items
    for (const item of enrichedCart.items) {
      const orderItemId = crypto.randomUUID();
      
      // Get product and variant info for order item snapshot
      const productInfo = await c.env.DB.prepare(`
        SELECT 
          p.id as product_id,
          pt.title as product_title,
          pv.title as variant_title,
          pv.sku
        FROM product_variants pv
        LEFT JOIN products p ON pv.product_id = p.id
        LEFT JOIN product_translations pt ON p.id = pt.product_id AND pt.language_code = 'ro'
        WHERE pv.id = ?
      `).bind(item.variant_id).first();

      await c.env.DB.prepare(`
        INSERT INTO order_items (
          id, order_id, variant_id, product_title, variant_title, sku, quantity, unit_price, total_price, metadata, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).bind(
        orderItemId, orderId, item.variant_id,
        productInfo?.product_title || 'Unknown Product',
        productInfo?.variant_title || 'Default Variant',
        productInfo?.sku || null,
        item.quantity, item.unit_price, item.total_price,
        JSON.stringify(item.metadata || {}), now
      ).run();
    }

    // Mark cart as abandoned (since we don't have completed_at, we use abandoned_at to indicate it's no longer active)
    await c.env.DB.prepare(`
      UPDATE carts SET abandoned_at = ?, updated_at = ? WHERE id = ?
    `).bind(now, now, cartId).run();

    // Create payment record if needed with correct field names
    if (selectedPaymentSession.provider_id !== 'cash_on_delivery') {
      const paymentId = crypto.randomUUID();
      
      // Enhanced gateway data with PaymentIntent ID for capture later
      const sessionData = selectedPaymentSession.data || {};
      const gatewayData = {
        ...sessionData,
        payment_intent_id: sessionData.payment_intent_id,
        authorization_created_at: now,
        authorization_amount: enrichedCart.total * 100, // Store in cents
        capture_method: 'manual'
      };
      
      await c.env.DB.prepare(`
        INSERT INTO payments (
          id, order_id, amount, currency_code, provider, provider_transaction_id, type, status, gateway_data, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).bind(
        paymentId, orderId, enrichedCart.total, orderData.currency_code,
        selectedPaymentSession.provider_id, sessionData.payment_intent_id || null, 'payment', 'pending',
        JSON.stringify(gatewayData), now
      ).run();
    }

    // Track analytics for each order item
    try {
      const analyticsService = new AnalyticsService(c.env as any);
      for (const item of enrichedCart.items) {
        if (item.product_id && item.variant_id) {
          await analyticsService.trackProductPurchase(
            item.product_id,
            item.variant_id,
            item.quantity,
            item.total_price,
            typeof orderData.customer_id === 'string' ? orderData.customer_id : undefined
          );
        }
      }
      console.log('Analytics tracking completed for order:', orderId);
    } catch (analyticsError) {
      console.error('Failed to track analytics for order:', orderId, analyticsError);
      // Don't fail the order creation if analytics fails
    }

    // Send order confirmation email
    if (orderData.email) {
      try {
        console.log('Sending order confirmation email to:', orderData.email);
        
        // Create a minimal environment object for EmailService
        const emailEnv = {
          RESEND_API_KEY: c.env.RESEND_API_KEY,
          FRONTEND_URL: c.env.FRONTEND_URL || 'http://localhost:3001'
        } as any;
        
        const emailService = new EmailService(emailEnv);
        
        // Create customer object for email template
        const customer = {
          email: orderData.email,
          first_name: enrichedCart.billing_address?.first_name || enrichedCart.shipping_address?.first_name || 'Dragă client',
          last_name: enrichedCart.billing_address?.last_name || enrichedCart.shipping_address?.last_name || '',
          phone: enrichedCart.billing_address?.phone || enrichedCart.shipping_address?.phone || '',
        };

        // Create order object for email template (match the format expected by email service)
        const emailOrder = {
          id: orderId,
          display_id: orderNumber,
          number: orderNumber,
          status: orderData.status,
          items: enrichedCart.items.map((item: any) => ({
            title: item.product_title || item.variant_title || 'Product',
            quantity: item.quantity,
            unit_price: item.unit_price,
            total_price: item.total_price
          })),
          subtotal: enrichedCart.subtotal,
          shipping_total: enrichedCart.shipping_total,
          tax_total: enrichedCart.tax_total,
          discount_total: enrichedCart.discount_total,
          total: enrichedCart.total,
          shipping_address: enrichedCart.shipping_address,
          billing_address: enrichedCart.billing_address,
          metadata: orderMetadata,
          easybox_delivery: easyboxInfo,
          created_at: now
        };
        
        await emailService.sendOrderConfirmation(emailOrder, customer);
        console.log('Order confirmation email sent successfully');
      } catch (emailError) {
        console.error('Failed to send order confirmation email:', emailError);
        // Don't fail the order creation if email fails
      }
    }

    // Send admin notification email
    try {
      console.log('Sending admin notification email for order:', orderNumber);
      
      // Create a minimal environment object for EmailService (reuse the same pattern)
      const emailEnv = {
        RESEND_API_KEY: c.env.RESEND_API_KEY,
        FRONTEND_URL: c.env.FRONTEND_URL || 'http://localhost:3001'
      } as any;
      
      const emailService = new EmailService(emailEnv);
      
      // Create customer object for admin notification (reuse the same customer object)
      const adminCustomer = {
        email: orderData.email || 'N/A',
        first_name: enrichedCart.billing_address?.first_name || enrichedCart.shipping_address?.first_name || '',
        last_name: enrichedCart.billing_address?.last_name || enrichedCart.shipping_address?.last_name || '',
        phone: enrichedCart.billing_address?.phone || enrichedCart.shipping_address?.phone || '',
        id: orderData.customer_id || null,
      };

      // Create order object for admin notification (reuse the emailOrder structure)
      const adminEmailOrder = {
        id: orderId,
        number: orderNumber,
        display_id: orderNumber,
        email: orderData.email,
        customer_id: orderData.customer_id,
        total_amount: enrichedCart.total,
        subtotal_amount: enrichedCart.subtotal,
        shipping_amount: enrichedCart.shipping_total,
        tax_amount: enrichedCart.tax_total,
        created_at: now,
        billing_address: enrichedCart.billing_address,
        shipping_address: enrichedCart.shipping_address,
        items: enrichedCart.items.map((item: any) => ({
          product_title: item.product_title || item.variant_title || 'Product',
          variant_title: item.variant_title || '',
          quantity: item.quantity,
          unit_price: item.unit_price,
          sku: item.sku || ''
        })),
        status: orderData.status
      };
      
      await emailService.sendAdminOrderNotification(adminEmailOrder, adminCustomer);
      console.log('Admin notification email sent successfully');
    } catch (emailError) {
      console.error('Failed to send admin notification email:', emailError);
      // Don't fail the order creation if admin email fails
    }

    // Return success response with order data
    return c.json({
      success: true,
      data: {
        order: {
          id: orderId,
          number: orderNumber,
          status: orderData.status,
          customer_id: orderData.customer_id,
          email: orderData.email,
          items: enrichedCart.items,
          subtotal: enrichedCart.subtotal,
          shipping_total: enrichedCart.shipping_total,
          tax_total: enrichedCart.tax_total,
          discount_total: enrichedCart.discount_total,
          total: enrichedCart.total,
          shipping_address: enrichedCart.shipping_address,
          billing_address: enrichedCart.billing_address,
          metadata: orderMetadata,
          easybox_delivery: easyboxInfo,
          payment_method: {
            provider: selectedPaymentSession.provider_id,
            status: selectedPaymentSession.status
          }
        },
        type: 'order'
      },
      message: easyboxInfo 
        ? `Order created successfully. Delivery to easybox: ${easyboxInfo.full_address}`
        : 'Order created successfully'
    });

  } catch (error) {
    console.error('Error completing cart:', error);
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to complete cart',
    }, 500);
  }
});

// GET /store/orders/:id - Get order details (useful for admin interface and emails)
app.get('/orders/:id', async (c) => {
  try {
    const orderId = c.req.param('id');

    // Get order from database
    const order = await c.env.DB.prepare(`SELECT * FROM orders WHERE id = ?`).bind(orderId).first();
    
    if (!order) {
      return c.json({
        success: false,
        error: 'Order not found',
      }, 404);
    }

    // Get order items with product details
    const itemsQuery = `
      SELECT 
        oi.*,
        pv.id as variant_id, pv.sku, pv.inventory_quantity,
        p.id as product_id, p.handle as product_handle, p.thumbnail as product_thumbnail,
        pt.title as current_product_title, pt.description as product_description
      FROM order_items oi
      LEFT JOIN product_variants pv ON oi.variant_id = pv.id
      LEFT JOIN products p ON pv.product_id = p.id
      LEFT JOIN product_translations pt ON p.id = pt.product_id AND pt.language_code = 'ro'
      WHERE oi.order_id = ?
      ORDER BY oi.created_at ASC
    `;

    const items = await c.env.DB.prepare(itemsQuery).bind(orderId).all();

    // Parse metadata and extract easybox information
    const metadata = safeParseJson(order.metadata) || {};
    const easyboxInfo = extractEasyboxInfo(metadata);

    // Structure the order response with frontend-compatible field names
    const enrichedOrder = {
      ...order,
      display_id: order.number, // Frontend expects display_id
      shipping_total: order.shipping_amount, // Frontend expects shipping_total
      total: order.total_amount, // Frontend expects total
      metadata: metadata,
      billing_address: safeParseJson(order.billing_address),
      shipping_address: safeParseJson(order.shipping_address),
      items: (items.results || []).map((item: any) => ({
        ...item,
        metadata: safeParseJson(item.metadata) || {}
      })),
      easybox_delivery: easyboxInfo,
      is_easybox_delivery: isEasyboxDelivery(metadata),
      delivery_method: easyboxInfo ? 'easybox' : 'standard'
    };

    return c.json({
      success: true,
      order: enrichedOrder,
    });

  } catch (error) {
    return handleError(c, error, 'Failed to fetch order');
  }
});

export const cartSimplifiedRoutes = app;

import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { cacheMiddleware } from '../../middleware/cache';

type Bindings = {
  DB: D1Database;
  JWT_SECRET: string;
  FRONTEND_URL: string;
  ADMIN_URL: string;
  CACHE: KVNamespace;
};

const app = new Hono<{ Bindings: Bindings }>();

// CORS middleware
app.use('/*', cors({
  origin: (origin) => {
    const allowedOrigins = [
      'http://localhost:3000',
      'http://localhost:3001',
      'http://localhost:3002',
      'https://handmadein.ro',
      'https://www.handmadein.ro',
      'https://staging.handmadein.ro'
    ];
    return allowedOrigins.includes(origin) || origin?.endsWith('.handmadein.ro') ? origin : null;
  },
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization'],
  credentials: true,
}));

// Get homepage data with aggressive caching
app.get('/data', cacheMiddleware({
  ttl: 1800, // 30 minutes cache
  keyGenerator: () => 'homepage_data'
}), async (c) => {
  try {    // Check if we have cached homepage data
    let homepageData: any = await c.env.CACHE.get('homepage_data', { type: 'json' });
    
    if (!homepageData) {
      console.log('Homepage data cache miss, building from database...');
      
      // Get featured collections
      const collectionsQuery = `
        SELECT 
          c.id, c.handle, c.is_active, c.sort_order, c.image_url,
          COALESCE(ct_ro.title, ct_en.title) as title,
          COALESCE(ct_ro.description, ct_en.description) as description,
          COUNT(DISTINCT p.id) as product_count
        FROM collections c
        LEFT JOIN collection_translations ct_ro ON c.id = ct_ro.collection_id AND ct_ro.language_code = 'ro'
        LEFT JOIN collection_translations ct_en ON c.id = ct_en.collection_id AND ct_en.language_code = 'en'
        LEFT JOIN products p ON c.id = p.collection_id AND p.deleted_at IS NULL AND p.status = 'published'
        WHERE c.deleted_at IS NULL AND c.is_active = 1 AND c.sort_order IS NOT NULL
        GROUP BY c.id
        ORDER BY c.sort_order ASC
        LIMIT 6
      `;
      
      // Get popular products
      const popularProductsQuery = `
        SELECT 
          p.id, p.handle, p.status, p.thumbnail,
          COALESCE(pt_ro.title, pt_en.title, p.handle) as title,
          COALESCE(pt_ro.subtitle, pt_en.subtitle) as subtitle,
          COALESCE(SUM(os.total_orders), 0) as total_orders,
          COALESCE(AVG(os.popularity_score), 0) as popularity_score
        FROM products p
        LEFT JOIN product_translations pt_ro ON p.id = pt_ro.product_id AND pt_ro.language_code = 'ro'
        LEFT JOIN product_translations pt_en ON p.id = pt_en.product_id AND pt_en.language_code = 'en'
        LEFT JOIN order_statistics os ON p.id = os.product_id
        WHERE p.deleted_at IS NULL AND p.status = 'published'
        GROUP BY p.id
        ORDER BY popularity_score DESC, total_orders DESC
        LIMIT 8
      `;
      
      // Get latest products
      const latestProductsQuery = `
        SELECT 
          p.id, p.handle, p.thumbnail,
          COALESCE(pt_ro.title, pt_en.title, p.handle) as title,
          COALESCE(pt_ro.subtitle, pt_en.subtitle) as subtitle,
          p.created_at
        FROM products p
        LEFT JOIN product_translations pt_ro ON p.id = pt_ro.product_id AND pt_ro.language_code = 'ro'
        LEFT JOIN product_translations pt_en ON p.id = pt_en.product_id AND pt_en.language_code = 'en'
        WHERE p.deleted_at IS NULL AND p.status = 'published'
        ORDER BY p.created_at DESC
        LIMIT 8
      `;
      
      // Execute all queries in parallel
      const [collectionsResult, popularResult, latestResult] = await Promise.all([
        c.env.DB.prepare(collectionsQuery).all(),
        c.env.DB.prepare(popularProductsQuery).all(),
        c.env.DB.prepare(latestProductsQuery).all()
      ]);
      
      homepageData = {
        featured_collections: collectionsResult.results || [],
        popular_products: popularResult.results || [],
        latest_products: latestResult.results || [],
        stats: {
          total_collections: (collectionsResult.results || []).length,
          total_popular: (popularResult.results || []).length,
          total_latest: (latestResult.results || []).length
        },
        updated_at: new Date().toISOString(),
        cached: false
      };
      
      // Cache for 30 minutes
      await c.env.CACHE.put('homepage_data', JSON.stringify(homepageData), {
        expirationTtl: 1800
      });
      
      console.log(`Built and cached homepage data with ${homepageData.featured_collections.length} collections, ${homepageData.popular_products.length} popular products, ${homepageData.latest_products.length} latest products`);
    } else {
      console.log('Homepage data served from cache');
      homepageData.cached = true;
    }
    
    return c.json({
      success: true,
      data: homepageData
    });
    
  } catch (error) {
    console.error('Error fetching homepage data:', error);
    return c.json({
      success: false,
      error: 'Failed to fetch homepage data'
    }, 500);
  }
});

// Get quick stats for homepage (heavily cached)
app.get('/stats', cacheMiddleware({
  ttl: 3600, // 1 hour cache
  keyGenerator: () => 'homepage_stats'
}), async (c) => {
  try {    // Try to get from cache first
    let stats: any = await c.env.CACHE.get('homepage_stats', { type: 'json' });
    
    if (!stats) {
      console.log('Homepage stats cache miss, calculating...');
      
      // Get counts from database
      const [
        productsResult,
        collectionsResult,
        ordersResult
      ] = await Promise.all([
        c.env.DB.prepare('SELECT COUNT(*) as count FROM products WHERE deleted_at IS NULL AND status = "published"').first(),
        c.env.DB.prepare('SELECT COUNT(*) as count FROM collections WHERE deleted_at IS NULL AND is_active = 1').first(),
        c.env.DB.prepare('SELECT COUNT(*) as count FROM orders WHERE deleted_at IS NULL').first()
      ]);
      
      stats = {
        total_products: (productsResult as any)?.count || 0,
        total_collections: (collectionsResult as any)?.count || 0,
        total_orders: (ordersResult as any)?.count || 0,
        updated_at: new Date().toISOString(),
        cached: false
      };
      
      // Cache for 1 hour
      await c.env.CACHE.put('homepage_stats', JSON.stringify(stats), {
        expirationTtl: 3600
      });
      
      console.log(`Calculated and cached homepage stats: ${stats.total_products} products, ${stats.total_collections} collections, ${stats.total_orders} orders`);
    } else {
      console.log('Homepage stats served from cache');
      stats.cached = true;
    }
    
    return c.json({
      success: true,
      stats
    });
    
  } catch (error) {
    console.error('Error fetching homepage stats:', error);
    return c.json({
      success: false,
      error: 'Failed to fetch homepage stats'
    }, 500);
  }
});

export { app as homepageRoutes };

import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { CacheService } from '../../services/cache';
import { CacheInvalidationService } from '../../services/cache-invalidation';
import { CacheWarmingService } from '../../services/cache-warming';

type Bindings = {
  DB: D1Database;
  JWT_SECRET: string;
  FRONTEND_URL: string;
  ADMIN_URL: string;
  CACHE: KVNamespace;
  SESSIONS: KVNamespace;
};

const app = new Hono<{ Bindings: Bindings }>();

// CORS middleware
app.use('/*', cors({
  origin: (origin) => {
    const allowedOrigins = [
      'http://localhost:3000',
      'http://localhost:3001',
      'http://localhost:3002',
      'https://admin.handmadein.ro',
      'https://admin-staging.handmadein.ro'
    ];
    return allowedOrigins.includes(origin) || origin?.endsWith('.handmadein.ro') ? origin : null;
  },
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization'],
  credentials: true,
}));

// Get cache statistics
app.get('/stats', async (c) => {
  try {
    const invalidationService = new CacheInvalidationService(c.env);
    const stats = await invalidationService.getCacheStats();
    
    return c.json({
      success: true,
      stats
    });
  } catch (error) {
    console.error('Error getting cache stats:', error);
    return c.json({
      success: false,
      error: 'Failed to get cache statistics'
    }, 500);
  }
});

// Get cache warming status
app.get('/warming/status', async (c) => {
  try {
    const warmingService = new CacheWarmingService(c.env);
    const status = await warmingService.getCacheWarmingStatus();
    
    return c.json({
      success: true,
      status
    });
  } catch (error) {
    console.error('Error getting cache warming status:', error);
    return c.json({
      success: false,
      error: 'Failed to get cache warming status'
    }, 500);
  }
});

// Warm all caches
app.post('/warming/all', async (c) => {
  try {
    const warmingService = new CacheWarmingService(c.env);
    await warmingService.warmAllCaches();
    
    return c.json({
      success: true,
      message: 'Cache warming completed'
    });
  } catch (error) {
    console.error('Error warming caches:', error);
    return c.json({
      success: false,
      error: 'Failed to warm caches'
    }, 500);
  }
});

// Warm specific cache
app.post('/warming/:type', async (c) => {
  try {
    const type = c.req.param('type');
    const warmingService = new CacheWarmingService(c.env);
    
    switch (type) {
      case 'payment-methods':
        await warmingService.warmPaymentMethods();
        break;
      case 'popular-products':
        await warmingService.warmPopularProducts();
        break;
      case 'featured-collections':
        await warmingService.warmFeaturedCollections();
        break;
      case 'shipping-methods':
        await warmingService.warmShippingMethods();
        break;
      case 'homepage':
        await warmingService.warmHomepageData();
        break;
      default:
        return c.json({
          success: false,
          error: `Unknown cache type: ${type}`
        }, 400);
    }
    
    return c.json({
      success: true,
      message: `Cache warming completed for ${type}`
    });
  } catch (error) {
    console.error(`Error warming ${c.req.param('type')} cache:`, error);
    return c.json({
      success: false,
      error: `Failed to warm ${c.req.param('type')} cache`
    }, 500);
  }
});

// Invalidate specific cache patterns
app.delete('/invalidate/:pattern', async (c) => {
  try {
    const pattern = c.req.param('pattern');
    const invalidationService = new CacheInvalidationService(c.env);
    
    const deleted = await invalidationService.invalidatePattern(pattern);
    
    return c.json({
      success: true,
      message: `Invalidated cache pattern: ${pattern}`,
      deleted
    });
  } catch (error) {
    console.error('Error invalidating cache pattern:', error);
    return c.json({
      success: false,
      error: 'Failed to invalidate cache pattern'
    }, 500);
  }
});

// Invalidate product cache
app.delete('/invalidate/product/:id', async (c) => {
  try {
    const productId = c.req.param('id');
    const handle = c.req.query('handle');
    const invalidationService = new CacheInvalidationService(c.env);
    
    await invalidationService.invalidateProduct(productId, handle);
    
    return c.json({
      success: true,
      message: `Invalidated cache for product ${productId}`
    });
  } catch (error) {
    console.error('Error invalidating product cache:', error);
    return c.json({
      success: false,
      error: 'Failed to invalidate product cache'
    }, 500);
  }
});

// Invalidate collection cache
app.delete('/invalidate/collection/:id', async (c) => {
  try {
    const collectionId = c.req.param('id');
    const handle = c.req.query('handle');
    const invalidationService = new CacheInvalidationService(c.env);
    
    await invalidationService.invalidateCollection(collectionId, handle);
    
    return c.json({
      success: true,
      message: `Invalidated cache for collection ${collectionId}`
    });
  } catch (error) {
    console.error('Error invalidating collection cache:', error);
    return c.json({
      success: false,
      error: 'Failed to invalidate collection cache'
    }, 500);
  }
});

// Clear all cache (emergency use)
app.delete('/clear-all', async (c) => {
  try {
    const invalidationService = new CacheInvalidationService(c.env);
    const deleted = await invalidationService.clearAllCache();
    
    return c.json({
      success: true,
      message: `Cleared all cache`,
      deleted
    });
  } catch (error) {
    console.error('Error clearing all cache:', error);
    return c.json({
      success: false,
      error: 'Failed to clear all cache'
    }, 500);
  }
});

// Get cache health check
app.get('/health', async (c) => {
  try {
    // Test KV availability
    await c.env.CACHE.get('health-check');
    await c.env.CACHE.put('health-check', 'ok', { expirationTtl: 60 });
    
    return c.json({
      success: true,
      cache_available: true,
      timestamp: new Date().toISOString()
    });  } catch (error) {
    console.error('Cache health check failed:', error);
    return c.json({
      success: false,
      cache_available: false,
      error: error instanceof Error ? error.message : String(error)
    }, 500);
  }
});

export { app as cacheManagementRoutes };

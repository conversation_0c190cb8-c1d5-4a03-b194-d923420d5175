import { Context, Next } from 'hono';
import { sessionMiddleware, SessionMiddlewareOptions } from './session';

/**
 * PERFORMANCE: High-performance session configurations for different route types
 * These configurations are optimized to minimize KV operations and improve response times
 */

/**
 * Ultra-fast session configuration for checkout and payment routes
 * Minimizes all session operations to ensure fastest possible checkout experience
 */
export function checkoutSessionMiddleware() {
  return sessionMiddleware({
    autoCreate: false, // Don't auto-create sessions for checkout to save time
    trackActivity: false, // No activity tracking during checkout
    trackPages: false, // No page tracking during checkout
    lazyUpdate: true, // Always use lazy updates
    minUpdateInterval: 3600, // 1 hour - very infrequent updates
    skipSession: false // We still need session for cart ID tracking
  });
}

/**
 * Performance-optimized session for cart operations
 * Balances functionality with performance
 */
export function cartSessionMiddleware() {
  return sessionMiddleware({
    autoCreate: true, // Auto-create for cart persistence
    trackActivity: false, // No activity tracking for cart routes
    trackPages: false, // No page tracking for cart routes
    lazyUpdate: true, // Use lazy updates
    minUpdateInterval: 600 // 10 minutes between updates
  });
}

/**
 * Minimal session for API routes that only need cart ID
 * Skips most session functionality for maximum performance
 */
export function apiSessionMiddleware() {
  return sessionMiddleware({
    autoCreate: false, // Don't auto-create for API routes
    trackActivity: false, // No tracking
    trackPages: false, // No tracking
    lazyUpdate: true, // Lazy updates only
    minUpdateInterval: 1800 // 30 minutes
  });
}

/**
 * No-session middleware for routes that don't need session at all
 * Maximum performance by completely skipping session operations
 */
export function noSessionMiddleware() {
  return sessionMiddleware({
    skipSession: true // Skip all session operations
  });
}

/**
 * Smart session middleware that automatically chooses the best configuration
 * based on the request path
 */
export function smartSessionMiddleware() {
  return async (c: Context, next: Next) => {
    const url = new URL(c.req.url);
    const path = url.pathname;

    // Determine the best session configuration based on path
    if (path.includes('/checkout') || path.includes('/payment') || path.includes('/complete')) {
      // Use checkout-optimized session for critical paths
      return checkoutSessionMiddleware()(c, next);
    } else if (path.includes('/cart')) {
      // Use cart-optimized session for cart operations
      return cartSessionMiddleware()(c, next);
    } else if (path.includes('/api/') && !path.includes('/session')) {
      // Use minimal session for API routes
      return apiSessionMiddleware()(c, next);
    } else if (path.includes('/health') || path.includes('/status') || path.includes('/ping')) {
      // Skip session entirely for health checks
      return noSessionMiddleware()(c, next);
    } else {
      // Use default optimized session for other routes
      return sessionMiddleware({
        autoCreate: true,
        trackActivity: false, // Disabled by default for performance
        trackPages: false, // Disabled by default for performance
        lazyUpdate: true,
        minUpdateInterval: 300 // 5 minutes
      })(c, next);
    }
  };
}

/**
 * Session bypass middleware for routes that absolutely don't need sessions
 * Use this for static content, health checks, etc.
 */
export function bypassSessionMiddleware() {
  return async (c: Context, next: Next) => {
    // Skip all session processing
    await next();
  };
}

/**
 * Critical path middleware - optimized for the most performance-sensitive routes
 * Use this for payment processing, order completion, etc.
 */
export function criticalPathSessionMiddleware() {
  return sessionMiddleware({
    autoCreate: false, // Don't create sessions on critical paths
    trackActivity: false, // No tracking
    trackPages: false, // No tracking
    lazyUpdate: true, // Always lazy
    minUpdateInterval: 7200, // 2 hours - very infrequent
    skipSession: false // Still need basic session for cart/user ID
  });
}

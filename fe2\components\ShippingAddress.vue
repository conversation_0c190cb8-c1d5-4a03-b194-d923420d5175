<template>
  <div>
    <h2 class="text-2xl font-semibold mb-6">{{ $t('checkout.shipping_address') }}</h2>
    
    <!-- Email Field -->
    <div v-if="!isAuthenticated" class="mb-6">
      <label for="email" class="block text-sm font-medium text-forest-700 mb-1">
        {{ $t('checkout.email_address') }}
      </label>
      <input
        id="email"
        v-model="email"
        type="email"
        required
        class="w-full rounded-md border-wheat-300 shadow-sm focus:border-terracotta-500 focus:ring-terracotta-500 sm:text-sm"
        :class="{'border-romana-red': validationErrors.email}"
        :placeholder="$t('checkout.email_placeholder')"
        @blur="validateField('email')"
      />
      <p v-if="validationErrors.email" class="mt-1 text-sm text-romana-red">{{ validationErrors.email }}</p>
    </div>
    
    <!-- Save Address Checkbox -->
    <div v-if="isAuthenticated" class="flex items-center mt-2">
      <input 
        id="saveAddress" 
        v-model="saveAddress" 
        type="checkbox" 
        class="h-4 w-4 text-terracotta-500 focus:ring-terracotta-500 border-wheat-300 rounded"
      />
      <label for="saveAddress" class="ml-2 block text-sm text-forest-700">
        {{ $t('checkout.save_address') }}
      </label>
    </div>
    
    <!-- Create Account Section - Only for non-authenticated users -->
    <div v-if="!isAuthenticated" class="mt-6 p-4 border border-forest-100 rounded-md bg-forest-50">
      <div class="flex items-center mb-3">
        <input 
          id="createAccount" 
          v-model="createAccount" 
          type="checkbox" 
          class="h-4 w-4 text-terracotta-500 focus:ring-terracotta-500 border-wheat-300 rounded"
        />
        <label for="createAccount" class="ml-2 block text-base font-medium text-forest-700">
          {{ $t('checkout.create_account') }}
        </label>
      </div>
      
      <p v-if="createAccount" class="text-sm text-forest-600 mb-4">
        {{ $t('checkout.create_account_description') }}
      </p>
      
      <div v-if="createAccount" class="space-y-4">
        <!-- Password -->
        <div>
          <label for="password" class="block text-sm font-medium text-gray-700 mb-1">
            {{ $t('account.password') }} <span class="text-red-500">*</span>
          </label>
          <input
            id="password"
            v-model="password"
            type="password"
            @blur="validateField('password')"
            class="w-full rounded-md border-gray-300 shadow-sm focus:border-forest-500 focus:ring-forest-500"
          />
          <p v-if="validationErrors.password" class="mt-1 text-sm text-red-600">
            {{ validationErrors.password }}
          </p>
        </div>
        
        <!-- Confirm Password -->
        <div>
          <label for="confirmPassword" class="block text-sm font-medium text-gray-700 mb-1">
            {{ $t('account.confirm_password') }} <span class="text-red-500">*</span>
          </label>
          <input
            id="confirmPassword"
            v-model="confirmPassword"
            type="password"
            @blur="validateField('confirmPassword')"
            class="w-full rounded-md border-gray-300 shadow-sm focus:border-forest-500 focus:ring-forest-500"
          />
          <p v-if="validationErrors.confirmPassword" class="mt-1 text-sm text-red-600">
            {{ validationErrors.confirmPassword }}
          </p>
        </div>
      </div>
    </div>
    
    <!-- Saved Addresses Section -->
    <div v-if="shippingAddresses.length > 0" class="mb-6">
      <p class="text-forest-700 mb-3">{{ $t('checkout.select_existing_address', 'Select from your saved addresses') }}</p>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div
          v-for="address in shippingAddresses"
          :key="address.id"
          @click="selectSavedAddress(address)"
          class="border rounded-lg p-4 cursor-pointer transition-all duration-200"
          :class="[
            selectedAddressId === address.id
              ? 'border-terracotta-500 bg-terracotta-50 ring-1 ring-terracotta-500'
              : 'border-wheat-300 hover:border-terracotta-300'
          ]"
        >
          <div class="flex items-start justify-between">
            <div>
              <div class="font-medium">{{ address.first_name }} {{ address.last_name }}</div>
              <div class="text-sm text-forest-700 mt-1">
                <div>{{ address.address_1 }}</div>
                <div v-if="address.address_2">{{ address.address_2 }}</div>
                <div>{{ address.postal_code }} {{ address.city }}, {{ address.province }}</div>
                <div>{{ getCountryName(address.country_code) }}</div>
                <div v-if="address.phone">{{ address.phone }}</div>
              </div>
            </div>
            <div>
              <input
                type="radio"
                :checked="selectedAddressId === address.id"
                class="h-4 w-4 text-terracotta-500 focus:ring-terracotta-500 border-wheat-300"
                @click.stop
              />
            </div>
          </div>
        </div>
      </div>
      
      <div class="flex items-center mt-4">
        <p class="text-sm text-forest-700">{{ $t('checkout.or_enter_new_address', 'Or enter a new address:') }}</p>
        <div class="grow border-t border-gray-200 ml-3"></div>
      </div>
    </div>
    
    <!-- Enter Address Form -->
    <form @submit.prevent="submitForm" class="space-y-6">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- First Name -->
        <div>
          <label for="firstName" class="block text-sm font-medium text-gray-700 mb-1">
            {{ $t('checkout.first_name') }} <span class="text-red-500">*</span>
          </label>
          <input
            id="firstName"
            v-model="form.first_name"
            type="text"
            required
            class="w-full rounded-md border-gray-300 shadow-sm focus:border-forest-500 focus:ring-forest-500"
            :class="{'border-red-500': validationErrors.first_name}"
            :placeholder="$t('checkout.first_name_placeholder')"
            @blur="validateField('first_name')"
          />
          <p v-if="validationErrors.first_name" class="mt-1 text-sm text-red-600">{{ validationErrors.first_name }}</p>
        </div>
        
        <!-- Last Name -->
        <div>
          <label for="lastName" class="block text-sm font-medium text-gray-700 mb-1">
            {{ $t('checkout.last_name') }} <span class="text-red-500">*</span>
          </label>
          <input
            id="lastName"
            v-model="form.last_name"
            type="text"
            required
            class="w-full rounded-md border-gray-300 shadow-sm focus:border-forest-500 focus:ring-forest-500"
            :class="{'border-red-500': validationErrors.last_name}"
            :placeholder="$t('checkout.last_name_placeholder')"
            @blur="validateField('last_name')"
          />
          <p v-if="validationErrors.last_name" class="mt-1 text-sm text-red-600">{{ validationErrors.last_name }}</p>
        </div>
      </div>
      
      <!-- Company -->
      <div>
        <label for="company" class="block text-sm font-medium text-gray-700 mb-1">
          {{ $t('checkout.company') }}
        </label>
        <input
          id="company"
          v-model="form.company"
          type="text"
          class="w-full rounded-md border-gray-300 shadow-sm focus:border-forest-500 focus:ring-forest-500"
          :placeholder="$t('checkout.company_placeholder')"
        />
      </div>
      
      <!-- Address Line 1 -->
      <div>
        <label for="address1" class="block text-sm font-medium text-gray-700 mb-1">
          {{ $t('checkout.address_1') }} <span class="text-red-500">*</span>
        </label>
        <input
          id="address1"
          v-model="form.address_1"
          type="text"
          required
          class="w-full rounded-md border-gray-300 shadow-sm focus:border-forest-500 focus:ring-forest-500"
          :class="{'border-red-500': validationErrors.address_1}"
          :placeholder="$t('checkout.address_1_placeholder')"
          @blur="validateField('address_1')"
        />
        <p v-if="validationErrors.address_1" class="mt-1 text-sm text-red-600">{{ validationErrors.address_1 }}</p>
      </div>
      
      <!-- Address Line 2 -->
      <div>
        <label for="address2" class="block text-sm font-medium text-gray-700 mb-1">
          {{ $t('checkout.address_2') }}
        </label>
        <input
          id="address2"
          v-model="form.address_2"
          type="text"
          class="w-full rounded-md border-gray-300 shadow-sm focus:border-forest-500 focus:ring-forest-500"
          :placeholder="$t('checkout.address_2_placeholder')"
        />
      </div>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- City -->
        <div>
          <label for="city" class="block text-sm font-medium text-gray-700 mb-1">
            {{ $t('checkout.city') }} <span class="text-red-500">*</span>
          </label>
          <input
            id="city"
            v-model="form.city"
            type="text"
            required
            class="w-full rounded-md border-gray-300 shadow-sm focus:border-forest-500 focus:ring-forest-500"
            :class="{'border-red-500': validationErrors.city}"
            :placeholder="$t('checkout.city_placeholder')"
            @blur="validateField('city')"
          />
          <p v-if="validationErrors.city" class="mt-1 text-sm text-red-600">{{ validationErrors.city }}</p>
        </div>
        
        <!-- Province/County -->
        <div>
          <label for="province" class="block text-sm font-medium text-gray-700 mb-1">
            {{ $t('checkout.province') }}
          </label>
          <select v-if="countryRegions.length > 0"
            id="province"
            v-model="form.province"
            class="w-full rounded-md border-gray-300 shadow-sm focus:border-forest-500 focus:ring-forest-500"
          >
            <option value="" disabled>{{ $t('checkout.select_province') }}</option>
            <option v-for="region in countryRegions" :key="region" :value="region">
              {{ region }}
            </option>
          </select>
          <input v-else
            id="province"
            v-model="form.province"
            type="text"
            class="w-full rounded-md border-gray-300 shadow-sm focus:border-forest-500 focus:ring-forest-500"
            :placeholder="$t('checkout.province_placeholder')"
          />
        </div>
      </div>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Postal Code -->
        <div>
          <label for="postalCode" class="block text-sm font-medium text-gray-700 mb-1">
            {{ $t('checkout.postal_code') }} <span class="text-red-500">*</span>
          </label>
          <input
            id="postalCode"
            v-model="form.postal_code"
            type="text"
            required
            class="w-full rounded-md border-gray-300 shadow-sm focus:border-forest-500 focus:ring-forest-500"
            :class="{'border-red-500': validationErrors.postal_code}"
            :placeholder="$t('checkout.postal_code_placeholder')"
            @blur="validateField('postal_code')"
          />
          <p v-if="validationErrors.postal_code" class="mt-1 text-sm text-red-600">{{ validationErrors.postal_code }}</p>
        </div>
        
        <!-- Country -->
        <div>
          <label for="country" class="block text-sm font-medium text-gray-700 mb-1">
            {{ $t('checkout.country') }} <span class="text-red-500">*</span>
          </label>
          <input
            id="country"
            :value="getCountryName(form.country_code)"
            type="text"
            disabled
            class="w-full rounded-md border-gray-300 bg-gray-100 shadow-sm"
          />
          <p v-if="validationErrors.country_code" class="mt-1 text-sm text-red-600">{{ validationErrors.country_code }}</p>
        </div>
      </div>
      
      <!-- Phone -->
      <div>
        <label for="phone" class="block text-sm font-medium text-gray-700 mb-1">
          {{ $t('checkout.phone') }} <span class="text-red-500">*</span>
        </label>
        <div class="relative">
          <span class="absolute inset-y-0 left-0 flex items-center pl-3 text-forest-600 font-medium">
            {{ phonePrefix }}
          </span>
          <input
            id="phone"
            v-model="phoneWithoutPrefix"
            type="tel"
            required
            class="w-full pl-12 rounded-md border-gray-300 shadow-sm focus:border-forest-500 focus:ring-forest-500"
            :class="{'border-red-500': validationErrors.phone}"
            :placeholder="$t('checkout.phone_placeholder')"
            @blur="validateField('phone')"
          />
        </div>
        <p v-if="validationErrors.phone" class="mt-1 text-sm text-red-600">{{ validationErrors.phone }}</p>
      </div>
      
      <!-- Billing Address Toggle -->
      <BillingAddressToggle 
        :sameAsShipping="sameAsBilling" 
        @update:sameAsShipping="sameAsBilling = $event"
      >
        <template #billingForm>
          <!-- Billing Address Form -->
          <div v-if="billingAddresses.length > 0" class="mb-6">
            <p class="text-forest-700 mb-3">{{ $t('checkout.select_existing_billing', 'Select from your saved billing addresses') }}</p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div
                v-for="address in billingAddresses"
                :key="address.id"
                @click="selectBillingAddress(address)"
                class="border rounded-lg p-4 cursor-pointer transition-all duration-200"
                :class="[
                  selectedBillingAddressId === address.id
                    ? 'border-blue-500 bg-blue-50 ring-1 ring-blue-500'
                    : 'border-wheat-300 hover:border-blue-300'
                ]"
              >
                <div class="flex items-start justify-between">
                  <div>
                    <div class="font-medium">{{ address.first_name }} {{ address.last_name }}</div>
                    <div class="text-sm text-forest-700 mt-1">
                      <div>{{ address.address_1 }}</div>
                      <div v-if="address.address_2">{{ address.address_2 }}</div>
                      <div>{{ address.postal_code }} {{ address.city }}, {{ address.province }}</div>
                      <div>{{ getCountryName(address.country_code) }}</div>
                      <div v-if="address.phone">{{ address.phone }}</div>
                    </div>
                  </div>
                  <div>
                    <input
                      type="radio"
                      :checked="selectedBillingAddressId === address.id"
                      class="h-4 w-4 text-blue-500 focus:ring-blue-500 border-wheat-300"
                      @click.stop
                    />
                  </div>
                </div>
              </div>
            </div>
            
            <div class="flex items-center mt-4">
              <p class="text-sm text-forest-700">{{ $t('checkout.or_enter_new_billing', 'Or enter a new billing address:') }}</p>
              <div class="grow border-t border-gray-200 ml-3"></div>
            </div>
          </div>
          
          <!-- Billing Address Form -->
          <div class="space-y-6">
            <!-- Billing form fields here (same structure as shipping address form) -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <!-- First Name -->
              <div>
                <label for="billingFirstName" class="block text-sm font-medium text-gray-700 mb-1">
                  {{ $t('checkout.first_name') }} <span class="text-red-500">*</span>
                </label>
                <input
                  id="billingFirstName"
                  v-model="billingForm.first_name"
                  type="text"
                  required
                  class="w-full rounded-md border-gray-300 shadow-sm focus:border-forest-500 focus:ring-forest-500"
                  :placeholder="$t('checkout.first_name_placeholder')"
                />
              </div>
              
              <!-- Last Name -->
              <div>
                <label for="billingLastName" class="block text-sm font-medium text-gray-700 mb-1">
                  {{ $t('checkout.last_name') }} <span class="text-red-500">*</span>
                </label>
                <input
                  id="billingLastName"
                  v-model="billingForm.last_name"
                  type="text"
                  required
                  class="w-full rounded-md border-gray-300 shadow-sm focus:border-forest-500 focus:ring-forest-500"
                  :placeholder="$t('checkout.last_name_placeholder')"
                />
              </div>
            </div>
            
            <!-- Company -->
            <div>
              <label for="billingCompany" class="block text-sm font-medium text-gray-700 mb-1">
                {{ $t('checkout.company') }}
              </label>
              <input
                id="billingCompany"
                v-model="billingForm.company"
                type="text"
                class="w-full rounded-md border-gray-300 shadow-sm focus:border-forest-500 focus:ring-forest-500"
                :placeholder="$t('checkout.company_placeholder')"
              />
            </div>
            
            <!-- Address Line 1 -->
            <div>
              <label for="billingAddress1" class="block text-sm font-medium text-gray-700 mb-1">
                {{ $t('checkout.address_line_1') }} <span class="text-red-500">*</span>
              </label>
              <input
                id="billingAddress1"
                v-model="billingForm.address_1"
                type="text"
                required
                class="w-full rounded-md border-gray-300 shadow-sm focus:border-forest-500 focus:ring-forest-500"
                :placeholder="$t('checkout.address_line_1_placeholder')"
              />
            </div>
            
            <!-- Address Line 2 -->
            <div>
              <label for="billingAddress2" class="block text-sm font-medium text-gray-700 mb-1">
                {{ $t('checkout.address_line_2') }}
              </label>
              <input
                id="billingAddress2"
                v-model="billingForm.address_2"
                type="text"
                class="w-full rounded-md border-gray-300 shadow-sm focus:border-forest-500 focus:ring-forest-500"
                :placeholder="$t('checkout.address_line_2_placeholder')"
              />
            </div>
            
            <!-- City, State, ZIP in one row -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
              <!-- City -->
              <div>
                <label for="billingCity" class="block text-sm font-medium text-gray-700 mb-1">
                  {{ $t('checkout.city') }} <span class="text-red-500">*</span>
                </label>
                <input
                  id="billingCity"
                  v-model="billingForm.city"
                  type="text"
                  required
                  class="w-full rounded-md border-gray-300 shadow-sm focus:border-forest-500 focus:ring-forest-500"
                  :placeholder="$t('checkout.city_placeholder')"
                />
              </div>
              
              <!-- State/Province -->
              <div>
                <label for="billingProvince" class="block text-sm font-medium text-gray-700 mb-1">
                  {{ $t('checkout.province') }} <span class="text-red-500">*</span>
                </label>
                <select v-if="billingCountryRegions.length > 0"
                  id="billingProvince"
                  v-model="billingForm.province"
                  required
                  class="w-full rounded-md border-gray-300 shadow-sm focus:border-forest-500 focus:ring-forest-500"
                >
                  <option value="" disabled>{{ $t('checkout.select_province') }}</option>
                  <option v-for="region in billingCountryRegions" :key="region" :value="region">
                    {{ region }}
                  </option>
                </select>
                <input v-else
                  id="billingProvince"
                  v-model="billingForm.province"
                  type="text"
                  required
                  class="w-full rounded-md border-gray-300 shadow-sm focus:border-forest-500 focus:ring-forest-500"
                  :placeholder="$t('checkout.province_placeholder')"
                />
              </div>
              
              <!-- ZIP/Postal Code -->
              <div>
                <label for="billingPostalCode" class="block text-sm font-medium text-gray-700 mb-1">
                  {{ $t('checkout.postal_code') }} <span class="text-red-500">*</span>
                </label>
                <input
                  id="billingPostalCode"
                  v-model="billingForm.postal_code"
                  type="text"
                  required
                  class="w-full rounded-md border-gray-300 shadow-sm focus:border-forest-500 focus:ring-forest-500"
                  :placeholder="$t('checkout.postal_code_placeholder')"
                />
              </div>
            </div>
            
            <!-- Country -->
            <div>
              <label for="billingCountry" class="block text-sm font-medium text-gray-700 mb-1">
                {{ $t('checkout.country') }} <span class="text-red-500">*</span>
              </label>
              <input
                id="billingCountry"
                :value="getCountryName(billingForm.country_code)"
                type="text"
                disabled
                class="w-full rounded-md border-gray-300 bg-gray-100 shadow-sm"
              />
              <input
                type="hidden"
                v-model="billingForm.country_code"
              />
              <p v-if="validationErrors.country_code" class="mt-1 text-sm text-red-600">{{ validationErrors.country_code }}</p>
            </div>
            
            <!-- Phone -->
            <div>
              <label for="billingPhone" class="block text-sm font-medium text-gray-700 mb-1">
                {{ $t('checkout.phone') }}
              </label>
              <div class="relative">
                <span class="absolute inset-y-0 left-0 flex items-center pl-3 text-forest-600 font-medium">
                  {{ billingPhonePrefix }}
                </span>
                <input
                  id="billingPhone"
                  v-model="billingPhoneWithoutPrefix"
                  type="tel"
                  class="w-full pl-12 rounded-md border-gray-300 shadow-sm focus:border-forest-500 focus:ring-forest-500"
                  :placeholder="$t('checkout.phone_placeholder')"
                />
              </div>
            </div>
            
            <!-- Save Billing Address Checkbox -->
            <div v-if="isAuthenticated" class="flex items-center mt-6">
              <input 
                id="saveBillingAddress" 
                v-model="saveBillingAddress" 
                type="checkbox" 
                class="h-4 w-4 text-blue-500 focus:ring-blue-500 border-wheat-300 rounded"
              />
              <label for="saveBillingAddress" class="ml-2 block text-sm text-forest-700">
                {{ $t('checkout.save_billing_address', 'Save billing address to my account') }}
              </label>
            </div>
          </div>
        </template>
      </BillingAddressToggle>
      
      <!-- Submit Buttons -->
      <div class="flex justify-between mt-8">
        <button
          type="button"
          @click="$emit('previous-step')"
          class="inline-flex items-center px-4 py-2 border border-wheat-300 shadow-sm text-sm font-medium rounded-md text-forest-700 bg-white hover:bg-wheat-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-forest-500"
        >
          <Icon name="heroicons:arrow-left" class="w-5 h-5 mr-1" />
          {{ $t('checkout.back') }}
        </button>
        
        <button
          type="submit"
          class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-terracotta-500 hover:bg-terracotta-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-terracotta-500"
          :disabled="loading"
        >
          <span v-if="loading" class="flex items-center">
            <Icon name="heroicons:arrow-path" class="w-5 h-5 mr-2 animate-spin" />
            {{ $t('common.loading') }}
          </span>
          <span v-else class="flex items-center">
            {{ $t('checkout.continue') }}
            <Icon name="heroicons:arrow-right" class="w-5 h-5 ml-1" />
          </span>
        </button>
      </div>
    </form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onBeforeUnmount, computed, watch } from 'vue'
import { useToast, useRuntimeConfig } from '#imports'
import { useAuth } from '~/composables/useAuth'
import { useI18n } from 'vue-i18n'
import { useLocaleStore } from '~/stores/locale'
import BillingAddressToggle from '~/components/BillingAddressToggle.vue'
import { useMedusaService } from '~/composables/useMedusaService'

// Use the auth cookie name directly
const AUTH_COOKIE_NAME = 'handmadein_auth'

interface Address {
  id?: string
  first_name: string
  last_name: string
  company?: string
  address_1: string
  address_2?: string
  city: string
  province?: string
  postal_code: string
  country_code: string
  phone?: string
  is_default_shipping?: boolean
  is_default_billing?: boolean
  address_type?: 'shipping' | 'billing' | 'both'
  metadata?: {
    address_type?: 'shipping' | 'billing' | 'both'
    is_default_shipping?: boolean
    is_default_billing?: boolean
  }
}

// Create validation error state with index signature
interface ValidationErrors {
  email: string;
  first_name: string;
  last_name: string;
  address_1: string;
  city: string;
  province: string;
  postal_code: string;
  country_code: string;
  phone: string;
  password: string;
  confirmPassword: string;
  [key: string]: string; // Add index signature
}

// Get auth token from cookie - same as AddressManagement
const getAuthToken = () => {
  if (typeof document !== 'undefined') {
    console.log('All cookies:', document.cookie)
    const cookies = document.cookie.split(';')
    
    // Look for the correct auth cookie
    const authCookie = cookies.find(cookie => cookie.trim().startsWith(`${AUTH_COOKIE_NAME}=`))
    console.log('Found auth cookie:', authCookie)
    
    if (authCookie) {
      try {
        const authData = JSON.parse(decodeURIComponent(authCookie.split('=')[1]))
        console.log('Parsed auth data:', authData)
        return authData.accessToken
      } catch (e) {
        console.error('Error parsing auth cookie:', e)
      }
    }
  }
  console.log('No auth token found')
  return null
}

// API URL configuration - same as AddressManagement
const getApiUrl = () => {
  // For local development, always use localhost:8787
  return process.env.NODE_ENV === 'production' 
    ? (config.public.apiUrl || 'https://bapi.handmadein.ro')
    : 'https://bapi.handmadein.ro'
}

const validationErrors = reactive<ValidationErrors>({
  email: '',
  first_name: '',
  last_name: '',
  address_1: '',
  city: '',
  province: '',
  postal_code: '',
  country_code: '',
  phone: '',
  password: '',
  confirmPassword: ''
})

// Define props - extend to include checkout state data
interface ShippingAddressProps {
  // Standard address fields
  id?: string
  first_name?: string
  last_name?: string
  company?: string
  address_1?: string
  address_2?: string
  city?: string
  province?: string
  postal_code?: string
  country_code?: string
  phone?: string
  
  // Extended checkout state fields
  sameAsBilling?: boolean
  billing?: any
  selectedShippingAddressId?: string | null
  selectedBillingAddressId?: string | null
  saveAddress?: boolean
}

const props = defineProps<{
  initialData?: ShippingAddressProps
  customerData?: any
}>()

// Update the emits section
const emit = defineEmits<{
  (e: 'update-address', data: any): void
  (e: 'update-customer', data: any): void
  (e: 'next-step'): void
  (e: 'previous-step'): void
}>()

// Setup toast and i18n
const toast = useToast()
const { t } = useI18n()
const { isAuthenticated } = useAuth()
const localeStore = useLocaleStore()
const config = useRuntimeConfig()

// UI state refs
const loading = ref(false)
const saveAddress = ref(false)
const saveBillingAddress = ref(false)
const sameAsBilling = ref(true)
const selectedAddressId = ref<string | null>(null)
const selectedBillingAddressId = ref<string | null>(null)
const customerAddresses = ref<Address[]>([])

// Add email field to data
const email = ref(props.customerData?.email || '')

// Watch email changes with debouncing to prevent excessive API calls
const emailDebounceTimer = ref<NodeJS.Timeout | null>(null)

watch(email, (newEmail) => {
  if (newEmail && !isAuthenticated.value) {
    // Clear existing timer
    if (emailDebounceTimer.value) {
      clearTimeout(emailDebounceTimer.value)
    }
    
    // Set new timer with 500ms debounce
    emailDebounceTimer.value = setTimeout(() => {
      // Emit customer data update after user stops typing
      emit('update-customer', {
        email: newEmail,
        first_name: form.first_name || props.customerData?.first_name || '',
        last_name: form.last_name || props.customerData?.last_name || '',
        phone: form.phone || props.customerData?.phone || ''
      })
    }, 500)
  }
})

// Form state
const form = reactive<Address>({
  first_name: props.initialData?.first_name || '',
  last_name: props.initialData?.last_name || '',
  company: props.initialData?.company || '',
  address_1: props.initialData?.address_1 || '',
  address_2: props.initialData?.address_2 || '',
  city: props.initialData?.city || '',
  province: props.initialData?.province || '',
  postal_code: props.initialData?.postal_code || '',
  country_code: props.initialData?.country_code || '',
  phone: props.initialData?.phone || '',
  is_default_shipping: false,
  is_default_billing: false,
  address_type: 'shipping'
})

// Billing address form
const billingForm = reactive<Address>({
  first_name: '',
  last_name: '',
  company: '',
  address_1: '',
  address_2: '',
  city: '',
  province: '',
  postal_code: '',
  country_code: '',
  phone: '',
  is_default_billing: false,
  address_type: 'billing'
})

// Countries list
const countries = [
  { code: 'ro', name: 'Romania' }
]

// Country regions mapping
const countryRegionsMap = {
  'ro': [
    'Alba', 'Arad', 'Argeș', 'Bacău', 'Bihor', 'Bistrița-Năsăud', 'Botoșani', 'Brașov',
    'Brăila', 'București', 'Buzău', 'Caraș-Severin', 'Călărași', 'Cluj', 'Constanța',
    'Covasna', 'Dâmbovița', 'Dolj', 'Galați', 'Giurgiu', 'Gorj', 'Harghita', 'Hunedoara',
    'Ialomița', 'Iași', 'Ilfov', 'Maramureș', 'Mehedinți', 'Mureș', 'Neamț', 'Olt',
    'Prahova', 'Satu Mare', 'Sălaj', 'Sibiu', 'Suceava', 'Teleorman', 'Timiș', 'Tulcea',
    'Vaslui', 'Vâlcea', 'Vrancea'
  ]
}

// Phone prefixes by country
const phonePrefixes = {
  'ro': '' // Empty prefix for Romania
}

// Get available regions for the current country
const countryRegions = computed(() => {
  const code = form.country_code.toLowerCase();
  return countryRegionsMap[code as keyof typeof countryRegionsMap] || []
})

// Get available regions for the billing country
const billingCountryRegions = computed(() => {
  const code = billingForm.country_code.toLowerCase();
  return countryRegionsMap[code as keyof typeof countryRegionsMap] || []
})

// Get phone prefix for the current country
const phonePrefix = computed(() => {
  const code = form.country_code.toLowerCase();
  return phonePrefixes[code as keyof typeof phonePrefixes] || ''
})

// Get phone prefix for the billing country
const billingPhonePrefix = computed(() => {
  const code = billingForm.country_code.toLowerCase();
  return phonePrefixes[code as keyof typeof phonePrefixes] || ''
})

// Phone without prefix for easier input
const phoneWithoutPrefix = computed({
  get: () => {
    if (form.phone && phonePrefix.value && form.phone.startsWith(phonePrefix.value)) {
      return form.phone.substring(phonePrefix.value.length)
    }
    return form.phone || ''
  },
  set: (value: string) => {
    const prefix = phonePrefix.value
    if (value && !value.startsWith(prefix)) {
      form.phone = `${prefix}${value}`
    } else {
      form.phone = value
    }
  }
})

// Billing phone without prefix for easier input
const billingPhoneWithoutPrefix = computed({
  get: () => {
    if (billingForm.phone && billingPhonePrefix.value && billingForm.phone.startsWith(billingPhonePrefix.value)) {
      return billingForm.phone.substring(billingPhonePrefix.value.length)
    }
    return billingForm.phone || ''
  },
  set: (value: string) => {
    const prefix = billingPhonePrefix.value
    if (value && !value.startsWith(prefix)) {
      billingForm.phone = `${prefix}${value}`
    } else {
      billingForm.phone = value
    }
  }
})

// Watch for country changes to update the phone prefix
watch(() => form.country_code, (newCountry) => {
  if (form.phone) {
    // Remove any existing prefix
    let phoneWithoutAnyPrefix = form.phone
    Object.values(phonePrefixes).forEach(prefix => {
      if (phoneWithoutAnyPrefix.startsWith(prefix)) {
        phoneWithoutAnyPrefix = phoneWithoutAnyPrefix.substring(prefix.length)
      }
    })
    
    // Add the new prefix
    const newPrefix = phonePrefixes[newCountry.toLowerCase() as keyof typeof phonePrefixes] || ''
    form.phone = `${newPrefix}${phoneWithoutAnyPrefix}`
  }
})

// Watch for billing country changes to update the phone prefix
watch(() => billingForm.country_code, (newCountry) => {
  if (billingForm.phone) {
    // Remove any existing prefix
    let phoneWithoutAnyPrefix = billingForm.phone
    Object.values(phonePrefixes).forEach(prefix => {
      if (phoneWithoutAnyPrefix.startsWith(prefix)) {
        phoneWithoutAnyPrefix = phoneWithoutAnyPrefix.substring(prefix.length)
      }
    })
    
    // Add the new prefix
    const newPrefix = phonePrefixes[newCountry.toLowerCase() as keyof typeof phonePrefixes] || ''
    billingForm.phone = `${newPrefix}${phoneWithoutAnyPrefix}`
  }
})

// Watch for changes to sameAsBilling to handle billing form data appropriately
watch(() => sameAsBilling.value, (isSame, wasDefault) => {
  console.log('[DEBUG] sameAsBilling changed from', wasDefault, 'to', isSame)
  
  if (isSame) {
    // When "Same as shipping" is checked, copy shipping data to billing form
    console.log('[DEBUG] Same as billing checked - copying shipping data to billing form')
    billingForm.first_name = form.first_name
    billingForm.last_name = form.last_name
    billingForm.company = form.company
    billingForm.address_1 = form.address_1
    billingForm.address_2 = form.address_2
    billingForm.city = form.city
    billingForm.province = form.province
    billingForm.postal_code = form.postal_code
    billingForm.country_code = form.country_code
    billingForm.phone = form.phone
  } else {
    // When "Same as shipping" is unchecked, try to find default billing address
    console.log('[DEBUG] Separate billing selected')
    
    // Check if billing form is empty (first time unchecking)
    const billingFormIsEmpty = !billingForm.first_name && !billingForm.last_name && !billingForm.address_1
    
    if (billingFormIsEmpty && customerAddresses.value.length > 0) {
      const defaultBillingAddress = customerAddresses.value.find(addr =>
        addr.is_default_billing || addr.metadata?.is_default_billing
      )
      
      if (defaultBillingAddress) {
        console.log('[DEBUG] Using default billing address')
        selectBillingAddress(defaultBillingAddress)
      } else {
        // If no default billing address, initialize with shipping data but allow editing
        console.log('[DEBUG] No default billing address found, initializing with shipping data for editing')
        billingForm.first_name = form.first_name
        billingForm.last_name = form.last_name
        billingForm.company = form.company
        billingForm.address_1 = form.address_1
        billingForm.address_2 = form.address_2
        billingForm.city = form.city
        billingForm.province = form.province
        billingForm.postal_code = form.postal_code
        billingForm.country_code = form.country_code
        billingForm.phone = form.phone
      }
    } else if (billingFormIsEmpty) {
      // No saved addresses, initialize with shipping data
      console.log('[DEBUG] No saved addresses, initializing billing form with shipping data')
      billingForm.first_name = form.first_name
      billingForm.last_name = form.last_name
      billingForm.company = form.company
      billingForm.address_1 = form.address_1
      billingForm.address_2 = form.address_2
      billingForm.city = form.city
      billingForm.province = form.province
      billingForm.postal_code = form.postal_code
      billingForm.country_code = form.country_code
      billingForm.phone = form.phone
    }
    // If billing form already has data, preserve it (user has edited it)
    console.log('[DEBUG] Billing form preserved/updated. Current billing data:', JSON.stringify(billingForm, null, 2))
  }
}, { immediate: false })

// Prevent watcher conflicts during initialization
const isInitializing = ref(true)

// Watch authentication state changes to load addresses when user signs in
watch(() => isAuthenticated.value, async (newAuthState, oldAuthState) => {
  // Only trigger when user changes from unauthenticated to authenticated
  if (newAuthState && !oldAuthState) {
    console.log('User signed in - loading customer addresses...')
    try {
      await loadCustomerAddresses()
      
      // Show a brief success message
      toast.add({
        title: 'Success',
        description: 'Your saved addresses have been loaded',
        color: 'green'
      })
    } catch (err) {
      console.error('Error loading addresses after sign in:', err)
      // Don't show error toast as this is not critical for checkout flow
    }
  } else if (!newAuthState && oldAuthState) {
    // User signed out - clear addresses
    console.log('User signed out - clearing customer addresses...')
    customerAddresses.value = []
    selectedAddressId.value = null
    selectedBillingAddressId.value = null
  }
})

// Computed property for shipping addresses
const shippingAddresses = computed(() => {
  return customerAddresses.value.filter(address => {
    const addressType = address.address_type || address.metadata?.address_type;
    return addressType === 'shipping' || addressType === 'both' || address.is_default_shipping;
  });
});

// Computed property for billing addresses
const billingAddresses = computed(() => {
  return customerAddresses.value.filter(address => {
    const addressType = address.address_type || address.metadata?.address_type;
    return addressType === 'billing' || addressType === 'both' || address.is_default_billing;
  });
});

// Get the medusa service
const medusaService = useMedusaService()

// Fetch addresses from Cloudflare backend - same as AddressManagement
const loadCustomerAddresses = async () => {
  if (!isAuthenticated.value) return

  console.log('loadCustomerAddresses called')
  loading.value = true
  try {
    const token = getAuthToken()
    console.log('Auth token:', token ? 'Found' : 'Not found')
    
    if (!token) {
      throw new Error('No authentication token found')
    }

    const apiUrl = getApiUrl()
    console.log('API URL:', apiUrl)
    
    // Add cache-busting parameter to ensure fresh data
    const timestamp = Date.now()
    const url = `${apiUrl}/store/customers/me/addresses?_t=${timestamp}`
    console.log('Making request to:', url)
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    })

    console.log('Response status:', response.status)
    console.log('Response ok:', response.ok)

    if (!response.ok) {
      const errorText = await response.text()
      console.error('Response error:', errorText)
      throw new Error(`Failed to fetch addresses: ${response.statusText}`)
    }

    const data = await response.json()
    console.log('Response data:', data)
    
    if (data.success) {
      // Parse metadata if it's a string
      customerAddresses.value = (data.data || []).map((addr: any) => ({
        ...addr,
        metadata: typeof addr.metadata === 'string' 
          ? JSON.parse(addr.metadata || '{}')
          : (addr.metadata || {})
      }))
      console.log('Addresses loaded:', customerAddresses.value.length)
      console.log('[DEBUG] Loaded addresses:', customerAddresses.value.map(addr => ({
        id: addr.id,
        first_name: addr.first_name,
        last_name: addr.last_name,
        address_1: addr.address_1,
        address_type: addr.address_type,
        created_at: (addr as any).created_at,
        updated_at: (addr as any).updated_at
      })))
      
      // Find and select default shipping address if exists
      const defaultShippingAddress = customerAddresses.value.find(addr => 
        addr.is_default_shipping || addr.metadata?.is_default_shipping
      )
      
      if (defaultShippingAddress) {
        console.log('Found default shipping address, selecting it automatically')
        selectSavedAddress(defaultShippingAddress)
      }
      
      // Find and select default billing address if using separate billing
      if (!sameAsBilling.value) {
        const defaultBillingAddress = customerAddresses.value.find(addr => 
          addr.is_default_billing || addr.metadata?.is_default_billing
        )
        
        if (defaultBillingAddress) {
          console.log('Found default billing address, selecting it automatically')
          selectBillingAddress(defaultBillingAddress)
        }
      }
    } else {
      throw new Error(data.error || 'Failed to fetch addresses')
    }
  } catch (err: any) {
    console.error('Error fetching addresses:', err)
    toast.add({
      title: 'Error',
      description: err.message || 'Failed to load addresses',
      color: 'red'
    })
  } finally {
    loading.value = false
  }
}

// Select a saved address
const selectSavedAddress = (address: Address) => {
  selectedAddressId.value = address.id || null
  
  // Copy address data to form
  form.first_name = address.first_name
  form.last_name = address.last_name
  form.company = address.company || ''
  form.address_1 = address.address_1
  form.address_2 = address.address_2 || ''
  form.city = address.city
  form.province = address.province || ''
  form.postal_code = address.postal_code
  form.country_code = address.country_code
  
  // Handle phone with prefix correctly
  if (address.phone) {
    // Get the current country's prefix
    const prefix = phonePrefixes[address.country_code.toLowerCase() as keyof typeof phonePrefixes] || ''
    
    // Remove the prefix if it's already there to avoid duplication
    if (prefix && address.phone.startsWith(prefix)) {
      form.phone = address.phone
    } else {
      // Add the prefix if it's not already there
      form.phone = prefix ? `${prefix}${address.phone}` : address.phone
    }
  } else {
    form.phone = ''
  }
  
  // Reset validation errors
  Object.keys(validationErrors).forEach(key => {
    validationErrors[key] = ''
  })
}

// Select a billing address
const selectBillingAddress = (address: Address) => {
  selectedBillingAddressId.value = address.id || null
  
  // Copy address data to billing form
  billingForm.first_name = address.first_name
  billingForm.last_name = address.last_name
  billingForm.company = address.company || ''
  billingForm.address_1 = address.address_1
  billingForm.address_2 = address.address_2 || ''
  billingForm.city = address.city
  billingForm.province = address.province || ''
  billingForm.postal_code = address.postal_code
  billingForm.country_code = address.country_code
  
  // Handle phone with prefix correctly
  if (address.phone) {
    // For billing we always use Romanian prefix
    const prefix = phonePrefixes['ro'] || ''
    
    // Check for any existing prefix and remove it
    let phoneWithoutPrefix = address.phone
    Object.values(phonePrefixes).forEach(p => {
      if (phoneWithoutPrefix.startsWith(p)) {
        phoneWithoutPrefix = phoneWithoutPrefix.substring(p.length)
      }
    })
    
    // Add the Romanian prefix (which is empty)
    billingForm.phone = `${prefix}${phoneWithoutPrefix}`
  } else {
    billingForm.phone = ''
  }
}

// Get country name from code
const getCountryName = (code: string): string => {
  if (!code) return ''
  
  // First try to get the localized country name from translations
  const countryKey = `countries.${code.toLowerCase()}`
  if (t(countryKey) !== countryKey) {
    return t(countryKey)
  }
  
  // Fallback to our static list
  const country = countries.find(c => c.code.toLowerCase() === code.toLowerCase())
  
  return country ? country.name : code
}

// Field validation
const validateField = (field: string): boolean => {
  validationErrors[field as keyof typeof validationErrors] = ''
  
  switch (field) {
    case 'email':
      if (!email.value) {
        validationErrors.email = t('checkout.email_required')
        return false
      }
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(email.value)) {
        validationErrors.email = t('checkout.email_invalid')
        return false
      }
      break
    case 'first_name':
      validationErrors.first_name = form.first_name ? '' : t('validation.required', { field: t('checkout.first_name') })
      break
    case 'last_name':
      validationErrors.last_name = form.last_name ? '' : t('validation.required', { field: t('checkout.last_name') })
      break
    case 'address_1':
      validationErrors.address_1 = form.address_1 ? '' : t('validation.required', { field: t('checkout.address_line_1') })
      break
    case 'city':
      validationErrors.city = form.city ? '' : t('validation.required', { field: t('checkout.city') })
      break
    case 'province':
      validationErrors.province = form.province ? '' : t('validation.required', { field: t('checkout.province') })
      break
    case 'postal_code':
      validationErrors.postal_code = form.postal_code ? '' : t('validation.required', { field: t('checkout.postal_code') })
      break
    case 'country_code':
      validationErrors.country_code = form.country_code ? '' : t('validation.required', { field: t('checkout.country') })
      break
    case 'password':
      if (createAccount.value) {
        if (!password.value) {
          validationErrors.password = t('validation.required', { field: t('account.password') })
          return false
        }
        if (password.value.length < 8) {
          validationErrors.password = t('validation.min_length', { field: t('account.password'), length: 8 })
          return false
        }
      }
      break
    case 'confirmPassword':
      if (createAccount.value) {
        if (!confirmPassword.value) {
          validationErrors.confirmPassword = t('validation.required', { field: t('account.confirm_password') })
          return false
        }
        if (password.value !== confirmPassword.value) {
          validationErrors.confirmPassword = t('validation.passwords_dont_match')
          return false
        }
      }
      break
  }
  
  return validationErrors[field as keyof typeof validationErrors] === ''
}

// Add account creation related refs
const createAccount = ref(false)
const password = ref('')
const confirmPassword = ref('')

// Helper function to check if addresses are identical
const areAddressesIdentical = (addr1: any, addr2: any, checkType: boolean = true): boolean => {
  // Core address fields that define uniqueness
  const coreFields = [
    'first_name', 'last_name', 'address_1', 'address_line_1',
    'city', 'postal_code', 'country_code'
  ]
  
  // Optional fields that should also be compared
  const optionalFields = ['company', 'address_2', 'address_line_2', 'province', 'state', 'phone']
  
  // Check core fields (required for identity)
  const coreMatch = coreFields.every(field => {
    // Handle both address_1/address_line_1 naming variations
    let val1 = addr1[field] || addr1[field === 'address_1' ? 'address_line_1' : field === 'address_line_1' ? 'address_1' : field] || ''
    let val2 = addr2[field] || addr2[field === 'address_1' ? 'address_line_1' : field === 'address_line_1' ? 'address_1' : field] || ''
    
    val1 = val1.toString().toLowerCase().trim()
    val2 = val2.toString().toLowerCase().trim()
    
    return val1 === val2
  })
  
  if (!coreMatch) return false
  
  // Check optional fields
  const optionalMatch = optionalFields.every(field => {
    let val1 = addr1[field] || ''
    let val2 = addr2[field] || ''
    
    val1 = val1.toString().toLowerCase().trim()
    val2 = val2.toString().toLowerCase().trim()
    
    return val1 === val2
  })
  
  if (!optionalMatch) return false
  
  // If we're checking types, ensure they're compatible
  if (checkType) {
    const type1 = addr1.metadata?.address_type || addr1.type || 'both'
    const type2 = addr2.metadata?.address_type || addr2.type || 'both'
    
    // Types are compatible if:
    // 1. They're exactly the same
    // 2. One is 'both' (meaning it can serve as either type)
    return type1 === type2 || type1 === 'both' || type2 === 'both'
  }
  
  return true
}

// Save address (create or update) - same as AddressManagement
const saveAddressToAccount = async (addressForm = form, addressType = 'shipping') => {
  if (!isAuthenticated.value) {
    console.warn('User not authenticated, cannot save address')
    return
  }

  loading.value = true
  try {
    const token = getAuthToken()
    if (!token) {
      throw new Error('No authentication token found')
    }

    const apiUrl = getApiUrl()
    console.log(`[DEBUG] saveAddressToAccount called with type: ${addressType}`)
    console.log(`[DEBUG] addressForm data:`, JSON.stringify(addressForm, null, 2))
    
    const addressData = {
      first_name: addressForm.first_name,
      last_name: addressForm.last_name,
      company: addressForm.company || '',
      address_1: addressForm.address_1,
      address_2: addressForm.address_2 || '',
      city: addressForm.city,
      province: addressForm.province || '',
      postal_code: addressForm.postal_code,
      country_code: addressForm.country_code,
      phone: addressForm.phone || '',
      is_default_shipping: false,
      is_default_billing: false,
      address_type: addressType,
      metadata: {
        address_type: addressType
      }
    }
    
    console.log(`[DEBUG] Final addressData being sent to backend:`, JSON.stringify(addressData, null, 2))
    
    const method = 'POST'
    const url = `${apiUrl}/store/customers/me/addresses`

    const response = await fetch(url, {
      method,
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(addressData)
    })

    if (!response.ok) {
      throw new Error(`Failed to save address: ${response.statusText}`)
    }

    const data = await response.json()
    console.log('[DEBUG] Save address response:', data)
    
    if (data.success) {
      console.log('[DEBUG] Address saved successfully, waiting before reload...')
      
      // Add a small delay to ensure backend consistency before reloading
      await new Promise(resolve => setTimeout(resolve, 500))
      
      console.log('[DEBUG] Reloading customer addresses...')
      await loadCustomerAddresses()
      
      toast.add({
        title: 'Success',
        description: 'Address saved successfully',
        color: 'green'
      })
      return data.data
    } else {
      throw new Error(data.error || 'Failed to save address')
    }
  } catch (err: any) {
    console.error('Error saving address:', err)
    toast.add({
      title: 'Error',
      description: err.message || 'Failed to save address',
      color: 'red'
    })
    throw err
  } finally {
    loading.value = false
  }
}

// Update existing address - same as AddressManagement
const updateSavedAddress = async () => {
  if (!selectedAddressId.value) return
  
  loading.value = true
  try {
    const token = getAuthToken()
    if (!token) {
      throw new Error('No authentication token found')
    }

    // Get current address to preserve important fields
    const currentAddress = customerAddresses.value.find(addr => addr.id === selectedAddressId.value)
    if (!currentAddress) {
      throw new Error('Address not found')
    }

    // Preserve existing metadata and update address_type if needed
    let existingMetadata: any = {}
    try {
      existingMetadata = typeof currentAddress.metadata === 'string' 
        ? JSON.parse(currentAddress.metadata || '{}')
        : (currentAddress.metadata || {})
    } catch (e) {
      console.warn('Error parsing existing metadata:', e)
      existingMetadata = {}
    }

    // Update the metadata with current address_type
    const updatedMetadata = {
      ...existingMetadata,
      address_type: existingMetadata.address_type || currentAddress.address_type || 'shipping'
    }

    const apiUrl = getApiUrl()
    const response = await fetch(`${apiUrl}/store/customers/me/addresses/${selectedAddressId.value}`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        // Send all current address data to preserve it
        first_name: form.first_name,
        last_name: form.last_name,
        company: form.company || '',
        address_1: form.address_1,
        address_2: form.address_2 || '',
        city: form.city,
        province: form.province || '',
        postal_code: form.postal_code,
        country_code: form.country_code,
        phone: form.phone || '',
        // Preserve existing default status
        is_default_shipping: Boolean(currentAddress.is_default_shipping),
        is_default_billing: Boolean(currentAddress.is_default_billing),
        // Preserve metadata with address_type
        metadata: updatedMetadata
      })
    })

    if (!response.ok) {
      throw new Error(`Failed to update address: ${response.statusText}`)
    }

    const data = await response.json()
    if (data.success) {
      await loadCustomerAddresses()
      toast.add({
        title: 'Success',
        description: 'Address updated successfully',
        color: 'green'
      })
      return data.data
    } else {
      throw new Error(data.error || 'Failed to update address')
    }
  } catch (err: any) {
    console.error('Error updating address:', err)
    toast.add({
      title: 'Error',
      description: err.message || 'Failed to update address',
      color: 'red'
    })
    throw err
  } finally {
    loading.value = false
  }
}

// Form submission
const submitForm = async () => {
  // Validate all fields
  if (!isAuthenticated.value) {
    validateField('email')
    
    // Validate password fields if create account is checked
    if (createAccount.value) {
      validateField('password')
      validateField('confirmPassword')
    }
  }
  
  validateField('first_name')
  validateField('last_name')
  validateField('address_1')
  validateField('city')
  validateField('province')
  validateField('postal_code')
  validateField('country_code')
  
  // Check if there are any validation errors
  if (Object.values(validationErrors).some(error => error)) {
    toast.add({
      title: t('checkout.validation_error'),
      description: t('checkout.please_fix_errors'),
      color: 'red'
    })
    return
  }
  
  loading.value = true
  
  try {
    console.log('[DEBUG] Form submission started')
    console.log('[DEBUG] sameAsBilling:', sameAsBilling.value)
    console.log('[DEBUG] saveAddress:', saveAddress.value)
    console.log('[DEBUG] saveBillingAddress:', saveBillingAddress.value)
    console.log('[DEBUG] Current shipping form state:', JSON.stringify(form, null, 2))
    console.log('[DEBUG] Current billing form state:', JSON.stringify(billingForm, null, 2))
    
    // Process address data
    console.log('[DEBUG] About to create addressData with sameAsBilling:', sameAsBilling.value)
    console.log('[DEBUG] billingForm has data:', Object.values(billingForm).some(value => !!value))
    
    const addressData = {
      shipping: {
        first_name: form.first_name,
        last_name: form.last_name,
        company: form.company,
        address_1: form.address_1,
        address_2: form.address_2,
        city: form.city,
        province: form.province,
        postal_code: form.postal_code,
        country_code: form.country_code,
        phone: form.phone,
      },
      // Always include billing address, either same as shipping or different
      billing: sameAsBilling.value ? {
        first_name: form.first_name,
        last_name: form.last_name,
        company: form.company,
        address_1: form.address_1,
        address_2: form.address_2,
        city: form.city,
        province: form.province,
        postal_code: form.postal_code,
        country_code: form.country_code,
        phone: form.phone,
      } : {
        first_name: billingForm.first_name,
        last_name: billingForm.last_name,
        company: billingForm.company,
        address_1: billingForm.address_1,
        address_2: billingForm.address_2,
        city: billingForm.city,
        province: billingForm.province,
        postal_code: billingForm.postal_code,
        country_code: billingForm.country_code,
        phone: billingForm.phone,
      },
      sameAsBilling: sameAsBilling.value,
      selectedShippingAddressId: selectedAddressId.value,
      selectedBillingAddressId: selectedBillingAddressId.value,
      saveAddress: saveAddress.value,
      saveBillingAddress: saveBillingAddress.value
    }

    console.log('[DEBUG] Final addressData being emitted:', JSON.stringify(addressData, null, 2))
    console.log('[DEBUG] Billing address is same as shipping:', sameAsBilling.value)
    console.log('[DEBUG] Are shipping and billing identical?:', JSON.stringify(addressData.shipping) === JSON.stringify(addressData.billing))
    
    // If user wants to save addresses to their account, do it now
    if (isAuthenticated.value) {
      try {
        // Save shipping address if checkbox is checked
        if (saveAddress.value) {
          console.log('Save Address checkbox checked - creating shipping address...')
          await saveAddressToAccount(form, 'shipping')
        }
        
        // Save billing address if checkbox is checked AND billing is different from shipping
        if (saveBillingAddress.value && !sameAsBilling.value) {
          console.log('Save Billing Address checkbox checked - creating billing address...')
          console.log('[DEBUG] Billing form data being saved:', JSON.stringify(billingForm, null, 2))
          console.log('[DEBUG] Shipping form data for comparison:', JSON.stringify(form, null, 2))
          await saveAddressToAccount(billingForm, 'billing')
        }
        
      } catch (err) {
        console.error('Failed to save address to account:', err)
        // Don't block checkout if address saving fails
        toast.add({
          title: 'Warning',
          description: 'Address could not be saved to your account, but checkout will continue',
          color: 'yellow'
        })
      }
    } else if (isAuthenticated.value && selectedAddressId.value && !saveAddress.value) {
      // Only update an existing address if:
      // 1. An address was selected
      // 2. "Save Address" checkbox is NOT checked (user is just using existing address)
      // 3. The address was actually modified
      const originalAddress = customerAddresses.value.find(addr => addr.id === selectedAddressId.value)
      if (originalAddress) {
        const isModified = 
          originalAddress.first_name !== form.first_name ||
          originalAddress.last_name !== form.last_name ||
          originalAddress.company !== (form.company || '') ||
          originalAddress.address_1 !== form.address_1 ||
          (originalAddress.address_2 || '') !== (form.address_2 || '') ||
          originalAddress.city !== form.city ||
          (originalAddress.province || '') !== (form.province || '') ||
          originalAddress.postal_code !== form.postal_code ||
          originalAddress.country_code !== form.country_code ||
          (originalAddress.phone || '') !== (form.phone || '')
          
        if (isModified) {
          try {
            console.log('Existing address was modified without Save Address checkbox - updating existing address...')
            await updateSavedAddress()
          } catch (err) {
            console.error('Failed to update address:', err)
            toast.add({
              title: 'Warning',
              description: 'Address changes could not be saved, but checkout will continue',
              color: 'yellow'
            })
          }
        }
      }
    }
    
    // Emit the address update
    emit('update-address', addressData)
    
    // Emit customer data update with account creation info
    emit('update-customer', {
      email: isAuthenticated.value ? props.customerData?.email || '' : email.value,
      first_name: form.first_name,
      last_name: form.last_name,
      phone: form.phone,
      createAccount: createAccount.value,
      password: createAccount.value ? password.value : undefined
    })
    
    // If creating an account, store needed info in localStorage for verification page
    if (createAccount.value && password.value) {
      localStorage.setItem('userEmail', email.value)
      localStorage.setItem('userPassword', password.value)
      localStorage.setItem('userFirstName', form.first_name)
      localStorage.setItem('userLastName', form.last_name)
    }
    
    // Note: Address saving is now handled in the checkout page
    // to ensure proper integration with the checkout flow
    
    // Emit event to move to next step
    emit('next-step')
  } catch (error: any) {
    toast.add({
      title: t('checkout.error'),
      description: error.message || t('checkout.address_save_error'),
      color: 'red'
    })
  } finally {
    loading.value = false
  }
}

// Initialize component
onMounted(async () => {
  // Initialize form data from props
  if (props.initialData) {
    // Fill the form with initial data if provided
    form.first_name = props.initialData.first_name || ''
    form.last_name = props.initialData.last_name || ''
    form.company = props.initialData.company || ''
    form.address_1 = props.initialData.address_1 || ''
    form.address_2 = props.initialData.address_2 || ''
    form.city = props.initialData.city || ''
    form.province = props.initialData.province || ''
    form.postal_code = props.initialData.postal_code || ''
    form.country_code = props.initialData.country_code || 'ro'
    form.phone = props.initialData.phone || ''
    
    // Restore sameAsBilling state and billing data if provided
    if (props.initialData.sameAsBilling !== undefined) {
      console.log('[DEBUG] Restoring sameAsBilling state:', props.initialData.sameAsBilling)
      sameAsBilling.value = props.initialData.sameAsBilling
    }
    
    // Restore billing form data if provided
    if (props.initialData.billing) {
      console.log('[DEBUG] Restoring billing data:', props.initialData.billing)
      billingForm.first_name = props.initialData.billing.first_name || ''
      billingForm.last_name = props.initialData.billing.last_name || ''
      billingForm.company = props.initialData.billing.company || ''
      billingForm.address_1 = props.initialData.billing.address_1 || ''
      billingForm.address_2 = props.initialData.billing.address_2 || ''
      billingForm.city = props.initialData.billing.city || ''
      billingForm.province = props.initialData.billing.province || ''
      billingForm.postal_code = props.initialData.billing.postal_code || ''
      billingForm.country_code = props.initialData.billing.country_code || 'ro'
      billingForm.phone = props.initialData.billing.phone || ''
    }
    
    // Restore selected address IDs if provided
    if (props.initialData.selectedShippingAddressId) {
      selectedAddressId.value = props.initialData.selectedShippingAddressId
    }
    if (props.initialData.selectedBillingAddressId) {
      selectedBillingAddressId.value = props.initialData.selectedBillingAddressId
    }
    
    // Restore save address flags if provided
    if (props.initialData.saveAddress !== undefined) {
      saveAddress.value = props.initialData.saveAddress
    }
  }
  
  // Initialize customer data
  if (props.customerData) {
    // Only set email if user is not authenticated
    if (!isAuthenticated.value) {
      email.value = props.customerData.email || ''
    }
    
    // If no address data was provided but customer data exists, use it for the form
    if (!props.initialData) {
      form.first_name = props.customerData.first_name || ''
      form.last_name = props.customerData.last_name || ''
      form.phone = props.customerData.phone || ''
    }
  }
  
  // Only set default country if not already set by restoration
  if (!billingForm.country_code) {
    billingForm.country_code = 'ro'
  }
  
  // Fetch saved addresses if authenticated
  if (isAuthenticated.value) {
    await loadCustomerAddresses()
    
    // If no address was auto-selected from loadCustomerAddresses and we have initial data,
    // try to match it with saved addresses to select the correct one
    if (!selectedAddressId.value && props.initialData && props.initialData.id) {
      const matchingAddress = customerAddresses.value.find(addr => addr.id === props.initialData?.id)
      if (matchingAddress) {
        selectSavedAddress(matchingAddress)
      }
    }
  }
  
  // Mark initialization as complete
  isInitializing.value = false
  console.log('[DEBUG] ShippingAddress component initialization complete')
})

// Clean up debounce timer on unmount
onBeforeUnmount(() => {
  if (emailDebounceTimer.value) {
    clearTimeout(emailDebounceTimer.value)
  }
})

// Define a defaultCountry computed property
const defaultCountry = computed(() => {
  // Get from locale if available, else use 'ro' as default
  return 'ro'
})
</script> 
